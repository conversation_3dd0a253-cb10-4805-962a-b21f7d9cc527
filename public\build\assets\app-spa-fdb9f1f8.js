import{o as d,f as p,g as e,s as $r,u as k,h as Sr,i as Qe,n as Pt,j as ge,k as Mr,l as N,m as We,p as Wt,r as L,w as Me,q as Ze,t as K,v as R,F as ue,x as fe,y as Ge,z as A,A as ce,B as Ye,C as Ne,D as z,E as J,T as gt,G as _o,H as ks,I as Ce,J as ve,K as Re,L as Kt,M as nt,N as Q,O as Rs,P as Ie,Q as wt,R as ze,S as Ar,U as jr,V as Er,_ as ae,W as Ir,X as Cs,Y as ko,b as ke,d as pt,e as Co,c as Tr,a as Br}from"./Modal.vue_vue_type_script_setup_true_lang-96a11b6a.js";function Pr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"})])}function Rr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 12.75 6 6 9-13.5"})])}function $o(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"})])}function So(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 19.5 8.25 12l7.5-7.5"})])}function Mo(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])}function Nr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"})])}function Lr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])}function Ns(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function Ls(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function Yt(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"})])}function Xt(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function $t(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])}function Dr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"})])}function zr(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"})])}function Or(t,s){return d(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ot=typeof document<"u";function Ao(t){return typeof t=="object"||"displayName"in t||"props"in t||"__vccOpts"in t}function Vr(t){return t.__esModule||t[Symbol.toStringTag]==="Module"||t.default&&Ao(t.default)}const he=Object.assign;function Qt(t,s){const o={};for(const r in s){const n=s[r];o[r]=Oe(n)?n.map(t):t(n)}return o}const ht=()=>{},Oe=Array.isArray,jo=/#/g,Fr=/&/g,qr=/\//g,Ur=/=/g,Hr=/\?/g,Eo=/\+/g,Wr=/%5B/g,Kr=/%5D/g,Io=/%5E/g,Yr=/%60/g,To=/%7B/g,Xr=/%7C/g,Bo=/%7D/g,Qr=/%20/g;function $s(t){return encodeURI(""+t).replace(Xr,"|").replace(Wr,"[").replace(Kr,"]")}function Gr(t){return $s(t).replace(To,"{").replace(Bo,"}").replace(Io,"^")}function ds(t){return $s(t).replace(Eo,"%2B").replace(Qr,"+").replace(jo,"%23").replace(Fr,"%26").replace(Yr,"`").replace(To,"{").replace(Bo,"}").replace(Io,"^")}function Jr(t){return ds(t).replace(Ur,"%3D")}function Zr(t){return $s(t).replace(jo,"%23").replace(Hr,"%3F")}function en(t){return t==null?"":Zr(t).replace(qr,"%2F")}function yt(t){try{return decodeURIComponent(""+t)}catch{}return""+t}const tn=/\/$/,sn=t=>t.replace(tn,"");function Gt(t,s,o="/"){let r,n={},a="",u="";const h=s.indexOf("#");let c=s.indexOf("?");return h<c&&h>=0&&(c=-1),c>-1&&(r=s.slice(0,c),a=s.slice(c+1,h>-1?h:s.length),n=t(a)),h>-1&&(r=r||s.slice(0,h),u=s.slice(h,s.length)),r=an(r??s,o),{fullPath:r+(a&&"?")+a+u,path:r,query:n,hash:yt(u)}}function on(t,s){const o=s.query?t(s.query):"";return s.path+(o&&"?")+o+(s.hash||"")}function Ds(t,s){return!s||!t.toLowerCase().startsWith(s.toLowerCase())?t:t.slice(s.length)||"/"}function rn(t,s,o){const r=s.matched.length-1,n=o.matched.length-1;return r>-1&&r===n&&at(s.matched[r],o.matched[n])&&Po(s.params,o.params)&&t(s.query)===t(o.query)&&s.hash===o.hash}function at(t,s){return(t.aliasOf||t)===(s.aliasOf||s)}function Po(t,s){if(Object.keys(t).length!==Object.keys(s).length)return!1;for(const o in t)if(!nn(t[o],s[o]))return!1;return!0}function nn(t,s){return Oe(t)?zs(t,s):Oe(s)?zs(s,t):t===s}function zs(t,s){return Oe(s)?t.length===s.length&&t.every((o,r)=>o===s[r]):t.length===1&&t[0]===s}function an(t,s){if(t.startsWith("/"))return t;if(!t)return s;const o=s.split("/"),r=t.split("/"),n=r[r.length-1];(n===".."||n===".")&&r.push("");let a=o.length-1,u,h;for(u=0;u<r.length;u++)if(h=r[u],h!==".")if(h==="..")a>1&&a--;else break;return o.slice(0,a).join("/")+"/"+r.slice(u).join("/")}const He={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var bt;(function(t){t.pop="pop",t.push="push"})(bt||(bt={}));var vt;(function(t){t.back="back",t.forward="forward",t.unknown=""})(vt||(vt={}));function ln(t){if(!t)if(ot){const s=document.querySelector("base");t=s&&s.getAttribute("href")||"/",t=t.replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return t[0]!=="/"&&t[0]!=="#"&&(t="/"+t),sn(t)}const dn=/^[^#]+#/;function un(t,s){return t.replace(dn,"#")+s}function cn(t,s){const o=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{behavior:s.behavior,left:r.left-o.left-(s.left||0),top:r.top-o.top-(s.top||0)}}const Rt=()=>({left:window.scrollX,top:window.scrollY});function mn(t){let s;if("el"in t){const o=t.el,r=typeof o=="string"&&o.startsWith("#"),n=typeof o=="string"?r?document.getElementById(o.slice(1)):document.querySelector(o):o;if(!n)return;s=cn(n,t)}else s=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(s):window.scrollTo(s.left!=null?s.left:window.scrollX,s.top!=null?s.top:window.scrollY)}function Os(t,s){return(history.state?history.state.position-s:-1)+t}const us=new Map;function fn(t,s){us.set(t,s)}function pn(t){const s=us.get(t);return us.delete(t),s}let gn=()=>location.protocol+"//"+location.host;function Ro(t,s){const{pathname:o,search:r,hash:n}=s,a=t.indexOf("#");if(a>-1){let h=n.includes(t.slice(a))?t.slice(a).length:1,c=n.slice(h);return c[0]!=="/"&&(c="/"+c),Ds(c,"")}return Ds(o,t)+r+n}function hn(t,s,o,r){let n=[],a=[],u=null;const h=({state:b})=>{const v=Ro(t,location),x=o.value,g=s.value;let S=0;if(b){if(o.value=v,s.value=b,u&&u===x){u=null;return}S=g?b.position-g.position:0}else r(v);n.forEach(P=>{P(o.value,x,{delta:S,type:bt.pop,direction:S?S>0?vt.forward:vt.back:vt.unknown})})};function c(){u=o.value}function i(b){n.push(b);const v=()=>{const x=n.indexOf(b);x>-1&&n.splice(x,1)};return a.push(v),v}function l(){const{history:b}=window;b.state&&b.replaceState(he({},b.state,{scroll:Rt()}),"")}function f(){for(const b of a)b();a=[],window.removeEventListener("popstate",h),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",h),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:c,listen:i,destroy:f}}function Vs(t,s,o,r=!1,n=!1){return{back:t,current:s,forward:o,replaced:r,position:window.history.length,scroll:n?Rt():null}}function vn(t){const{history:s,location:o}=window,r={value:Ro(t,o)},n={value:s.state};n.value||a(r.value,{back:null,current:r.value,forward:null,position:s.length-1,replaced:!0,scroll:null},!0);function a(c,i,l){const f=t.indexOf("#"),b=f>-1?(o.host&&document.querySelector("base")?t:t.slice(f))+c:gn()+t+c;try{s[l?"replaceState":"pushState"](i,"",b),n.value=i}catch(v){console.error(v),o[l?"replace":"assign"](b)}}function u(c,i){const l=he({},s.state,Vs(n.value.back,c,n.value.forward,!0),i,{position:n.value.position});a(c,l,!0),r.value=c}function h(c,i){const l=he({},n.value,s.state,{forward:c,scroll:Rt()});a(l.current,l,!0);const f=he({},Vs(r.value,c,null),{position:l.position+1},i);a(c,f,!1),r.value=c}return{location:r,state:n,push:h,replace:u}}function yn(t){t=ln(t);const s=vn(t),o=hn(t,s.state,s.location,s.replace);function r(a,u=!0){u||o.pauseListeners(),history.go(a)}const n=he({location:"",base:t,go:r,createHref:un.bind(null,t)},s,o);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>s.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>s.state.value}),n}function bn(t){return typeof t=="string"||t&&typeof t=="object"}function No(t){return typeof t=="string"||typeof t=="symbol"}const Lo=Symbol("");var Fs;(function(t){t[t.aborted=4]="aborted",t[t.cancelled=8]="cancelled",t[t.duplicated=16]="duplicated"})(Fs||(Fs={}));function lt(t,s){return he(new Error,{type:t,[Lo]:!0},s)}function qe(t,s){return t instanceof Error&&Lo in t&&(s==null||!!(t.type&s))}const qs="[^/]+?",xn={sensitive:!1,strict:!1,start:!0,end:!0},wn=/[.+*?^${}()[\]/\\]/g;function _n(t,s){const o=he({},xn,s),r=[];let n=o.start?"^":"";const a=[];for(const i of t){const l=i.length?[]:[90];o.strict&&!i.length&&(n+="/");for(let f=0;f<i.length;f++){const b=i[f];let v=40+(o.sensitive?.25:0);if(b.type===0)f||(n+="/"),n+=b.value.replace(wn,"\\$&"),v+=40;else if(b.type===1){const{value:x,repeatable:g,optional:S,regexp:P}=b;a.push({name:x,repeatable:g,optional:S});const j=P||qs;if(j!==qs){v+=10;try{new RegExp(`(${j})`)}catch(_){throw new Error(`Invalid custom RegExp for param "${x}" (${j}): `+_.message)}}let w=g?`((?:${j})(?:/(?:${j}))*)`:`(${j})`;f||(w=S&&i.length<2?`(?:/${w})`:"/"+w),S&&(w+="?"),n+=w,v+=20,S&&(v+=-8),g&&(v+=-20),j===".*"&&(v+=-50)}l.push(v)}r.push(l)}if(o.strict&&o.end){const i=r.length-1;r[i][r[i].length-1]+=.7000000000000001}o.strict||(n+="/?"),o.end?n+="$":o.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,o.sensitive?"":"i");function h(i){const l=i.match(u),f={};if(!l)return null;for(let b=1;b<l.length;b++){const v=l[b]||"",x=a[b-1];f[x.name]=v&&x.repeatable?v.split("/"):v}return f}function c(i){let l="",f=!1;for(const b of t){(!f||!l.endsWith("/"))&&(l+="/"),f=!1;for(const v of b)if(v.type===0)l+=v.value;else if(v.type===1){const{value:x,repeatable:g,optional:S}=v,P=x in i?i[x]:"";if(Oe(P)&&!g)throw new Error(`Provided param "${x}" is an array but it is not repeatable (* or + modifiers)`);const j=Oe(P)?P.join("/"):P;if(!j)if(S)b.length<2&&(l.endsWith("/")?l=l.slice(0,-1):f=!0);else throw new Error(`Missing required param "${x}"`);l+=j}}return l||"/"}return{re:u,score:r,keys:a,parse:h,stringify:c}}function kn(t,s){let o=0;for(;o<t.length&&o<s.length;){const r=s[o]-t[o];if(r)return r;o++}return t.length<s.length?t.length===1&&t[0]===40+40?-1:1:t.length>s.length?s.length===1&&s[0]===40+40?1:-1:0}function Do(t,s){let o=0;const r=t.score,n=s.score;for(;o<r.length&&o<n.length;){const a=kn(r[o],n[o]);if(a)return a;o++}if(Math.abs(n.length-r.length)===1){if(Us(r))return 1;if(Us(n))return-1}return n.length-r.length}function Us(t){const s=t[t.length-1];return t.length>0&&s[s.length-1]<0}const Cn={type:0,value:""},$n=/[a-zA-Z0-9_]/;function Sn(t){if(!t)return[[]];if(t==="/")return[[Cn]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function s(v){throw new Error(`ERR (${o})/"${i}": ${v}`)}let o=0,r=o;const n=[];let a;function u(){a&&n.push(a),a=[]}let h=0,c,i="",l="";function f(){i&&(o===0?a.push({type:0,value:i}):o===1||o===2||o===3?(a.length>1&&(c==="*"||c==="+")&&s(`A repeatable param (${i}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:i,regexp:l,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):s("Invalid state to consume buffer"),i="")}function b(){i+=c}for(;h<t.length;){if(c=t[h++],c==="\\"&&o!==2){r=o,o=4;continue}switch(o){case 0:c==="/"?(i&&f(),u()):c===":"?(f(),o=1):b();break;case 4:b(),o=r;break;case 1:c==="("?o=2:$n.test(c)?b():(f(),o=0,c!=="*"&&c!=="?"&&c!=="+"&&h--);break;case 2:c===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+c:o=3:l+=c;break;case 3:f(),o=0,c!=="*"&&c!=="?"&&c!=="+"&&h--,l="";break;default:s("Unknown state");break}}return o===2&&s(`Unfinished custom RegExp for param "${i}"`),f(),u(),n}function Mn(t,s,o){const r=_n(Sn(t.path),o),n=he(r,{record:t,parent:s,children:[],alias:[]});return s&&!n.record.aliasOf==!s.record.aliasOf&&s.children.push(n),n}function An(t,s){const o=[],r=new Map;s=Ys({strict:!1,end:!0,sensitive:!1},s);function n(f){return r.get(f)}function a(f,b,v){const x=!v,g=Ws(f);g.aliasOf=v&&v.record;const S=Ys(s,f),P=[g];if("alias"in f){const _=typeof f.alias=="string"?[f.alias]:f.alias;for(const M of _)P.push(Ws(he({},g,{components:v?v.record.components:g.components,path:M,aliasOf:v?v.record:g})))}let j,w;for(const _ of P){const{path:M}=_;if(b&&M[0]!=="/"){const T=b.record.path,I=T[T.length-1]==="/"?"":"/";_.path=b.record.path+(M&&I+M)}if(j=Mn(_,b,S),v?v.alias.push(j):(w=w||j,w!==j&&w.alias.push(j),x&&f.name&&!Ks(j)&&u(f.name)),zo(j)&&c(j),g.children){const T=g.children;for(let I=0;I<T.length;I++)a(T[I],j,v&&v.children[I])}v=v||j}return w?()=>{u(w)}:ht}function u(f){if(No(f)){const b=r.get(f);b&&(r.delete(f),o.splice(o.indexOf(b),1),b.children.forEach(u),b.alias.forEach(u))}else{const b=o.indexOf(f);b>-1&&(o.splice(b,1),f.record.name&&r.delete(f.record.name),f.children.forEach(u),f.alias.forEach(u))}}function h(){return o}function c(f){const b=In(f,o);o.splice(b,0,f),f.record.name&&!Ks(f)&&r.set(f.record.name,f)}function i(f,b){let v,x={},g,S;if("name"in f&&f.name){if(v=r.get(f.name),!v)throw lt(1,{location:f});S=v.record.name,x=he(Hs(b.params,v.keys.filter(w=>!w.optional).concat(v.parent?v.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),f.params&&Hs(f.params,v.keys.map(w=>w.name))),g=v.stringify(x)}else if(f.path!=null)g=f.path,v=o.find(w=>w.re.test(g)),v&&(x=v.parse(g),S=v.record.name);else{if(v=b.name?r.get(b.name):o.find(w=>w.re.test(b.path)),!v)throw lt(1,{location:f,currentLocation:b});S=v.record.name,x=he({},b.params,f.params),g=v.stringify(x)}const P=[];let j=v;for(;j;)P.unshift(j.record),j=j.parent;return{name:S,path:g,params:x,matched:P,meta:En(P)}}t.forEach(f=>a(f));function l(){o.length=0,r.clear()}return{addRoute:a,resolve:i,removeRoute:u,clearRoutes:l,getRoutes:h,getRecordMatcher:n}}function Hs(t,s){const o={};for(const r of s)r in t&&(o[r]=t[r]);return o}function Ws(t){const s={path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:t.aliasOf,beforeEnter:t.beforeEnter,props:jn(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}};return Object.defineProperty(s,"mods",{value:{}}),s}function jn(t){const s={},o=t.props||!1;if("component"in t)s.default=o;else for(const r in t.components)s[r]=typeof o=="object"?o[r]:o;return s}function Ks(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function En(t){return t.reduce((s,o)=>he(s,o.meta),{})}function Ys(t,s){const o={};for(const r in t)o[r]=r in s?s[r]:t[r];return o}function In(t,s){let o=0,r=s.length;for(;o!==r;){const a=o+r>>1;Do(t,s[a])<0?r=a:o=a+1}const n=Tn(t);return n&&(r=s.lastIndexOf(n,r-1)),r}function Tn(t){let s=t;for(;s=s.parent;)if(zo(s)&&Do(t,s)===0)return s}function zo({record:t}){return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect)}function Bn(t){const s={};if(t===""||t==="?")return s;const r=(t[0]==="?"?t.slice(1):t).split("&");for(let n=0;n<r.length;++n){const a=r[n].replace(Eo," "),u=a.indexOf("="),h=yt(u<0?a:a.slice(0,u)),c=u<0?null:yt(a.slice(u+1));if(h in s){let i=s[h];Oe(i)||(i=s[h]=[i]),i.push(c)}else s[h]=c}return s}function Xs(t){let s="";for(let o in t){const r=t[o];if(o=Jr(o),r==null){r!==void 0&&(s+=(s.length?"&":"")+o);continue}(Oe(r)?r.map(a=>a&&ds(a)):[r&&ds(r)]).forEach(a=>{a!==void 0&&(s+=(s.length?"&":"")+o,a!=null&&(s+="="+a))})}return s}function Pn(t){const s={};for(const o in t){const r=t[o];r!==void 0&&(s[o]=Oe(r)?r.map(n=>n==null?null:""+n):r==null?r:""+r)}return s}const Rn=Symbol(""),Qs=Symbol(""),Nt=Symbol(""),Ss=Symbol(""),cs=Symbol("");function ct(){let t=[];function s(r){return t.push(r),()=>{const n=t.indexOf(r);n>-1&&t.splice(n,1)}}function o(){t=[]}return{add:s,list:()=>t.slice(),reset:o}}function Ke(t,s,o,r,n,a=u=>u()){const u=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((h,c)=>{const i=b=>{b===!1?c(lt(4,{from:o,to:s})):b instanceof Error?c(b):bn(b)?c(lt(2,{from:s,to:b})):(u&&r.enterCallbacks[n]===u&&typeof b=="function"&&u.push(b),h())},l=a(()=>t.call(r&&r.instances[n],s,o,i));let f=Promise.resolve(l);t.length<3&&(f=f.then(i)),f.catch(b=>c(b))})}function Jt(t,s,o,r,n=a=>a()){const a=[];for(const u of t)for(const h in u.components){let c=u.components[h];if(!(s!=="beforeRouteEnter"&&!u.instances[h]))if(Ao(c)){const l=(c.__vccOpts||c)[s];l&&a.push(Ke(l,o,r,u,h,n))}else{let i=c();a.push(()=>i.then(l=>{if(!l)throw new Error(`Couldn't resolve component "${h}" at "${u.path}"`);const f=Vr(l)?l.default:l;u.mods[h]=l,u.components[h]=f;const v=(f.__vccOpts||f)[s];return v&&Ke(v,o,r,u,h,n)()}))}}return a}function Gs(t){const s=Qe(Nt),o=Qe(Ss),r=N(()=>{const c=k(t.to);return s.resolve(c)}),n=N(()=>{const{matched:c}=r.value,{length:i}=c,l=c[i-1],f=o.matched;if(!l||!f.length)return-1;const b=f.findIndex(at.bind(null,l));if(b>-1)return b;const v=Js(c[i-2]);return i>1&&Js(l)===v&&f[f.length-1].path!==v?f.findIndex(at.bind(null,c[i-2])):b}),a=N(()=>n.value>-1&&On(o.params,r.value.params)),u=N(()=>n.value>-1&&n.value===o.matched.length-1&&Po(o.params,r.value.params));function h(c={}){if(zn(c)){const i=s[k(t.replace)?"replace":"push"](k(t.to)).catch(ht);return t.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>i),i}return Promise.resolve()}return{route:r,href:N(()=>r.value.href),isActive:a,isExactActive:u,navigate:h}}function Nn(t){return t.length===1?t[0]:t}const Ln=ge({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Gs,setup(t,{slots:s}){const o=Mr(Gs(t)),{options:r}=Qe(Nt),n=N(()=>({[Zs(t.activeClass,r.linkActiveClass,"router-link-active")]:o.isActive,[Zs(t.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:o.isExactActive}));return()=>{const a=s.default&&Nn(s.default(o));return t.custom?a:We("a",{"aria-current":o.isExactActive?t.ariaCurrentValue:null,href:o.href,onClick:o.navigate,class:n.value},a)}}}),Dn=Ln;function zn(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const s=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(s))return}return t.preventDefault&&t.preventDefault(),!0}}function On(t,s){for(const o in s){const r=s[o],n=t[o];if(typeof r=="string"){if(r!==n)return!1}else if(!Oe(n)||n.length!==r.length||r.some((a,u)=>a!==n[u]))return!1}return!0}function Js(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const Zs=(t,s,o)=>t??s??o,Vn=ge({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:s,slots:o}){const r=Qe(cs),n=N(()=>t.route||r.value),a=Qe(Qs,0),u=N(()=>{let i=k(a);const{matched:l}=n.value;let f;for(;(f=l[i])&&!f.components;)i++;return i}),h=N(()=>n.value.matched[u.value]);Wt(Qs,N(()=>u.value+1)),Wt(Rn,h),Wt(cs,n);const c=L();return Me(()=>[c.value,h.value,t.name],([i,l,f],[b,v,x])=>{l&&(l.instances[f]=i,v&&v!==l&&i&&i===b&&(l.leaveGuards.size||(l.leaveGuards=v.leaveGuards),l.updateGuards.size||(l.updateGuards=v.updateGuards))),i&&l&&(!v||!at(l,v)||!b)&&(l.enterCallbacks[f]||[]).forEach(g=>g(i))},{flush:"post"}),()=>{const i=n.value,l=t.name,f=h.value,b=f&&f.components[l];if(!b)return eo(o.default,{Component:b,route:i});const v=f.props[l],x=v?v===!0?i.params:typeof v=="function"?v(i):v:null,S=We(b,he({},x,s,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(f.instances[l]=null)},ref:c}));return eo(o.default,{Component:S,route:i})||S}}});function eo(t,s){if(!t)return null;const o=t(s);return o.length===1?o[0]:o}const Fn=Vn;function qn(t){const s=An(t.routes,t),o=t.parseQuery||Bn,r=t.stringifyQuery||Xs,n=t.history,a=ct(),u=ct(),h=ct(),c=$r(He);let i=He;ot&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=Qt.bind(null,F=>""+F),f=Qt.bind(null,en),b=Qt.bind(null,yt);function v(F,$){let C,q;return No(F)?(C=s.getRecordMatcher(F),q=$):q=F,s.addRoute(q,C)}function x(F){const $=s.getRecordMatcher(F);$&&s.removeRoute($)}function g(){return s.getRoutes().map(F=>F.record)}function S(F){return!!s.getRecordMatcher(F)}function P(F,$){if($=he({},$||c.value),typeof F=="string"){const B=Gt(o,F,$.path),Z=s.resolve({path:B.path},$),le=n.createHref(B.fullPath);return he(B,Z,{params:b(Z.params),hash:yt(B.hash),redirectedFrom:void 0,href:le})}let C;if(F.path!=null)C=he({},F,{path:Gt(o,F.path,$.path).path});else{const B=he({},F.params);for(const Z in B)B[Z]==null&&delete B[Z];C=he({},F,{params:f(B)}),$.params=f($.params)}const q=s.resolve(C,$),y=F.hash||"";q.params=l(b(q.params));const m=on(r,he({},F,{hash:Gr(y),path:q.path})),H=n.createHref(m);return he({fullPath:m,hash:y,query:r===Xs?Pn(F.query):F.query||{}},q,{redirectedFrom:void 0,href:H})}function j(F){return typeof F=="string"?Gt(o,F,c.value.path):he({},F)}function w(F,$){if(i!==F)return lt(8,{from:$,to:F})}function _(F){return I(F)}function M(F){return _(he(j(F),{replace:!0}))}function T(F){const $=F.matched[F.matched.length-1];if($&&$.redirect){const{redirect:C}=$;let q=typeof C=="function"?C(F):C;return typeof q=="string"&&(q=q.includes("?")||q.includes("#")?q=j(q):{path:q},q.params={}),he({query:F.query,hash:F.hash,params:q.path!=null?{}:F.params},q)}}function I(F,$){const C=i=P(F),q=c.value,y=F.state,m=F.force,H=F.replace===!0,B=T(C);if(B)return I(he(j(B),{state:typeof B=="object"?he({},y,B.state):y,force:m,replace:H}),$||C);const Z=C;Z.redirectedFrom=$;let le;return!m&&rn(r,q,C)&&(le=lt(16,{to:Z,from:q}),re(q,q,!0,!1)),(le?Promise.resolve(le):X(Z,q)).catch(ne=>qe(ne)?qe(ne,2)?ne:ee(ne):W(ne,Z,q)).then(ne=>{if(ne){if(qe(ne,2))return I(he({replace:H},j(ne.to),{state:typeof ne.to=="object"?he({},y,ne.to.state):y,force:m}),$||Z)}else ne=G(Z,q,!0,H,y);return oe(Z,q,ne),ne})}function U(F,$){const C=w(F,$);return C?Promise.reject(C):Promise.resolve()}function te(F){const $=Te.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(F):F()}function X(F,$){let C;const[q,y,m]=Un(F,$);C=Jt(q.reverse(),"beforeRouteLeave",F,$);for(const B of q)B.leaveGuards.forEach(Z=>{C.push(Ke(Z,F,$))});const H=U.bind(null,F,$);return C.push(H),_e(C).then(()=>{C=[];for(const B of a.list())C.push(Ke(B,F,$));return C.push(H),_e(C)}).then(()=>{C=Jt(y,"beforeRouteUpdate",F,$);for(const B of y)B.updateGuards.forEach(Z=>{C.push(Ke(Z,F,$))});return C.push(H),_e(C)}).then(()=>{C=[];for(const B of m)if(B.beforeEnter)if(Oe(B.beforeEnter))for(const Z of B.beforeEnter)C.push(Ke(Z,F,$));else C.push(Ke(B.beforeEnter,F,$));return C.push(H),_e(C)}).then(()=>(F.matched.forEach(B=>B.enterCallbacks={}),C=Jt(m,"beforeRouteEnter",F,$,te),C.push(H),_e(C))).then(()=>{C=[];for(const B of u.list())C.push(Ke(B,F,$));return C.push(H),_e(C)}).catch(B=>qe(B,8)?B:Promise.reject(B))}function oe(F,$,C){h.list().forEach(q=>te(()=>q(F,$,C)))}function G(F,$,C,q,y){const m=w(F,$);if(m)return m;const H=$===He,B=ot?history.state:{};C&&(q||H?n.replace(F.fullPath,he({scroll:H&&B&&B.scroll},y)):n.push(F.fullPath,y)),c.value=F,re(F,$,C,H),ee()}let V;function se(){V||(V=n.listen((F,$,C)=>{if(!Pe.listening)return;const q=P(F),y=T(q);if(y){I(he(y,{replace:!0,force:!0}),q).catch(ht);return}i=q;const m=c.value;ot&&fn(Os(m.fullPath,C.delta),Rt()),X(q,m).catch(H=>qe(H,12)?H:qe(H,2)?(I(he(j(H.to),{force:!0}),q).then(B=>{qe(B,20)&&!C.delta&&C.type===bt.pop&&n.go(-1,!1)}).catch(ht),Promise.reject()):(C.delta&&n.go(-C.delta,!1),W(H,q,m))).then(H=>{H=H||G(q,m,!1),H&&(C.delta&&!qe(H,8)?n.go(-C.delta,!1):C.type===bt.pop&&qe(H,20)&&n.go(-1,!1)),oe(q,m,H)}).catch(ht)}))}let de=ct(),O=ct(),E;function W(F,$,C){ee(F);const q=O.list();return q.length?q.forEach(y=>y(F,$,C)):console.error(F),Promise.reject(F)}function Y(){return E&&c.value!==He?Promise.resolve():new Promise((F,$)=>{de.add([F,$])})}function ee(F){return E||(E=!F,se(),de.list().forEach(([$,C])=>F?C(F):$()),de.reset()),F}function re(F,$,C,q){const{scrollBehavior:y}=t;if(!ot||!y)return Promise.resolve();const m=!C&&pn(Os(F.fullPath,0))||(q||!C)&&history.state&&history.state.scroll||null;return Pt().then(()=>y(F,$,m)).then(H=>H&&mn(H)).catch(H=>W(H,F,$))}const pe=F=>n.go(F);let xe;const Te=new Set,Pe={currentRoute:c,listening:!0,addRoute:v,removeRoute:x,clearRoutes:s.clearRoutes,hasRoute:S,getRoutes:g,resolve:P,options:t,push:_,replace:M,go:pe,back:()=>pe(-1),forward:()=>pe(1),beforeEach:a.add,beforeResolve:u.add,afterEach:h.add,onError:O.add,isReady:Y,install(F){const $=this;F.component("RouterLink",Dn),F.component("RouterView",Fn),F.config.globalProperties.$router=$,Object.defineProperty(F.config.globalProperties,"$route",{enumerable:!0,get:()=>k(c)}),ot&&!xe&&c.value===He&&(xe=!0,_(n.location).catch(y=>{}));const C={};for(const y in He)Object.defineProperty(C,y,{get:()=>c.value[y],enumerable:!0});F.provide(Nt,$),F.provide(Ss,Sr(C)),F.provide(cs,c);const q=F.unmount;Te.add(F),F.unmount=function(){Te.delete(F),Te.size<1&&(i=He,V&&V(),V=null,c.value=He,xe=!1,E=!1),q()}}};function _e(F){return F.reduce(($,C)=>$.then(()=>te(C)),Promise.resolve())}return Pe}function Un(t,s){const o=[],r=[],n=[],a=Math.max(s.matched.length,t.matched.length);for(let u=0;u<a;u++){const h=s.matched[u];h&&(t.matched.find(i=>at(i,h))?r.push(h):o.push(h));const c=t.matched[u];c&&(s.matched.find(i=>at(i,c))||n.push(c))}return[o,r,n]}function et(){return Qe(Nt)}function Oo(t){return Qe(Ss)}const be=Ze("auth",()=>{const t=L(null),s=L(localStorage.getItem("auth_token")),o=L(!1),r=L(!1),n=N(()=>!!s.value&&!!t.value||r.value),a=async f=>{o.value=!0;try{const b=await fetch("/api/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(f)});if(!b.ok)throw new Error("Login failed");const v=await b.json();return s.value=v.token,t.value=v.user,localStorage.setItem("auth_token",v.token),v}catch(b){throw console.error("Login error:",b),b}finally{o.value=!1}},u=async()=>{var f;o.value=!0;try{await fetch("/logout",{method:"POST",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((f=document.querySelector('meta[name="csrf-token"]'))==null?void 0:f.getAttribute("content"))||""},credentials:"include"}),s.value&&await fetch("/api/logout",{method:"POST",headers:{Authorization:`Bearer ${s.value}`,Accept:"application/json"}})}catch(b){console.error("Logout error:",b)}finally{t.value=null,s.value=null,r.value=!1,localStorage.removeItem("auth_token"),o.value=!1}},h=async()=>{o.value=!0;try{if(window.user){t.value=window.user,r.value=!0,console.log("Using server-provided user data:",t.value);return}const f=await fetch("/api/user-session",{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"include"});if(f.ok){const b=await f.json();if(b&&b.authenticated!==!1){t.value=b,r.value=!0,console.log("Session auth successful:",b);return}}if(s.value){const b=await fetch("/api/user",{headers:{Authorization:`Bearer ${s.value}`,Accept:"application/json"}});if(b.ok){const v=await b.json();if(v){t.value=v,console.log("Token auth successful:",v);return}}}console.log("No authentication found, clearing auth state"),t.value=null,r.value=!1,s.value&&(s.value=null,localStorage.removeItem("auth_token"))}catch(f){console.error("Fetch user error:",f),t.value=null,r.value=!1,s.value&&(s.value=null,localStorage.removeItem("auth_token"))}finally{o.value=!1}};return{user:t,token:s,isLoading:o,sessionAuth:r,isAuthenticated:n,login:a,logout:u,fetchUser:h,initialize:async()=>{await h()},setUser:f=>{t.value=f,r.value=!0,console.log("Auth store: user set directly",f)},setLoading:f=>{o.value=f}}});var Hn=typeof global=="object"&&global&&global.Object===Object&&global;const Wn=Hn;var Kn=typeof self=="object"&&self&&self.Object===Object&&self,Yn=Wn||Kn||Function("return this")();const Vo=Yn;var Xn=Vo.Symbol;const Et=Xn;var Fo=Object.prototype,Qn=Fo.hasOwnProperty,Gn=Fo.toString,mt=Et?Et.toStringTag:void 0;function Jn(t){var s=Qn.call(t,mt),o=t[mt];try{t[mt]=void 0;var r=!0}catch{}var n=Gn.call(t);return r&&(s?t[mt]=o:delete t[mt]),n}var Zn=Object.prototype,ea=Zn.toString;function ta(t){return ea.call(t)}var sa="[object Null]",oa="[object Undefined]",to=Et?Et.toStringTag:void 0;function ra(t){return t==null?t===void 0?oa:sa:to&&to in Object(t)?Jn(t):ta(t)}function na(t){return t!=null&&typeof t=="object"}var aa="[object Symbol]";function la(t){return typeof t=="symbol"||na(t)&&ra(t)==aa}var ia=/\s/;function da(t){for(var s=t.length;s--&&ia.test(t.charAt(s)););return s}var ua=/^\s+/;function ca(t){return t&&t.slice(0,da(t)+1).replace(ua,"")}function ms(t){var s=typeof t;return t!=null&&(s=="object"||s=="function")}var so=0/0,ma=/^[-+]0x[0-9a-f]+$/i,fa=/^0b[01]+$/i,pa=/^0o[0-7]+$/i,ga=parseInt;function oo(t){if(typeof t=="number")return t;if(la(t))return so;if(ms(t)){var s=typeof t.valueOf=="function"?t.valueOf():t;t=ms(s)?s+"":s}if(typeof t!="string")return t===0?t:+t;t=ca(t);var o=fa.test(t);return o||pa.test(t)?ga(t.slice(2),o?2:8):ma.test(t)?so:+t}var ha=function(){return Vo.Date.now()};const Zt=ha;var va="Expected a function",ya=Math.max,ba=Math.min;function xa(t,s,o){var r,n,a,u,h,c,i=0,l=!1,f=!1,b=!0;if(typeof t!="function")throw new TypeError(va);s=oo(s)||0,ms(o)&&(l=!!o.leading,f="maxWait"in o,a=f?ya(oo(o.maxWait)||0,s):a,b="trailing"in o?!!o.trailing:b);function v(T){var I=r,U=n;return r=n=void 0,i=T,u=t.apply(U,I),u}function x(T){return i=T,h=setTimeout(P,s),l?v(T):u}function g(T){var I=T-c,U=T-i,te=s-I;return f?ba(te,a-U):te}function S(T){var I=T-c,U=T-i;return c===void 0||I>=s||I<0||f&&U>=a}function P(){var T=Zt();if(S(T))return j(T);h=setTimeout(P,g(T))}function j(T){return h=void 0,b&&r?v(T):(r=n=void 0,u)}function w(){h!==void 0&&clearTimeout(h),i=0,r=c=n=h=void 0}function _(){return h===void 0?u:j(Zt())}function M(){var T=Zt(),I=S(T);if(r=arguments,n=this,c=T,I){if(h===void 0)return x(c);if(f)return clearTimeout(h),h=setTimeout(P,s),v(c)}return h===void 0&&(h=setTimeout(P,s)),u}return M.cancel=w,M.flush=_,M}const wa={class:"overflow-hidden"},_a={key:0,class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},ka=["checked","indeterminate"],Ca=["onClick"],$a={key:0},Sa=["colspan"],Ma={key:1},Aa=["colspan"],ja={key:0,class:"px-4 py-3"},Ea=["checked","onChange"],Ia=["innerHTML"],Ta={key:1,class:"text-gray-900"},Ba=ge({__name:"Table",props:{columns:{},data:{},loading:{type:Boolean,default:!1},striped:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},hover:{type:Boolean,default:!0},size:{default:"md"},sortBy:{},sortDirection:{default:"asc"},selectable:{type:Boolean,default:!1},selectedRows:{default:()=>[]},emptyMessage:{default:"No data available"},stickyHeader:{type:Boolean,default:!1},maxHeight:{}},emits:["sort","select","row-click"],setup(t,{emit:s}){const o=t,r=s,n=L([...o.selectedRows]),a=N(()=>"min-w-full divide-y divide-gray-200"),u=N(()=>{const j="bg-gray-50",w=o.stickyHeader?"sticky top-0 z-10":"";return[j,w].filter(Boolean).join(" ")}),h=N(()=>"bg-white divide-y divide-gray-200"),c={sm:"text-sm",md:"text-sm",lg:"text-base"},i=N(()=>o.columns.length+(o.selectable?1:0)),l=N(()=>o.data.length>0&&n.value.length===o.data.length),f=N(()=>n.value.length>0&&n.value.length<o.data.length),b=(j,w)=>w.split(".").reduce((_,M)=>_==null?void 0:_[M],j),v=(j,w)=>j.id||j.key||w,x=j=>{const w=o.sortBy===j&&o.sortDirection==="asc"?"desc":"asc";r("sort",{column:j,direction:w})},g=j=>n.value.some(w=>v(w,-1)===v(j,-1)),S=j=>{const w=g(j);w?n.value=n.value.filter(_=>v(_,-1)!==v(j,-1)):n.value.push(j),r("select",{selectedRows:[...n.value],row:j,isSelected:!w})},P=()=>{l.value?n.value=[]:n.value=[...o.data],r("select",{selectedRows:[...n.value]})};return(j,w)=>(d(),p("div",wa,[e("div",{class:K(["overflow-auto",j.maxHeight?`max-h-[${j.maxHeight}]`:"",j.bordered?"border border-gray-200 rounded-lg":""])},[e("table",{class:K(a.value)},[e("thead",{class:K(u.value)},[e("tr",null,[j.selectable?(d(),p("th",_a,[e("input",{type:"checkbox",checked:l.value,indeterminate:f.value,onChange:P,class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,ka)])):R("",!0),(d(!0),p(ue,null,fe(j.columns,_=>(d(),p("th",{key:_.key,class:K(["px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",_.align==="center"?"text-center":_.align==="right"?"text-right":"text-left",_.sortable?"cursor-pointer hover:bg-gray-100 select-none":"",_.headerClass||""]),style:Ge(_.width?{width:_.width}:{}),onClick:M=>_.sortable?x(_.key):null},[e("div",{class:K(["flex items-center",_.align==="center"?"justify-center":_.align==="right"?"justify-end":"justify-start"])},[e("span",null,A(_.label),1),_.sortable?(d(),p(ue,{key:0},[j.sortBy===_.key&&j.sortDirection==="asc"?(d(),ce(k(Lr),{key:0,class:"ml-1 h-4 w-4"})):j.sortBy===_.key&&j.sortDirection==="desc"?(d(),ce(k($o),{key:1,class:"ml-1 h-4 w-4"})):(d(),ce(k(Nr),{key:2,class:"ml-1 h-4 w-4 text-gray-400"}))],64)):R("",!0)],2)],14,Ca))),128))])],2),e("tbody",{class:K(h.value)},[j.loading?(d(),p("tr",$a,[e("td",{colspan:i.value,class:"px-4 py-8 text-center"},w[0]||(w[0]=[Ye('<div class="flex items-center justify-center"><svg class="animate-spin h-5 w-5 text-primary-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span class="text-gray-500">Loading...</span></div>',1)]),8,Sa)])):j.data.length===0?(d(),p("tr",Ma,[e("td",{colspan:i.value,class:"px-4 py-8 text-center text-gray-500"},A(j.emptyMessage),9,Aa)])):(d(!0),p(ue,{key:2},fe(j.data,(_,M)=>(d(),p("tr",{key:v(_,M),class:K(["transition-colors duration-150",j.hover?"hover:bg-gray-50":"",j.striped&&M%2===1?"bg-gray-50":"",g(_)?"bg-primary-50":""])},[j.selectable?(d(),p("td",ja,[e("input",{type:"checkbox",checked:g(_),onChange:T=>S(_),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Ea)])):R("",!0),(d(!0),p(ue,null,fe(j.columns,T=>(d(),p("td",{key:T.key,class:K(["px-4 py-3",T.align==="center"?"text-center":T.align==="right"?"text-right":"text-left",c[j.size],T.cellClass||""])},[T.render?(d(),p(ue,{key:0},[typeof T.render(b(_,T.key),_,M)=="object"?(d(),ce(Ne(T.render(b(_,T.key),_,M)),{key:0})):(d(),p("span",{key:1,innerHTML:T.render(b(_,T.key),_,M)},null,8,Ia))],64)):(d(),p("span",Ta,A(b(_,T.key)),1))],2))),128))],2))),128))],2)],2)],2)]))}}),_t=Ze("watchlist",()=>{const t=L([]),s=L(!1),o=L(null),r=L(0),n=()=>{window.dispatchEvent(new CustomEvent("watchlist-updated",{detail:{count:t.value.length}}))},a=N(()=>t.value.length),u=N(()=>t.value.length===0),h=N(()=>t.value.map(w=>w.id)),c=async(w=1,_=15,M="")=>{var I;const T=be();if(!T.isAuthenticated)return t.value=[],{data:[],meta:{}};s.value=!0,o.value=null;try{const U=new URLSearchParams({page:w.toString(),per_page:_.toString(),...M&&{search:M}}),te={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},X=(I=document.querySelector('meta[name="csrf-token"]'))==null?void 0:I.getAttribute("content");X&&(te["X-CSRF-TOKEN"]=X),T.token&&(te.Authorization=`Bearer ${T.token}`);const oe=await fetch(`/api/watchlist?${U}`,{headers:te,credentials:"include"});if(!oe.ok)throw new Error("Failed to fetch watchlist");const G=await oe.json();return w===1?t.value=G.data||[]:t.value.push(...G.data||[]),r.value=Date.now(),n(),G}catch(U){return o.value=U instanceof Error?U.message:"Failed to fetch watchlist",console.error("Watchlist fetch error:",U),{data:[],meta:{}}}finally{s.value=!1}},i=async w=>{var M;const _=be();if(!_.isAuthenticated)return o.value="Please sign in to add items to your watchlist",!1;x(w.id)||t.value.unshift(w);try{const T={"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"},I=(M=document.querySelector('meta[name="csrf-token"]'))==null?void 0:M.getAttribute("content");I&&(T["X-CSRF-TOKEN"]=I),_.token&&(T.Authorization=`Bearer ${_.token}`);const U=await fetch("/api/watchlist",{method:"POST",headers:T,credentials:"include",body:JSON.stringify({item_id:w.id})}),te=await U.json();if(!U.ok)throw g(w.id),new Error(te.message||"Failed to add item to watchlist");return o.value=null,n(),!0}catch(T){return g(w.id),o.value=T instanceof Error?T.message:"Failed to add item to watchlist",console.error("Add to watchlist error:",T),!1}},l=async w=>{var T;const _=be();if(!_.isAuthenticated)return o.value="Please sign in to manage your watchlist",!1;const M=[...t.value];g(w);try{const I={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},U=(T=document.querySelector('meta[name="csrf-token"]'))==null?void 0:T.getAttribute("content");U&&(I["X-CSRF-TOKEN"]=U),_.token&&(I.Authorization=`Bearer ${_.token}`);const te=await fetch(`/api/watchlist/${w}`,{method:"DELETE",headers:I,credentials:"include"}),X=await te.json();if(!te.ok)throw t.value=M,new Error(X.message||"Failed to remove item from watchlist");return o.value=null,n(),!0}catch(I){return t.value=M,o.value=I instanceof Error?I.message:"Failed to remove item from watchlist",console.error("Remove from watchlist error:",I),!1}},f=async w=>x(w.id)?await l(w.id):await i(w),b=async w=>{var M;const _=be();if(!_.isAuthenticated)return!1;try{const T={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},I=(M=document.querySelector('meta[name="csrf-token"]'))==null?void 0:M.getAttribute("content");I&&(T["X-CSRF-TOKEN"]=I),_.token&&(T.Authorization=`Bearer ${_.token}`);const U=await fetch(`/api/watchlist/check/${w}`,{headers:T,credentials:"include"});return U.ok&&(await U.json()).in_watchlist||!1}catch(T){return console.error("Check watchlist status error:",T),!1}},v=async()=>{var _;const w=be();if(!w.isAuthenticated)return t.value=[],0;try{const M={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},T=(_=document.querySelector('meta[name="csrf-token"]'))==null?void 0:_.getAttribute("content");T&&(M["X-CSRF-TOKEN"]=T),w.token&&(M.Authorization=`Bearer ${w.token}`);const I=await fetch("/api/watchlist/count",{headers:M,credentials:"include"});if(!I.ok)return t.value.length;const te=(await I.json()).count||0;return te!==t.value.length&&await c(),te}catch(M){return console.error("Sync watchlist count error:",M),t.value.length}},x=w=>t.value.some(_=>_.id===w),g=w=>{const _=t.value.findIndex(M=>M.id===w);_>-1&&t.value.splice(_,1)};return{items:t,isLoading:s,error:o,lastSyncTime:r,watchlistCount:a,isEmpty:u,watchlistItemIds:h,fetchWatchlist:c,addToWatchlist:i,removeFromWatchlist:l,toggleWatchlist:f,checkWatchlistStatus:b,syncWatchlistCount:v,isInWatchlist:x,clearWatchlist:()=>{t.value=[],o.value=null,r.value=0,n()},refreshWatchlist:async()=>await c(),initializeWatchlist:async()=>{be().isAuthenticated&&await c()}}}),Le=Ze("notifications",()=>{const t=L([]),s=c=>{const i=Date.now().toString()+Math.random().toString(36).substr(2,9),l={id:i,duration:5e3,persistent:!1,...c};return t.value.push(l),i};return{notifications:t,addNotification:s,removeNotification:c=>{const i=t.value.findIndex(l=>l.id===c);i>-1&&t.value.splice(i,1)},clearAll:()=>{t.value=[]},success:(c,i,l)=>s({type:"success",message:c,title:i,...l}),error:(c,i,l)=>s({type:"error",message:c,title:i,duration:7e3,...l}),info:(c,i,l)=>s({type:"info",message:c,title:i,...l}),warning:(c,i,l)=>s({type:"warning",message:c,title:i,duration:6e3,...l})}}),Pa={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"auth-modal-title",role:"dialog","aria-modal":"true"},Ra={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Na={key:0,class:"inline-block align-bottom bg-white rounded-2xl shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full overflow-hidden"},La={class:"relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 px-6 py-8 text-center"},Da={class:"mb-4"},za={class:"mx-auto w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"},Oa={id:"auth-modal-title",class:"text-2xl font-bold text-white mb-2"},Va={class:"text-slate-300 text-sm"},Fa={class:"relative bg-white"},qa={class:"px-6 py-8"},Ua={key:"login",class:"space-y-6"},Ha={class:"relative"},Wa={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Ka=["disabled"],Ya={class:"relative"},Xa={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Qa=["type","disabled"],Ga={class:"flex items-center justify-between"},Ja={class:"flex items-center"},Za={class:"text-center"},el={class:"text-sm text-gray-600"},tl={key:0,class:"bg-green-50 border border-green-200 rounded-xl p-4"},sl={class:"flex"},ol={class:"ml-3"},rl={class:"text-sm text-green-800"},nl={key:1,class:"bg-red-50 border border-red-200 rounded-xl p-4"},al={class:"flex"},ll={class:"ml-3"},il={class:"text-sm text-red-800"},dl=["disabled"],ul={key:0},cl={key:1,class:"flex items-center"},ml={key:"register",class:"space-y-6"},fl={class:"grid grid-cols-2 gap-4"},pl=["disabled"],gl=["disabled"],hl={class:"relative"},vl={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},yl=["disabled"],bl={key:0},xl={class:"relative"},wl={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},_l=["required","disabled"],kl={class:"grid grid-cols-1 gap-4"},Cl={class:"relative"},$l={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Sl=["type","disabled"],Ml={class:"relative"},Al={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},jl=["type","disabled"],El={class:"flex items-start"},Il={class:"flex items-center h-5"},Tl={key:1,class:"bg-green-50 border border-green-200 rounded-xl p-4"},Bl={class:"flex"},Pl={class:"ml-3"},Rl={class:"text-sm text-green-800"},Nl={key:2,class:"bg-red-50 border border-red-200 rounded-xl p-4"},Ll={class:"flex"},Dl={class:"ml-3"},zl={class:"text-sm text-red-800"},Ol=["disabled"],Vl={key:0},Fl={key:1,class:"flex items-center"},ql={class:"text-center"},Ul={class:"text-sm text-gray-600"},Hl=ge({__name:"AuthModal",props:{show:{type:Boolean},startWithRegister:{type:Boolean,default:!1},title:{default:""},subtitle:{default:""},closable:{type:Boolean,default:!0},closeOnBackdrop:{type:Boolean,default:!0},showPhoneField:{type:Boolean,default:!0},showSocialLogin:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!0},customBranding:{}},emits:["update:show","success","close","forgot-password","social-login"],setup(t,{expose:s,emit:o}){const r=t,n=o,a=be(),u=Le(),h=L(r.startWithRegister),c=L(!1),i=L(!1),l=L(""),f=L(""),b=L({email:"",password:"",remember:!1}),v=L({firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1}),x=N(()=>a.isLoading),g=N(()=>r.title?r.title:h.value?"Join Us Today":"Welcome Back"),S=N(()=>r.subtitle?r.subtitle:h.value?"Create an account to get started":"Sign in to your account to continue"),P=N(()=>v.value.firstName.trim()&&v.value.lastName.trim()&&v.value.email.trim()&&v.value.phone.trim()&&v.value.password&&v.value.passwordConfirmation&&v.value.password===v.value.passwordConfirmation&&v.value.password.length>=4&&v.value.acceptTerms),j=()=>{h.value=!h.value,M(),T(),r.autoFocus&&setTimeout(async()=>{await oe()},150)},w=()=>{l.value=""},_=()=>{f.value=""},M=()=>{w(),_()},T=()=>{b.value={email:"",password:"",remember:!1},v.value={firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1},c.value=!1,i.value=!1},I=async()=>{var G;if(M(),!b.value.email.trim()){l.value="Email address is required.",u.error("Email address is required.");return}if(!b.value.password){l.value="Password is required.",u.error("Password is required.");return}a.setLoading(!0);try{const V=(G=document.querySelector('meta[name="csrf-token"]'))==null?void 0:G.getAttribute("content");if(!V){const de="Security token not found. Please refresh the page.";l.value=de,u.error(de);return}const se=await fetch("/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":V},body:JSON.stringify({username:b.value.email,password:b.value.password,remember:b.value.remember,redirect:window.location.href})});if(se.ok){const de=await se.json();f.value="Login successful! Redirecting...",u.success("Login successful! Welcome back."),n("success",de.user||{email:b.value.email}),setTimeout(()=>{n("close"),n("update:show",!1),de.redirect?window.location.href=de.redirect:window.location.reload()},1e3)}else{const de=await se.json();let O="Invalid login credentials. Please check your email and password.";if(se.status===422&&de.errors){const E=[];for(const W in de.errors)E.push(...de.errors[W]);O=E.join(" ")}else de.message&&(O=de.message);l.value=O,u.error(O)}}catch(V){console.error("Login error:",V);let se="Login failed. Please try again.";V instanceof TypeError&&V.message.includes("fetch")?se="Network error. Please check your connection and try again.":V instanceof Error&&(se=V.message),l.value=se,u.error(se)}finally{a.setLoading(!1)}},U=async()=>{var G;if(M(),!v.value.firstName.trim()){l.value="First name is required.";return}if(!v.value.lastName.trim()){l.value="Last name is required.";return}if(!v.value.email.trim()){l.value="Email address is required.";return}if(!v.value.phone.trim()){l.value="Phone number is required.";return}if(!v.value.password){l.value="Password is required.";return}if(v.value.password.length<4){l.value="Password must be at least 4 characters long.";return}if(v.value.password!==v.value.passwordConfirmation){l.value="Passwords do not match.";return}if(!v.value.acceptTerms){l.value="You must accept the terms and conditions.";return}a.setLoading(!0);try{const V=(G=document.querySelector('meta[name="csrf-token"]'))==null?void 0:G.getAttribute("content"),se={name:`${v.value.firstName.trim()} ${v.value.lastName.trim()}`,email:v.value.email.trim(),phone:v.value.phone.trim(),password:v.value.password,password_confirmation:v.value.passwordConfirmation},de=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":V||""},body:JSON.stringify(se)}),O=await de.json();if(!de.ok){if(de.status===422&&O.errors){const E=[];for(const W in O.errors)E.push(...O.errors[W]);l.value=E.join(" ")}else l.value=O.message||"Registration failed. Please try again.";return}f.value="Account created successfully! Logging you in...",u.success("Account created successfully! Logging you in..."),b.value.email=v.value.email,b.value.password=v.value.password,await I()}catch(V){console.error("Registration error:",V);let se="Registration failed. Please try again.";V instanceof TypeError&&V.message.includes("fetch")?se="Network error. Please check your connection and try again.":V instanceof Error&&(se=V.message),l.value=se,u.error(se)}finally{a.setLoading(!1)}},te=()=>{M(),T(),h.value=r.startWithRegister,n("close"),n("update:show",!1)},X=()=>{r.closeOnBackdrop&&te()},oe=async()=>{if(!r.autoFocus)return;await Pt();const G=h.value?"register-firstName":"login-email",V=document.getElementById(G);V&&V.focus()};return Me(()=>r.startWithRegister,G=>{h.value=G}),Me(()=>r.show,async G=>{G&&(h.value=r.startWithRegister,w(),T(),r.autoFocus&&setTimeout(async()=>{await oe()},200))}),s({toggleForm:j,clearError:w,resetForms:T,autoFocusFirstInput:oe}),(G,V)=>(d(),ce(_o,{to:"body"},[z(gt,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:J(()=>[G.show?(d(),p("div",Pa,[e("div",Ra,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity backdrop-blur-sm",onClick:X}),z(gt,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to-class":"opacity-100 translate-y-0 sm:scale-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-y-0 sm:scale-100","leave-to-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:J(()=>[G.show?(d(),p("div",Na,[e("div",La,[G.closable?(d(),p("button",{key:0,type:"button",class:"absolute top-4 right-4 text-white/80 hover:text-white transition-colors duration-200 rounded-full p-2 hover:bg-white/10",onClick:te},[z(k(ks),{class:"h-5 w-5"})])):R("",!0),e("div",Da,[e("div",za,[z(k(Or),{class:"h-8 w-8 text-white"})])]),e("h2",Oa,A(g.value),1),e("p",Va,A(S.value),1)]),e("div",Fa,[e("div",qa,[z(gt,{mode:"out-in","enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-x-4","enter-to-class":"opacity-100 translate-x-0","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-x-0","leave-to-class":"opacity-0 -translate-x-4"},{default:J(()=>[h.value?h.value?(d(),p("div",ml,[e("form",{onSubmit:Ce(U,["prevent"]),class:"space-y-5"},[e("div",fl,[e("div",null,[V[21]||(V[21]=e("label",{for:"register-firstName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," First Name ",-1)),ve(e("input",{id:"register-firstName","onUpdate:modelValue":V[6]||(V[6]=se=>v.value.firstName=se),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"First name",disabled:x.value},null,8,pl),[[Re,v.value.firstName]])]),e("div",null,[V[22]||(V[22]=e("label",{for:"register-lastName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Last Name ",-1)),ve(e("input",{id:"register-lastName","onUpdate:modelValue":V[7]||(V[7]=se=>v.value.lastName=se),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Last name",disabled:x.value},null,8,gl),[[Re,v.value.lastName]])])]),e("div",null,[V[23]||(V[23]=e("label",{for:"register-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",hl,[e("div",vl,[z(k(Ns),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"register-email","onUpdate:modelValue":V[8]||(V[8]=se=>v.value.email=se),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:x.value},null,8,yl),[[Re,v.value.email]])])]),G.showPhoneField?(d(),p("div",bl,[V[24]||(V[24]=e("label",{for:"register-phone",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Phone Number ",-1)),e("div",xl,[e("div",wl,[z(k(zr),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"register-phone","onUpdate:modelValue":V[9]||(V[9]=se=>v.value.phone=se),type:"tel",required:!v.value.email,class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your phone number",disabled:x.value},null,8,_l),[[Re,v.value.phone]])])])):R("",!0),e("div",kl,[e("div",null,[V[25]||(V[25]=e("label",{for:"register-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",Cl,[e("div",$l,[z(k($t),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"register-password","onUpdate:modelValue":V[10]||(V[10]=se=>v.value.password=se),type:c.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Create a password",disabled:x.value},null,8,Sl),[[Kt,v.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[11]||(V[11]=se=>c.value=!c.value)},[c.value?(d(),ce(k(Yt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(d(),ce(k(Xt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",null,[V[26]||(V[26]=e("label",{for:"register-password-confirmation",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Confirm Password ",-1)),e("div",Ml,[e("div",Al,[z(k($t),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"register-password-confirmation","onUpdate:modelValue":V[12]||(V[12]=se=>v.value.passwordConfirmation=se),type:i.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Confirm your password",disabled:x.value},null,8,jl),[[Kt,v.value.passwordConfirmation]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[13]||(V[13]=se=>i.value=!i.value)},[i.value?(d(),ce(k(Yt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(d(),ce(k(Xt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])])]),e("div",El,[e("div",Il,[ve(e("input",{id:"accept-terms","onUpdate:modelValue":V[14]||(V[14]=se=>v.value.acceptTerms=se),type:"checkbox",required:"",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[nt,v.value.acceptTerms]])]),V[27]||(V[27]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"accept-terms",class:"text-gray-700"},[Q(" I agree to the "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Terms of Service "),Q(" and "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Privacy Policy ")])],-1))]),f.value?(d(),p("div",Tl,[e("div",Bl,[z(k(Rs),{class:"h-5 w-5 text-green-400"}),e("div",Pl,[e("p",Rl,A(f.value),1)])])])):R("",!0),l.value?(d(),p("div",Nl,[e("div",Ll,[z(k(Ls),{class:"h-5 w-5 text-red-400"}),e("div",Dl,[e("p",zl,A(l.value),1)])])])):R("",!0),e("button",{type:"submit",disabled:x.value||!P.value,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[x.value?(d(),p("span",Fl,V[28]||(V[28]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),Q(" Creating account... ")]))):(d(),p("span",Vl,"Create Account"))],8,Ol)],32),e("div",ql,[e("p",Ul,[V[29]||(V[29]=Q(" Already have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:V[15]||(V[15]=se=>h.value=!1)}," Sign in here ")])])])):R("",!0):(d(),p("div",Ua,[e("form",{onSubmit:Ce(I,["prevent"]),class:"space-y-5"},[e("div",null,[V[16]||(V[16]=e("label",{for:"login-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",Ha,[e("div",Wa,[z(k(Ns),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"login-email","onUpdate:modelValue":V[0]||(V[0]=se=>b.value.email=se),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:x.value},null,8,Ka),[[Re,b.value.email]])])]),e("div",null,[V[17]||(V[17]=e("label",{for:"login-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",Ya,[e("div",Xa,[z(k($t),{class:"h-5 w-5 text-gray-400"})]),ve(e("input",{id:"login-password","onUpdate:modelValue":V[1]||(V[1]=se=>b.value.password=se),type:c.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your password",disabled:x.value},null,8,Qa),[[Kt,b.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:V[2]||(V[2]=se=>c.value=!c.value)},[c.value?(d(),ce(k(Yt),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(d(),ce(k(Xt),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",Ga,[e("div",Ja,[ve(e("input",{id:"remember-me","onUpdate:modelValue":V[3]||(V[3]=se=>b.value.remember=se),type:"checkbox",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[nt,b.value.remember]]),V[18]||(V[18]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-700"}," Remember me ",-1))]),e("button",{type:"button",class:"text-sm text-slate-600 hover:text-slate-500 font-medium",onClick:V[4]||(V[4]=se=>G.$emit("forgot-password"))}," Forgot password? ")]),e("div",Za,[e("p",el,[V[19]||(V[19]=Q(" Don't have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:V[5]||(V[5]=se=>h.value=!0)}," Create one here ")])]),f.value?(d(),p("div",tl,[e("div",sl,[z(k(Rs),{class:"h-5 w-5 text-green-400"}),e("div",ol,[e("p",rl,A(f.value),1)])])])):R("",!0),l.value?(d(),p("div",nl,[e("div",al,[z(k(Ls),{class:"h-5 w-5 text-red-400"}),e("div",ll,[e("p",il,A(l.value),1)])])])):R("",!0),e("button",{type:"submit",disabled:x.value||!b.value.email||!b.value.password,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[x.value?(d(),p("span",cl,V[20]||(V[20]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),Q(" Signing in... ")]))):(d(),p("span",ul,"Sign In"))],8,dl)],32)]))]),_:1})])])])):R("",!0)]),_:1})])])):R("",!0)]),_:1})]))}});const Ae=(t,s)=>{const o=t.__vccOpts||t;for(const[r,n]of s)o[r]=n;return o},Lt=Ae(Hl,[["__scopeId","data-v-da7d539c"]]),Wl={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},Kl=ge({__name:"WatchlistNavIcon",setup(t){const s=_t(),o=be(),r=et(),n=L(!1),a=N(()=>{const c=window.location.pathname;return c==="/cart"||c.startsWith("/spa")||c.startsWith("/home-vue")||c.startsWith("/bid-dashboard")}),u=c=>{if(!o.isAuthenticated){c.preventDefault(),n.value=!0;return}a.value&&(c.preventDefault(),r.push("/bid-dashboard"))},h=async c=>{n.value=!1,await s.initializeWatchlist(),a.value?r.push("/bid-dashboard"):window.location.href="/bid-dashboard"};return(c,i)=>(d(),p(ue,null,[(d(),ce(Ne(a.value?"router-link":"a"),{to:a.value&&k(o).isAuthenticated?"/bid-dashboard":void 0,href:a.value||!k(o).isAuthenticated?void 0:"/bid-dashboard",onClick:u,class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200",title:k(o).isAuthenticated?k(s).watchlistCount>0?`${k(s).watchlistCount} items in watchlist`:"View your watchlist":"Sign in to view your watchlist"},{default:J(()=>[i[1]||(i[1]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)),k(o).isAuthenticated&&k(s).watchlistCount>0?(d(),p("span",Wl,A(k(s).watchlistCount>9?"9+":k(s).watchlistCount),1)):R("",!0)]),_:1,__:[1]},8,["to","href","title"])),z(Lt,{show:n.value,"onUpdate:show":i[0]||(i[0]=l=>n.value=l),title:"Sign in to view your watchlist",subtitle:"Track your favorite auction items and manage your bidding activity.",onSuccess:h},null,8,["show"])],64))}});const Yl=["disabled","aria-expanded"],Xl={class:"flex items-center w-full"},Ql={class:"flex items-center flex-1 min-w-0 pr-2"},Gl={key:0,class:"flex flex-wrap gap-1"},Jl=["onClick"],Zl={key:0,class:"text-xs text-gray-500"},ei={key:1,class:"truncate"},ti={key:2,class:"text-gray-500 truncate"},si={class:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},oi={key:0,class:"p-3 border-b border-gray-200"},ri={class:"relative"},ni=["placeholder"],ai={key:1,class:"p-2 border-b border-gray-200 flex justify-between"},li={key:0,class:"px-4 py-8 text-sm text-gray-500 text-center"},ii={key:1,class:"py-1"},di={key:0,class:"my-1 border-gray-200"},ui={key:1,class:"px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider"},ci=["checked","disabled","onChange"],mi={class:"flex-1 min-w-0"},fi={class:"font-medium truncate"},pi={key:0,class:"text-xs text-gray-500 truncate"},gi=["onClick","disabled"],hi={class:"flex-1 min-w-0"},vi={class:"font-medium truncate"},yi={key:0,class:"text-xs text-gray-500 truncate"},qo=ge({__name:"Select",props:{items:{default:()=>[]},modelValue:{},placeholder:{default:"Select an option..."},searchable:{type:Boolean,default:!1},searchPlaceholder:{default:"Search..."},multiSelect:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},maxHeight:{default:"max-h-64"},emptyMessage:{default:"No options found"},maxDisplayItems:{default:3},closeOnSelect:{type:Boolean,default:!0}},emits:["update:modelValue","change","search","open","close"],setup(t,{expose:s,emit:o}){const r=t,n=o,a=L(!1),u=L(""),h=L(),c=L(),i=L([]),l=N(()=>{const X="relative w-full cursor-default rounded-md border bg-white py-2 pl-3 pr-8 text-left shadow-sm transition-colors duration-200 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500",oe="border-gray-300 hover:border-gray-400",G="border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed";return[X,r.disabled?G:oe].join(" ")}),f=N(()=>"absolute z-50 mt-1 w-full rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"),b=N(()=>{if(!r.searchable||!u.value)return r.items;const X=u.value.toLowerCase();return r.items.filter(oe=>{var G,V;return oe.type==="divider"||oe.type==="header"?!0:((G=oe.label)==null?void 0:G.toLowerCase().includes(X))||((V=oe.description)==null?void 0:V.toLowerCase().includes(X))})}),v=N(()=>i.value.slice(0,r.maxDisplayItems)),x=()=>{r.disabled||(a.value?S():g())},g=async()=>{var X;a.value=!0,n("open"),r.searchable&&(await Pt(),(X=c.value)==null||X.focus())},S=()=>{a.value=!1,u.value="",n("close")},P=X=>i.value.some(oe=>oe.key===X.key),j=X=>{X.disabled||(i.value=[X],n("update:modelValue",X),n("change",X),r.closeOnSelect&&S())},w=(X,oe)=>{if(X.disabled)return;oe.target.checked?i.value.push(X):i.value=i.value.filter(V=>V.key!==X.key),n("update:modelValue",[...i.value]),n("change",[...i.value])},_=X=>{i.value=i.value.filter(oe=>oe.key!==X.key),n("update:modelValue",r.multiSelect?[...i.value]:null),n("change",[...i.value])},M=()=>{const X=b.value.filter(oe=>oe.type!=="divider"&&oe.type!=="header"&&!oe.disabled);i.value=[...X],n("update:modelValue",[...i.value]),n("change",[...i.value])},T=()=>{i.value=[],n("update:modelValue",r.multiSelect?[]:null),n("change",[])},I=X=>{X.stopPropagation()},U=X=>{var oe;(oe=h.value)!=null&&oe.contains(X.target)||S()},te=X=>{X.key==="Escape"&&a.value&&S()};return Me(()=>r.modelValue,X=>{r.multiSelect?i.value=Array.isArray(X)?X:[]:i.value=X?[X]:[]},{immediate:!0}),Me(u,X=>{n("search",X)}),Ie(()=>{document.addEventListener("click",U),document.addEventListener("keydown",te)}),wt(()=>{document.removeEventListener("click",U),document.removeEventListener("keydown",te)}),s({open:g,close:S,toggle:x,selectAll:M,clearAll:T,isOpen:()=>a.value}),(X,oe)=>(d(),p("div",{class:"relative",ref_key:"selectRef",ref:h},[e("div",{onClick:x},[ze(X.$slots,"trigger",{isOpen:a.value,toggle:x,selectedItems:i.value},()=>[e("button",{class:K(l.value),disabled:X.disabled,type:"button","aria-haspopup":"listbox","aria-expanded":a.value},[e("div",Xl,[e("div",Ql,[X.multiSelect&&i.value.length>0?(d(),p("div",Gl,[(d(!0),p(ue,null,fe(v.value,G=>(d(),p("span",{key:G.key,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800"},[Q(A(G.label)+" ",1),e("button",{onClick:Ce(V=>_(G),["stop"]),class:"ml-1 h-3 w-3 rounded-full hover:bg-primary-200 flex items-center justify-center"},[z(k(ks),{class:"h-2 w-2"})],8,Jl)]))),128)),i.value.length>X.maxDisplayItems?(d(),p("span",Zl," +"+A(i.value.length-X.maxDisplayItems)+" more ",1)):R("",!0)])):!X.multiSelect&&i.value.length>0?(d(),p("span",ei,A(i.value[0].label),1)):(d(),p("span",ti,A(X.placeholder),1))]),e("div",si,[z(k($o),{class:K(["h-4 w-4 transition-transform duration-200 text-gray-400",a.value?"rotate-180":""])},null,8,["class"])])])],10,Yl)])]),z(gt,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:J(()=>[a.value?(d(),p("div",{key:0,class:K(f.value),onClick:I},[X.searchable?(d(),p("div",oi,[e("div",ri,[z(k(Dr),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),ve(e("input",{ref_key:"searchInput",ref:c,"onUpdate:modelValue":oe[0]||(oe[0]=G=>u.value=G),type:"text",placeholder:X.searchPlaceholder,class:"w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",onClick:oe[1]||(oe[1]=Ce(()=>{},["stop"]))},null,8,ni),[[Re,u.value]])])])):R("",!0),X.multiSelect&&b.value.length>0?(d(),p("div",ai,[e("button",{onClick:M,class:"text-xs text-primary-600 hover:text-primary-800 font-medium"}," Select All "),e("button",{onClick:T,class:"text-xs text-gray-600 hover:text-gray-800 font-medium"}," Clear All ")])):R("",!0),e("div",{class:K(["overflow-auto",X.maxHeight])},[b.value.length===0?(d(),p("div",li,A(X.emptyMessage),1)):(d(),p("div",ii,[(d(!0),p(ue,null,fe(b.value,G=>(d(),p(ue,{key:G.key},[G.type==="divider"?(d(),p("hr",di)):G.type==="header"?(d(),p("div",ui,A(G.label),1)):X.multiSelect?(d(),p("label",{key:2,class:K(["flex items-center px-4 py-2 text-sm transition-colors duration-150 cursor-pointer",G.disabled?"text-gray-400 cursor-not-allowed":P(G)?"bg-primary-50 text-primary-900":"text-gray-700 hover:bg-gray-100"])},[e("input",{type:"checkbox",checked:P(G),disabled:G.disabled,onChange:V=>w(G,V),onClick:oe[2]||(oe[2]=Ce(()=>{},["stop"])),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-3"},null,40,ci),G.icon?(d(),ce(Ne(G.icon),{key:0,class:"w-4 h-4 mr-3 flex-shrink-0"})):R("",!0),e("div",mi,[e("div",fi,A(G.label),1),G.description?(d(),p("div",pi,A(G.description),1)):R("",!0)])],2)):(d(),p("button",{key:3,class:K(["w-full flex items-center px-4 py-2 text-sm transition-colors duration-150 text-left",G.disabled?"text-gray-400 cursor-not-allowed":P(G)?"bg-primary-50 text-primary-900":G.danger?"text-red-700 hover:bg-red-50":"text-gray-700 hover:bg-gray-100"]),onClick:V=>j(G),disabled:G.disabled},[G.icon?(d(),ce(Ne(G.icon),{key:0,class:"w-4 h-4 mr-3 flex-shrink-0"})):R("",!0),e("div",hi,[e("div",vi,A(G.label),1),G.description?(d(),p("div",yi,A(G.description),1)):R("",!0)]),P(G)?(d(),ce(k(Rr),{key:1,class:"w-4 h-4 ml-3 flex-shrink-0 text-primary-600"})):R("",!0)],10,gi))],64))),128))]))],2)],2)):R("",!0)]),_:1})],512))}}),it=ge({__name:"Badge",props:{text:{default:""},variant:{default:"default"},size:{default:"sm"},rounded:{type:Boolean,default:!1},outlined:{type:Boolean,default:!1},closable:{type:Boolean,default:!1},icon:{},iconPosition:{default:"left"},dot:{type:Boolean,default:!1}},emits:["close"],setup(t,{emit:s}){const o=t,r=s,n=N(()=>{const c="inline-flex items-center font-medium transition-colors duration-200",i={xs:"px-2 py-0.5 text-xs",sm:"px-2.5 py-0.5 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-1.5 text-sm"},l={default:o.outlined?"text-gray-700 bg-white border border-gray-300":"text-gray-800 bg-gray-100",primary:o.outlined?"text-primary-700 bg-white border border-primary-300":"text-primary-800 bg-primary-100",secondary:o.outlined?"text-brand-700 bg-white border border-brand-300":"text-brand-800 bg-brand-100",success:o.outlined?"text-green-700 bg-white border border-green-300":"text-green-800 bg-green-100",warning:o.outlined?"text-yellow-700 bg-white border border-yellow-300":"text-yellow-800 bg-yellow-100",danger:o.outlined?"text-red-700 bg-white border border-red-300":"text-red-800 bg-red-100",info:o.outlined?"text-blue-700 bg-white border border-blue-300":"text-blue-800 bg-blue-100"},f=o.rounded?"rounded-full":"rounded-md",b=o.dot?"w-2 h-2 rounded-full":"";return[c,o.dot?b:i[o.size],l[o.variant],f].filter(Boolean).join(" ")}),a=N(()=>{const c={xs:"h-3 w-3",sm:"h-3 w-3",md:"h-4 w-4",lg:"h-4 w-4"},i=o.iconPosition==="left"?"mr-1":"ml-1";return[c[o.size],i].join(" ")}),u=N(()=>{const c="ml-1 inline-flex items-center justify-center rounded-full hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2",i={xs:"h-4 w-4",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-5 w-5"},l={default:"text-gray-500 hover:text-gray-700 focus:ring-gray-500",primary:"text-primary-600 hover:text-primary-800 focus:ring-primary-500",secondary:"text-brand-600 hover:text-brand-800 focus:ring-brand-500",success:"text-green-600 hover:text-green-800 focus:ring-green-500",warning:"text-yellow-600 hover:text-yellow-800 focus:ring-yellow-500",danger:"text-red-600 hover:text-red-800 focus:ring-red-500",info:"text-blue-600 hover:text-blue-800 focus:ring-blue-500"};return[c,i[o.size],l[o.variant]].join(" ")}),h=()=>{r("close")};return(c,i)=>(d(),p("span",{class:K(n.value)},[c.icon&&c.iconPosition==="left"?(d(),ce(Ne(c.icon),{key:0,class:K(a.value)},null,8,["class"])):R("",!0),ze(c.$slots,"default",{},()=>[Q(A(c.text),1)]),c.icon&&c.iconPosition==="right"?(d(),ce(Ne(c.icon),{key:1,class:K(a.value)},null,8,["class"])):R("",!0),c.closable?(d(),p("button",{key:2,onClick:h,class:K(u.value),type:"button"},[z(k(ks),{class:"h-3 w-3"})],2)):R("",!0)],2))}}),bi={class:"flex items-center justify-between","aria-label":"Pagination"},xi={class:"flex flex-1 justify-between sm:hidden"},wi=["disabled"],_i={class:"text-sm text-gray-700 flex items-center"},ki=["disabled"],Ci={class:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between"},$i={key:0},Si={class:"text-sm text-gray-700"},Mi={class:"font-medium"},Ai={class:"font-medium"},ji={class:"font-medium"},Ei={class:"flex items-center space-x-2"},Ii=["disabled"],Ti={class:"flex items-center space-x-1"},Bi={key:1,class:"px-3 py-2 text-sm text-gray-500"},Pi=["onClick"],Ri={key:2,class:"px-3 py-2 text-sm text-gray-500"},Ni=["disabled"],Li={key:0,class:"flex items-center space-x-2 mt-4 sm:mt-0"},Di=["value"],zi=["value"],Uo=ge({__name:"Pagination",props:{currentPage:{},totalPages:{},total:{default:0},perPage:{default:10},maxVisiblePages:{default:5},showInfo:{type:Boolean,default:!0},showFirstLast:{type:Boolean,default:!0},showPageSize:{type:Boolean,default:!1},pageSizeOptions:{default:()=>[10,25,50,100]}},emits:["page-change","page-size-change"],setup(t,{emit:s}){const o=t,r=s,n=N(()=>(o.currentPage-1)*o.perPage+1),a=N(()=>Math.min(o.currentPage*o.perPage,o.total)),u=N(()=>{const l=[],f=Math.floor(o.maxVisiblePages/2);let b=Math.max(1,o.currentPage-f),v=Math.min(o.totalPages,b+o.maxVisiblePages-1);v-b+1<o.maxVisiblePages&&(b=Math.max(1,v-o.maxVisiblePages+1));for(let x=b;x<=v;x++)l.push(x);return l}),h=l=>{const f="relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",b="bg-primary-500 text-white border border-primary-500",v="text-gray-700 bg-white border border-gray-300 hover:bg-gray-50";return[f,l===o.currentPage?b:v].join(" ")},c=l=>{l>=1&&l<=o.totalPages&&l!==o.currentPage&&r("page-change",l)},i=l=>{const f=l.target,b=parseInt(f.value);r("page-size-change",b)};return(l,f)=>(d(),p("nav",bi,[e("div",xi,[e("button",{disabled:l.currentPage<=1,onClick:f[0]||(f[0]=b=>c(l.currentPage-1)),class:K(["relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200",l.currentPage<=1?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"])}," Previous ",10,wi),e("span",_i," Page "+A(l.currentPage)+" of "+A(l.totalPages),1),e("button",{disabled:l.currentPage>=l.totalPages,onClick:f[1]||(f[1]=b=>c(l.currentPage+1)),class:K(["relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200",l.currentPage>=l.totalPages?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"])}," Next ",10,ki)]),e("div",Ci,[l.showInfo?(d(),p("div",$i,[e("p",Si,[f[6]||(f[6]=Q(" Showing ")),e("span",Mi,A(n.value),1),f[7]||(f[7]=Q(" to ")),e("span",Ai,A(a.value),1),f[8]||(f[8]=Q(" of ")),e("span",ji,A(l.total),1),f[9]||(f[9]=Q(" results "))])])):R("",!0),e("div",Ei,[e("button",{disabled:l.currentPage<=1,onClick:f[2]||(f[2]=b=>c(l.currentPage-1)),class:K(["relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",l.currentPage<=1?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"])},[z(k(So),{class:"h-4 w-4 mr-1"}),f[10]||(f[10]=Q(" Previous "))],10,Ii),e("div",Ti,[l.showFirstLast&&l.currentPage>l.maxVisiblePages?(d(),p("button",{key:0,onClick:f[3]||(f[3]=b=>c(1)),class:K(h(1))}," 1 ",2)):R("",!0),l.showFirstLast&&l.currentPage>l.maxVisiblePages+1?(d(),p("span",Bi," ... ")):R("",!0),(d(!0),p(ue,null,fe(u.value,b=>(d(),p("button",{key:b,onClick:v=>c(b),class:K(h(b))},A(b),11,Pi))),128)),l.showFirstLast&&l.currentPage<l.totalPages-l.maxVisiblePages?(d(),p("span",Ri," ... ")):R("",!0),l.showFirstLast&&l.currentPage<l.totalPages-l.maxVisiblePages+1?(d(),p("button",{key:3,onClick:f[4]||(f[4]=b=>c(l.totalPages)),class:K(h(l.totalPages))},A(l.totalPages),3)):R("",!0)]),e("button",{disabled:l.currentPage>=l.totalPages,onClick:f[5]||(f[5]=b=>c(l.currentPage+1)),class:K(["relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200",l.currentPage>=l.totalPages?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"])},[f[11]||(f[11]=Q(" Next ")),z(k(Mo),{class:"h-4 w-4 ml-1"})],10,Ni)])]),l.showPageSize?(d(),p("div",Li,[f[12]||(f[12]=e("label",{class:"text-sm text-gray-700"},"Show:",-1)),e("select",{value:l.perPage,onChange:i,class:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"},[(d(!0),p(ue,null,fe(l.pageSizeOptions,b=>(d(),p("option",{key:b,value:b},A(b),9,zi))),128))],40,Di),f[13]||(f[13]=e("span",{class:"text-sm text-gray-700"},"per page",-1))])):R("",!0)]))}}),Oi={key:0,class:"max-w-sm w-full"},Vi={class:"flex items-start"},Fi={class:"flex-shrink-0"},qi={key:0,class:"h-5 w-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ui={key:1,class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Hi={key:2,class:"h-5 w-5 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Wi={key:3,class:"h-5 w-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ki={class:"ml-3 flex-1"},Yi={class:"ml-4 flex-shrink-0"},Xi=ge({__name:"Notification",props:{type:{default:"info"},title:{},message:{},duration:{default:5e3},persistent:{type:Boolean,default:!1}},emits:["close"],setup(t,{emit:s}){const o=t,r=s,n=L(!1);let a=null;const u=()=>{n.value=!1,a&&clearTimeout(a),setTimeout(()=>{r("close")},300)};return Ie(()=>{n.value=!0,!o.persistent&&o.duration>0&&(a=window.setTimeout(()=>{u()},o.duration))}),wt(()=>{a&&clearTimeout(a)}),(h,c)=>n.value?(d(),p("div",Oi,[e("div",{class:K(["rounded-lg shadow-lg p-4 transition-all duration-300 transform",{"bg-green-50 border border-green-200":h.type==="success","bg-red-50 border border-red-200":h.type==="error","bg-blue-50 border border-blue-200":h.type==="info","bg-yellow-50 border border-yellow-200":h.type==="warning","translate-x-0 opacity-100":n.value,"translate-x-full opacity-0":!n.value}])},[e("div",Vi,[e("div",Fi,[h.type==="success"?(d(),p("svg",qi,c[0]||(c[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):h.type==="error"?(d(),p("svg",Ui,c[1]||(c[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))):h.type==="warning"?(d(),p("svg",Hi,c[2]||(c[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]))):(d(),p("svg",Wi,c[3]||(c[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),e("div",Ki,[h.title?(d(),p("h3",{key:0,class:K(["text-sm font-medium",{"text-green-800":h.type==="success","text-red-800":h.type==="error","text-blue-800":h.type==="info","text-yellow-800":h.type==="warning"}])},A(h.title),3)):R("",!0),e("p",{class:K(["text-sm",{"text-green-700":h.type==="success","text-red-700":h.type==="error","text-blue-700":h.type==="info","text-yellow-700":h.type==="warning","mt-1":h.title}])},A(h.message),3)]),e("div",Yi,[e("button",{onClick:u,class:K(["inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2",{"text-green-500 hover:bg-green-100 focus:ring-green-600":h.type==="success","text-red-500 hover:bg-red-100 focus:ring-red-600":h.type==="error","text-blue-500 hover:bg-blue-100 focus:ring-blue-600":h.type==="info","text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600":h.type==="warning"}])},c[4]||(c[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),2)])])],2)])):R("",!0)}}),Qi={class:"fixed bottom-4 right-4 z-50 space-y-2 max-w-sm w-full flex flex-col-reverse"},Gi=ge({__name:"NotificationContainer",setup(t){const s=Le();return(o,r)=>(d(),ce(_o,{to:"body"},[e("div",Qi,[z(Ar,{name:"notification",tag:"div",class:"space-y-2 flex flex-col-reverse"},{default:J(()=>[(d(!0),p(ue,null,fe(k(s).notifications,n=>(d(),ce(Xi,{key:n.id,type:n.type,title:n.title,message:n.message,duration:n.duration,persistent:n.persistent,onClose:a=>k(s).removeNotification(n.id)},null,8,["type","title","message","duration","persistent","onClose"]))),128))]),_:1})])]))}});const Ho=Ae(Gi,[["__scopeId","data-v-dc71a709"]]);const Ji=["aria-live"],Zi={key:0,class:"flex-shrink-0"},ed={class:"flex-1 min-w-0"},td={key:1,class:"mt-3"},sd={class:"flex space-x-3"},od=["onClick"],rd={key:1,class:"flex-shrink-0 ml-4"},nd=ge({__name:"Alert",props:{variant:{default:"info"},title:{},message:{},closable:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},autoClose:{type:Boolean,default:!1},autoCloseDelay:{default:5e3},actions:{default:()=>[]}},emits:["close","action"],setup(t,{expose:s,emit:o}){const r=t,n=o,a=L(!0),u=N(()=>{const g=["flex","p-4","rounded-lg","border","shadow-sm"],S={info:["bg-blue-50","border-blue-200","text-blue-800"],success:["bg-green-50","border-green-200","text-green-800"],warning:["bg-yellow-50","border-yellow-200","text-yellow-800"],error:["bg-red-50","border-red-200","text-red-800"]};return[...g,...S[r.variant]].join(" ")}),h=N(()=>({info:"InfoIcon",success:"CheckCircleIcon",warning:"ExclamationTriangleIcon",error:"XCircleIcon"})[r.variant]),c=N(()=>{const g=["w-5","h-5"],S={info:["text-blue-400"],success:["text-green-400"],warning:["text-yellow-400"],error:["text-red-400"]};return[...g,...S[r.variant]].join(" ")}),i=N(()=>{const g=["text-sm","font-medium"],S={info:["text-blue-800"],success:["text-green-800"],warning:["text-yellow-800"],error:["text-red-800"]};return[...g,...S[r.variant]].join(" ")}),l=N(()=>{const g=["text-sm"];r.title&&g.push("mt-1");const S={info:["text-blue-700"],success:["text-green-700"],warning:["text-yellow-700"],error:["text-red-700"]};return[...g,...S[r.variant]].join(" ")}),f=N(()=>{const g=["inline-flex","rounded-md","p-1.5","focus:outline-none","focus:ring-2","focus:ring-offset-2","transition-colors"],S={info:["text-blue-500","hover:bg-blue-100","focus:ring-blue-600","focus:ring-offset-blue-50"],success:["text-green-500","hover:bg-green-100","focus:ring-green-600","focus:ring-offset-green-50"],warning:["text-yellow-500","hover:bg-yellow-100","focus:ring-yellow-600","focus:ring-offset-yellow-50"],error:["text-red-500","hover:bg-red-100","focus:ring-red-600","focus:ring-offset-red-50"]};return[...g,...S[r.variant]].join(" ")}),b=(g="secondary")=>{const S=["text-sm","font-medium","px-3","py-1.5","rounded-md","focus:outline-none","focus:ring-2","focus:ring-offset-2","transition-colors"];if(g==="primary"){const j={info:["bg-blue-600","text-white","hover:bg-blue-700","focus:ring-blue-500"],success:["bg-green-600","text-white","hover:bg-green-700","focus:ring-green-500"],warning:["bg-yellow-600","text-white","hover:bg-yellow-700","focus:ring-yellow-500"],error:["bg-red-600","text-white","hover:bg-red-700","focus:ring-red-500"]};return[...S,...j[r.variant]].join(" ")}const P={info:["bg-blue-100","text-blue-800","hover:bg-blue-200","focus:ring-blue-500"],success:["bg-green-100","text-green-800","hover:bg-green-200","focus:ring-green-500"],warning:["bg-yellow-100","text-yellow-800","hover:bg-yellow-200","focus:ring-yellow-500"],error:["bg-red-100","text-red-800","hover:bg-red-200","focus:ring-red-500"]};return[...S,...P[r.variant]].join(" ")},v=()=>{a.value=!1,n("close")},x=g=>{g.handler(),n("action",g)};return Ie(()=>{r.autoClose&&r.autoCloseDelay>0&&setTimeout(()=>{v()},r.autoCloseDelay)}),s({close:v,show:()=>{a.value=!0},isVisible:()=>a.value}),(g,S)=>(d(),ce(gt,{name:"alert","enter-active-class":"transition-all duration-300 ease-out","enter-from-class":"opacity-0 transform scale-95","enter-to-class":"opacity-100 transform scale-100","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 transform scale-100","leave-to-class":"opacity-0 transform scale-95"},{default:J(()=>[a.value?(d(),p("div",{key:0,class:K(u.value),role:"alert","aria-live":g.variant==="error"?"assertive":"polite"},[g.showIcon?(d(),p("div",Zi,[(d(),ce(Ne(h.value),{class:K(c.value)},null,8,["class"]))])):R("",!0),e("div",ed,[g.title?(d(),p("h4",{key:0,class:K(i.value)},A(g.title),3)):R("",!0),e("div",{class:K(l.value)},[ze(g.$slots,"default",{},()=>[Q(A(g.message),1)],!0)],2),g.$slots.actions||g.actions.length>0?(d(),p("div",td,[e("div",sd,[ze(g.$slots,"actions",{},()=>[(d(!0),p(ue,null,fe(g.actions,P=>(d(),p("button",{key:P.label,class:K(b(P.variant)),onClick:j=>x(P)},A(P.label),11,od))),128))],!0)])])):R("",!0)]),g.closable?(d(),p("div",rd,[e("button",{class:K(f.value),onClick:v,"aria-label":"Close alert"},S[0]||(S[0]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),2)])):R("",!0)],10,Ji)):R("",!0)]),_:3}))}});const fs=Ae(nd,[["__scopeId","data-v-c0a21131"]]);const ad={key:6,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},ld={class:"text-center"},id=ge({__name:"Loading",props:{type:{default:"spinner"},size:{default:"md"},color:{default:"primary"},text:{},overlay:{type:Boolean,default:!1},centered:{type:Boolean,default:!1},inline:{type:Boolean,default:!1}},setup(t){const s=t,o=N(()=>{const v=["loading-container"];return s.overlay&&v.push("fixed","inset-0","z-50","bg-white","bg-opacity-75"),s.centered&&!s.overlay&&v.push("flex","items-center","justify-center"),s.inline?v.push("inline-flex","items-center"):s.overlay||v.push("flex","flex-col","items-center"),v.join(" ")}),r=N(()=>({xs:"w-4 h-4",sm:"w-6 h-6",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"})[s.size]),n=N(()=>({primary:"text-indigo-600",secondary:"text-gray-600",success:"text-green-600",warning:"text-yellow-600",error:"text-red-600",white:"text-white",gray:"text-gray-400"})[s.color]),a=N(()=>[r.value,n.value].join(" ")),u=N(()=>["flex","space-x-1",n.value].join(" ")),h=N(()=>[{xs:"w-1 h-1",sm:"w-1.5 h-1.5",md:"w-2 h-2",lg:"w-3 h-3",xl:"w-4 h-4"}[s.size],"bg-current","rounded-full","animate-bounce"].join(" ")),c=N(()=>[r.value,n.value].join(" ")),i=N(()=>["flex","space-x-1","items-end",n.value].join(" ")),l=N(()=>{const v={xs:"w-0.5",sm:"w-1",md:"w-1",lg:"w-1.5",xl:"w-2"},x={xs:"h-3",sm:"h-4",md:"h-6",lg:"h-8",xl:"h-12"};return[v[s.size],x[s.size],"bg-current","animate-pulse"].join(" ")}),f=N(()=>[r.value,n.value].join(" ")),b=N(()=>{const v=["text-sm","font-medium",n.value];return s.inline?v.push("ml-2"):v.push("mt-2"),v.join(" ")});return(v,x)=>(d(),p("div",{class:K(o.value)},[v.type==="spinner"?(d(),p("div",{key:0,class:K(a.value)},x[0]||(x[0]=[e("svg",{class:"animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]),2)):v.type==="dots"?(d(),p("div",{key:1,class:K(u.value)},[(d(),p(ue,null,fe(3,g=>e("div",{key:g,class:K(h.value),style:Ge({animationDelay:`${(g-1)*.2}s`})},null,6)),64))],2)):v.type==="pulse"?(d(),p("div",{key:2,class:K(c.value)},x[1]||(x[1]=[e("div",{class:"animate-pulse bg-current rounded-full"},null,-1)]),2)):v.type==="bars"?(d(),p("div",{key:3,class:K(i.value)},[(d(),p(ue,null,fe(4,g=>e("div",{key:g,class:K(l.value),style:Ge({animationDelay:`${(g-1)*.1}s`})},null,6)),64))],2)):v.type==="ring"?(d(),p("div",{key:4,class:K(f.value)},x[2]||(x[2]=[e("div",{class:"animate-spin rounded-full border-4 border-current border-t-transparent"},null,-1)]),2)):R("",!0),v.text?(d(),p("div",{key:5,class:K(b.value)},A(v.text),3)):R("",!0),v.overlay?(d(),p("div",ad,[e("div",ld,[(d(),ce(Ne(v.$options.components.LoadingSpinner),jr(v.$props,{overlay:!1}),null,16))])])):R("",!0)],2))}});const tt=Ae(id,[["__scopeId","data-v-ce9f76c3"]]);const dd=["aria-selected","aria-controls","id","disabled","onClick","onKeydown"],ud=["onClick","aria-label"],cd=["id","aria-labelledby","tabindex"],md=["innerHTML"],fd=ge({__name:"Tabs",props:{tabs:{},modelValue:{default:0},variant:{default:"default"},size:{default:"md"},vertical:{type:Boolean,default:!1},centered:{type:Boolean,default:!1},addable:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1}},emits:["update:modelValue","tab-change","tab-close","tab-add"],setup(t,{expose:s,emit:o}){const r=t,n=o,a=L(r.modelValue);Me(()=>r.modelValue,w=>{a.value=w}),Me(a,w=>{n("update:modelValue",w),n("tab-change",w,r.tabs[w])});const u=N(()=>{const w=["tabs-container"];return r.vertical&&w.push("flex"),w.join(" ")}),h=N(()=>{const w=["flex","tab-list"];r.vertical?w.push("flex-col","space-y-1","mr-4"):w.push("space-x-1"),r.centered&&!r.vertical&&w.push("justify-center");const _={default:["border-b","border-gray-200"],pills:[],underline:["border-b","border-gray-200"],bordered:["border","border-gray-200","rounded-lg","p-1","bg-gray-50"]};return[...w,..._[r.variant]].join(" ")}),c=w=>{const _=a.value===w,M=r.tabs[w],T=["flex","items-center","space-x-2","font-medium","transition-all","duration-200","focus:outline-none"];r.variant!=="underline"&&T.push("focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500");const I={sm:["px-3","py-1.5","text-sm"],md:["px-4","py-2","text-sm"],lg:["px-6","py-3","text-base"]},U={default:_?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],pills:_?["bg-indigo-100","text-indigo-700","rounded-lg"]:["text-gray-500","hover:text-gray-700","hover:bg-gray-100","rounded-lg"],underline:_?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],bordered:_?["bg-white","text-indigo-700","shadow-sm","rounded-md"]:["text-gray-500","hover:text-gray-700","hover:bg-white","rounded-md"]},te=M.disabled?["opacity-50","cursor-not-allowed","pointer-events-none"]:["cursor-pointer"];return[...T,...I[r.size],...U[r.variant],...te].join(" ")},i=w=>{const _=a.value===w,M=["w-4","h-4"];return _?M.push("text-current"):M.push("text-gray-400"),M.join(" ")},l=w=>{const _=a.value===w,M=["inline-flex","items-center","justify-center","px-2","py-0.5","rounded-full","text-xs","font-medium","min-w-[1.25rem]","h-5"];return _?M.push("bg-indigo-100","text-indigo-800"):M.push("bg-gray-100","text-gray-600"),M.join(" ")},f=w=>{const _=a.value===w,M=["ml-2","p-0.5","rounded","hover:bg-gray-200","focus:outline-none","focus:ring-1","focus:ring-gray-400"];return _?M.push("text-indigo-500"):M.push("text-gray-400"),M.join(" ")},b=N(()=>["flex","items-center","justify-center","w-8","h-8","text-gray-400","hover:text-gray-600","hover:bg-gray-100","rounded","transition-colors","duration-200","focus:outline-none","focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500"].join(" ")),v=N(()=>{const w=["tab-panels"];return r.vertical?w.push("flex-1"):w.push("mt-4"),w.join(" ")}),x=N(()=>["tab-panel","focus:outline-none"].join(" ")),g=w=>{var _;(_=r.tabs[w])!=null&&_.disabled||(a.value=w)},S=w=>{const _=r.tabs[w];if(n("tab-close",w,_),a.value===w){const M=w>0?w-1:0;r.tabs.length>1&&Pt(()=>{a.value=Math.min(M,r.tabs.length-2)})}else a.value>w&&a.value--},P=()=>{n("tab-add")},j=(w,_)=>{const{key:M}=w;if(M==="ArrowLeft"||M==="ArrowUp"){w.preventDefault();const T=_>0?_-1:r.tabs.length-1;g(T)}else if(M==="ArrowRight"||M==="ArrowDown"){w.preventDefault();const T=_<r.tabs.length-1?_+1:0;g(T)}else M==="Home"?(w.preventDefault(),g(0)):M==="End"&&(w.preventDefault(),g(r.tabs.length-1))};return s({selectTab:g,closeTab:S,addTab:P,activeTab:()=>a.value}),(w,_)=>(d(),p("div",{class:K(u.value)},[e("div",{class:K(h.value),role:"tablist"},[(d(!0),p(ue,null,fe(w.tabs,(M,T)=>(d(),p("button",{key:M.key||T,class:K(c(T)),"aria-selected":a.value===T,"aria-controls":`tabpanel-${M.key||T}`,id:`tab-${M.key||T}`,role:"tab",disabled:M.disabled,onClick:I=>g(T),onKeydown:I=>j(I,T)},[M.icon?(d(),ce(Ne(M.icon),{key:0,class:K(i(T))},null,8,["class"])):R("",!0),e("span",null,A(M.label),1),M.badge!==void 0?(d(),p("span",{key:1,class:K(l(T))},A(M.badge),3)):R("",!0),M.closable?(d(),p("button",{key:2,class:K(f(T)),onClick:Ce(I=>S(T),["stop"]),"aria-label":`Close ${M.label} tab`},_[0]||(_[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,ud)):R("",!0)],42,dd))),128)),w.addable?(d(),p("button",{key:0,class:K(b.value),onClick:P,"aria-label":"Add new tab"},_[1]||(_[1]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)):R("",!0)],2),e("div",{class:K(v.value)},[(d(!0),p(ue,null,fe(w.tabs,(M,T)=>ve((d(),p("div",{key:`panel-${M.key||T}`,class:K(x.value),id:`tabpanel-${M.key||T}`,"aria-labelledby":`tab-${M.key||T}`,role:"tabpanel",tabindex:a.value===T?0:-1},[ze(w.$slots,M.key||`tab-${T}`,{tab:M,index:T,active:a.value===T},()=>[M.content?(d(),p("div",{key:0,innerHTML:M.content},null,8,md)):R("",!0)],!0)],10,cd)),[[Er,a.value===T]])),128))],2)],2))}});const pd=Ae(fd,[["__scopeId","data-v-c55ef745"]]),gd={key:0,class:"banner-section"},hd={class:"banner-container relative overflow-hidden"},vd={class:"relative"},yd={href:"/register-bid",class:"block w-full"},bd=["src","alt"],xd={key:0,class:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center"},wd={class:"container mx-auto px-4"},_d={class:"grid grid-cols-1 md:grid-cols-3 gap-4 items-center text-white"},kd={class:"text-center"},Cd={class:"countdown-timer"},$d={class:"grid grid-cols-4 gap-2 text-center"},Sd={class:"countdown-item"},Md={class:"text-3xl font-bold"},Ad={class:"countdown-item"},jd={class:"text-3xl font-bold"},Ed={class:"countdown-item"},Id={class:"text-3xl font-bold"},Td={class:"countdown-item"},Bd={class:"text-3xl font-bold"},Pd={class:"text-center md:text-right"},Rd={class:"flex items-center justify-center md:justify-end space-x-2"},Nd={class:"text-sm"},Ld={key:1,class:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6"},Dd={class:"text-white text-center"},zd={class:"flex items-center justify-center space-x-2"},Od={key:0,class:"absolute bottom-4 left-1/2 transform -translate-x-1/2"},Vd={class:"flex space-x-2"},Fd=["onClick"],qd=ge({__name:"Banner",props:{adverts:{}},setup(t){const s=t,o=L(new Date);let r=null;const n=L(0),a=N(()=>{if(!s.adverts||s.adverts.length===0)return null;const i=n.value<s.adverts.length?n.value:0;return s.adverts[i]}),u=N(()=>{if(!a.value||!a.value.date_to)return{days:0,hours:0,minutes:0,seconds:0};const l=new Date(a.value.date_to).getTime()-o.value.getTime();if(l<=0)return{days:0,hours:0,minutes:0,seconds:0};const f=Math.floor(l/(1e3*60*60*24)),b=Math.floor(l%(1e3*60*60*24)/(1e3*60*60)),v=Math.floor(l%(1e3*60*60)/(1e3*60)),x=Math.floor(l%(1e3*60)/1e3);return{days:f,hours:b,minutes:v,seconds:x}}),h=()=>{o.value=new Date},c=i=>{i>=0&&i<s.adverts.length&&(n.value=i)};return Ie(()=>{r=setInterval(h,1e3)}),wt(()=>{r&&clearInterval(r)}),(i,l)=>i.adverts&&i.adverts.length>0&&a.value?(d(),p("div",gd,[e("div",hd,[e("div",vd,[e("a",yd,[e("img",{src:a.value.image,alt:a.value.description||"Advertisement",class:"w-full h-auto object-cover",style:{"max-height":"400px"}},null,8,bd)]),a.value.date_to?(d(),p("div",xd,[e("div",wd,[e("div",_d,[l[5]||(l[5]=e("div",{class:"hidden md:block"},null,-1)),e("div",kd,[e("div",Cd,[e("div",$d,[e("div",Sd,[e("div",Md,A(u.value.days),1),l[0]||(l[0]=e("div",{class:"text-sm uppercase tracking-wide"},"Days",-1))]),e("div",Ad,[e("div",jd,A(u.value.hours),1),l[1]||(l[1]=e("div",{class:"text-sm uppercase tracking-wide"},"Hours",-1))]),e("div",Ed,[e("div",Id,A(u.value.minutes),1),l[2]||(l[2]=e("div",{class:"text-sm uppercase tracking-wide"},"Minutes",-1))]),e("div",Td,[e("div",Bd,A(u.value.seconds),1),l[3]||(l[3]=e("div",{class:"text-sm uppercase tracking-wide"},"Seconds",-1))])])])]),e("div",Pd,[e("div",Rd,[l[4]||(l[4]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",Nd,A(a.value.description),1)])])])])])):a.value.description?(d(),p("div",Ld,[e("div",Dd,[e("div",zd,[l[6]||(l[6]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",null,A(a.value.description),1)])])])):R("",!0)]),i.adverts.length>1?(d(),p("div",Od,[e("div",Vd,[(d(!0),p(ue,null,fe(i.adverts,(f,b)=>(d(),p("button",{key:f.id,onClick:v=>c(b),class:K(["w-3 h-3 rounded-full transition-all duration-300",n.value===b?"bg-white":"bg-white bg-opacity-50 hover:bg-opacity-75"])},null,10,Fd))),128))])])):R("",!0)])])):R("",!0)}});const Wo=Ae(qd,[["__scopeId","data-v-4bed1f7b"]]),Ud={class:"relative"},Hd={key:0,class:"flex justify-center items-center mt-6 space-x-4"},Wd=["disabled"],Kd={class:"flex space-x-2"},Yd=["onClick","aria-label"],Xd=["disabled"],Qd={key:1,class:"text-center mt-2 text-sm text-gray-500"},Gd=50,Jd=ge({__name:"HorizontalSlider",props:{items:{},autoPlay:{type:Boolean,default:!1},autoPlayInterval:{default:5e3},showCounter:{type:Boolean,default:!0},containerClasses:{default:""},navigationButtonClasses:{default:""},dotClasses:{default:""},keyExtractor:{type:Function,default:(t,s)=>s}},setup(t){const s=t,o=L(),r=L(0),n=L(null),a=L(0),u=L(0),h=(w,_)=>s.keyExtractor(w,_),c=w=>{w>=0&&w<s.items.length&&(r.value=w,v())},i=()=>{r.value<s.items.length-1&&(r.value++,v())},l=()=>{r.value>0&&(r.value--,v())},f=()=>{!s.autoPlay||s.items.length<=1||(n.value=setInterval(()=>{r.value<s.items.length-1?r.value++:r.value=0},s.autoPlayInterval))},b=()=>{n.value&&(clearInterval(n.value),n.value=null)},v=()=>{s.autoPlay&&(b(),f())},x=w=>{a.value=w.touches[0].clientX},g=w=>{w.preventDefault()},S=w=>{u.value=w.changedTouches[0].clientX,P()},P=()=>{const w=a.value-u.value;Math.abs(w)>Gd&&(w>0?i():l())},j=w=>{switch(w.key){case"ArrowLeft":w.preventDefault(),l();break;case"ArrowRight":w.preventDefault(),i();break;case"Home":w.preventDefault(),c(0);break;case"End":w.preventDefault(),c(s.items.length-1);break}};return Me(()=>s.items,()=>{r.value=0,v()},{deep:!0}),Ie(()=>{s.autoPlay&&f()}),wt(()=>{b()}),(w,_)=>(d(),p("div",Ud,[e("div",{ref_key:"sliderContainer",ref:o,class:K(["overflow-hidden focus:outline-none",w.containerClasses]),tabindex:"0",onKeydown:j,onTouchstart:x,onTouchmove:g,onTouchend:S},[e("div",{class:"flex transition-transform duration-300 ease-in-out",style:Ge({transform:`translateX(-${r.value*100}%)`})},[(d(!0),p(ue,null,fe(w.items,(M,T)=>(d(),p("div",{key:h(M,T),class:"w-full flex-shrink-0"},[ze(w.$slots,"default",{item:M,index:T,isActive:T===r.value},void 0,!0)]))),128))],4)],34),w.items.length>1?(d(),p("div",Hd,[e("button",{onClick:l,disabled:r.value===0,class:K(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",w.navigationButtonClasses]),"aria-label":"Previous item"},[z(k(So),{class:"h-5 w-5 text-gray-600"})],10,Wd),e("div",Kd,[(d(!0),p(ue,null,fe(w.items,(M,T)=>(d(),p("button",{key:`dot-${h(M,T)}`,onClick:I=>c(T),class:K(["w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",[T===r.value?"bg-blue-600 scale-110":"bg-gray-300 hover:bg-gray-400",w.dotClasses]]),"aria-label":`Go to item ${T+1}`},null,10,Yd))),128))]),e("button",{onClick:i,disabled:r.value===w.items.length-1,class:K(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",w.navigationButtonClasses]),"aria-label":"Next item"},[z(k(Mo),{class:"h-5 w-5 text-gray-600"})],10,Xd)])):R("",!0),w.showCounter&&w.items.length>1?(d(),p("div",Qd,A(r.value+1)+" of "+A(w.items.length),1)):R("",!0)]))}});const Zd=Ae(Jd,[["__scopeId","data-v-bf9f80b5"]]),rt=(t,s="MWK",o="en-MW",r={})=>{const{showDecimals:n=!0,compact:a=!1}=r;if(t===0)return s==="MWK"?"MK 0":`${s} 0`;if(s==="MWK"||s==="MK"){const h={minimumFractionDigits:n?2:0,maximumFractionDigits:n?2:0};return a&&t>=1e6&&(h.notation="compact",h.compactDisplay="short"),`MK ${new Intl.NumberFormat("en-MW",h).format(t)}`}const u={style:"currency",currency:s,minimumFractionDigits:n?2:0,maximumFractionDigits:n?2:0};return a&&t>=1e6&&(u.notation="compact",u.compactDisplay="short"),new Intl.NumberFormat(o,u).format(t)},Ko=(t,s={year:"numeric",month:"short",day:"numeric"},o="en-US")=>{const r=typeof t=="string"?new Date(t):t;return new Intl.DateTimeFormat(o,s).format(r)},ps=(t,s="en-US")=>{const o=typeof t=="string"?new Date(t):t,n=Math.floor((new Date().getTime()-o.getTime())/1e3),a=new Intl.RelativeTimeFormat(s,{numeric:"auto"});return n<60?a.format(-n,"second"):n<3600?a.format(-Math.floor(n/60),"minute"):n<86400?a.format(-Math.floor(n/3600),"hour"):n<2592e3?a.format(-Math.floor(n/86400),"day"):n<31536e3?a.format(-Math.floor(n/2592e3),"month"):a.format(-Math.floor(n/31536e3),"year")},ro=(t,s="MWK")=>rt(t,s),eu=t=>{const s=new Date,o=t.getTime()-s.getTime();if(o<=0)return"Ended";const r=Math.floor(o/(1e3*60*60*24)),n=Math.floor(o%(1e3*60*60*24)/(1e3*60*60)),a=Math.floor(o%(1e3*60*60)/(1e3*60));return r>0?`${r}d ${n}h`:n>0?`${n}h ${a}m`:`${a}m`};function Yo(t,s){return function(){return t.apply(s,arguments)}}const{toString:tu}=Object.prototype,{getPrototypeOf:Ms}=Object,{iterator:Dt,toStringTag:Xo}=Symbol,zt=(t=>s=>{const o=tu.call(s);return t[o]||(t[o]=o.slice(8,-1).toLowerCase())})(Object.create(null)),Ve=t=>(t=t.toLowerCase(),s=>zt(s)===t),Ot=t=>s=>typeof s===t,{isArray:dt}=Array,xt=Ot("undefined");function su(t){return t!==null&&!xt(t)&&t.constructor!==null&&!xt(t.constructor)&&Be(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Qo=Ve("ArrayBuffer");function ou(t){let s;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?s=ArrayBuffer.isView(t):s=t&&t.buffer&&Qo(t.buffer),s}const ru=Ot("string"),Be=Ot("function"),Go=Ot("number"),Vt=t=>t!==null&&typeof t=="object",nu=t=>t===!0||t===!1,St=t=>{if(zt(t)!=="object")return!1;const s=Ms(t);return(s===null||s===Object.prototype||Object.getPrototypeOf(s)===null)&&!(Xo in t)&&!(Dt in t)},au=Ve("Date"),lu=Ve("File"),iu=Ve("Blob"),du=Ve("FileList"),uu=t=>Vt(t)&&Be(t.pipe),cu=t=>{let s;return t&&(typeof FormData=="function"&&t instanceof FormData||Be(t.append)&&((s=zt(t))==="formdata"||s==="object"&&Be(t.toString)&&t.toString()==="[object FormData]"))},mu=Ve("URLSearchParams"),[fu,pu,gu,hu]=["ReadableStream","Request","Response","Headers"].map(Ve),vu=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function kt(t,s,{allOwnKeys:o=!1}={}){if(t===null||typeof t>"u")return;let r,n;if(typeof t!="object"&&(t=[t]),dt(t))for(r=0,n=t.length;r<n;r++)s.call(null,t[r],r,t);else{const a=o?Object.getOwnPropertyNames(t):Object.keys(t),u=a.length;let h;for(r=0;r<u;r++)h=a[r],s.call(null,t[h],h,t)}}function Jo(t,s){s=s.toLowerCase();const o=Object.keys(t);let r=o.length,n;for(;r-- >0;)if(n=o[r],s===n.toLowerCase())return n;return null}const Xe=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Zo=t=>!xt(t)&&t!==Xe;function gs(){const{caseless:t}=Zo(this)&&this||{},s={},o=(r,n)=>{const a=t&&Jo(s,n)||n;St(s[a])&&St(r)?s[a]=gs(s[a],r):St(r)?s[a]=gs({},r):dt(r)?s[a]=r.slice():s[a]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&kt(arguments[r],o);return s}const yu=(t,s,o,{allOwnKeys:r}={})=>(kt(s,(n,a)=>{o&&Be(n)?t[a]=Yo(n,o):t[a]=n},{allOwnKeys:r}),t),bu=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),xu=(t,s,o,r)=>{t.prototype=Object.create(s.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:s.prototype}),o&&Object.assign(t.prototype,o)},wu=(t,s,o,r)=>{let n,a,u;const h={};if(s=s||{},t==null)return s;do{for(n=Object.getOwnPropertyNames(t),a=n.length;a-- >0;)u=n[a],(!r||r(u,t,s))&&!h[u]&&(s[u]=t[u],h[u]=!0);t=o!==!1&&Ms(t)}while(t&&(!o||o(t,s))&&t!==Object.prototype);return s},_u=(t,s,o)=>{t=String(t),(o===void 0||o>t.length)&&(o=t.length),o-=s.length;const r=t.indexOf(s,o);return r!==-1&&r===o},ku=t=>{if(!t)return null;if(dt(t))return t;let s=t.length;if(!Go(s))return null;const o=new Array(s);for(;s-- >0;)o[s]=t[s];return o},Cu=(t=>s=>t&&s instanceof t)(typeof Uint8Array<"u"&&Ms(Uint8Array)),$u=(t,s)=>{const r=(t&&t[Dt]).call(t);let n;for(;(n=r.next())&&!n.done;){const a=n.value;s.call(t,a[0],a[1])}},Su=(t,s)=>{let o;const r=[];for(;(o=t.exec(s))!==null;)r.push(o);return r},Mu=Ve("HTMLFormElement"),Au=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(o,r,n){return r.toUpperCase()+n}),no=(({hasOwnProperty:t})=>(s,o)=>t.call(s,o))(Object.prototype),ju=Ve("RegExp"),er=(t,s)=>{const o=Object.getOwnPropertyDescriptors(t),r={};kt(o,(n,a)=>{let u;(u=s(n,a,t))!==!1&&(r[a]=u||n)}),Object.defineProperties(t,r)},Eu=t=>{er(t,(s,o)=>{if(Be(t)&&["arguments","caller","callee"].indexOf(o)!==-1)return!1;const r=t[o];if(Be(r)){if(s.enumerable=!1,"writable"in s){s.writable=!1;return}s.set||(s.set=()=>{throw Error("Can not rewrite read-only method '"+o+"'")})}})},Iu=(t,s)=>{const o={},r=n=>{n.forEach(a=>{o[a]=!0})};return dt(t)?r(t):r(String(t).split(s)),o},Tu=()=>{},Bu=(t,s)=>t!=null&&Number.isFinite(t=+t)?t:s;function Pu(t){return!!(t&&Be(t.append)&&t[Xo]==="FormData"&&t[Dt])}const Ru=t=>{const s=new Array(10),o=(r,n)=>{if(Vt(r)){if(s.indexOf(r)>=0)return;if(!("toJSON"in r)){s[n]=r;const a=dt(r)?[]:{};return kt(r,(u,h)=>{const c=o(u,n+1);!xt(c)&&(a[h]=c)}),s[n]=void 0,a}}return r};return o(t,0)},Nu=Ve("AsyncFunction"),Lu=t=>t&&(Vt(t)||Be(t))&&Be(t.then)&&Be(t.catch),tr=((t,s)=>t?setImmediate:s?((o,r)=>(Xe.addEventListener("message",({source:n,data:a})=>{n===Xe&&a===o&&r.length&&r.shift()()},!1),n=>{r.push(n),Xe.postMessage(o,"*")}))(`axios@${Math.random()}`,[]):o=>setTimeout(o))(typeof setImmediate=="function",Be(Xe.postMessage)),Du=typeof queueMicrotask<"u"?queueMicrotask.bind(Xe):typeof process<"u"&&process.nextTick||tr,zu=t=>t!=null&&Be(t[Dt]),D={isArray:dt,isArrayBuffer:Qo,isBuffer:su,isFormData:cu,isArrayBufferView:ou,isString:ru,isNumber:Go,isBoolean:nu,isObject:Vt,isPlainObject:St,isReadableStream:fu,isRequest:pu,isResponse:gu,isHeaders:hu,isUndefined:xt,isDate:au,isFile:lu,isBlob:iu,isRegExp:ju,isFunction:Be,isStream:uu,isURLSearchParams:mu,isTypedArray:Cu,isFileList:du,forEach:kt,merge:gs,extend:yu,trim:vu,stripBOM:bu,inherits:xu,toFlatObject:wu,kindOf:zt,kindOfTest:Ve,endsWith:_u,toArray:ku,forEachEntry:$u,matchAll:Su,isHTMLForm:Mu,hasOwnProperty:no,hasOwnProp:no,reduceDescriptors:er,freezeMethods:Eu,toObjectSet:Iu,toCamelCase:Au,noop:Tu,toFiniteNumber:Bu,findKey:Jo,global:Xe,isContextDefined:Zo,isSpecCompliantForm:Pu,toJSONObject:Ru,isAsyncFn:Nu,isThenable:Lu,setImmediate:tr,asap:Du,isIterable:zu};function me(t,s,o,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",s&&(this.code=s),o&&(this.config=o),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}D.inherits(me,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const sr=me.prototype,or={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{or[t]={value:t}});Object.defineProperties(me,or);Object.defineProperty(sr,"isAxiosError",{value:!0});me.from=(t,s,o,r,n,a)=>{const u=Object.create(sr);return D.toFlatObject(t,u,function(c){return c!==Error.prototype},h=>h!=="isAxiosError"),me.call(u,t.message,s,o,r,n),u.cause=t,u.name=t.name,a&&Object.assign(u,a),u};const Ou=null;function hs(t){return D.isPlainObject(t)||D.isArray(t)}function rr(t){return D.endsWith(t,"[]")?t.slice(0,-2):t}function ao(t,s,o){return t?t.concat(s).map(function(n,a){return n=rr(n),!o&&a?"["+n+"]":n}).join(o?".":""):s}function Vu(t){return D.isArray(t)&&!t.some(hs)}const Fu=D.toFlatObject(D,{},null,function(s){return/^is[A-Z]/.test(s)});function Ft(t,s,o){if(!D.isObject(t))throw new TypeError("target must be an object");s=s||new FormData,o=D.toFlatObject(o,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,S){return!D.isUndefined(S[g])});const r=o.metaTokens,n=o.visitor||l,a=o.dots,u=o.indexes,c=(o.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(s);if(!D.isFunction(n))throw new TypeError("visitor must be a function");function i(x){if(x===null)return"";if(D.isDate(x))return x.toISOString();if(D.isBoolean(x))return x.toString();if(!c&&D.isBlob(x))throw new me("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(x)||D.isTypedArray(x)?c&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function l(x,g,S){let P=x;if(x&&!S&&typeof x=="object"){if(D.endsWith(g,"{}"))g=r?g:g.slice(0,-2),x=JSON.stringify(x);else if(D.isArray(x)&&Vu(x)||(D.isFileList(x)||D.endsWith(g,"[]"))&&(P=D.toArray(x)))return g=rr(g),P.forEach(function(w,_){!(D.isUndefined(w)||w===null)&&s.append(u===!0?ao([g],_,a):u===null?g:g+"[]",i(w))}),!1}return hs(x)?!0:(s.append(ao(S,g,a),i(x)),!1)}const f=[],b=Object.assign(Fu,{defaultVisitor:l,convertValue:i,isVisitable:hs});function v(x,g){if(!D.isUndefined(x)){if(f.indexOf(x)!==-1)throw Error("Circular reference detected in "+g.join("."));f.push(x),D.forEach(x,function(P,j){(!(D.isUndefined(P)||P===null)&&n.call(s,P,D.isString(j)?j.trim():j,g,b))===!0&&v(P,g?g.concat(j):[j])}),f.pop()}}if(!D.isObject(t))throw new TypeError("data must be an object");return v(t),s}function lo(t){const s={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(r){return s[r]})}function As(t,s){this._pairs=[],t&&Ft(t,this,s)}const nr=As.prototype;nr.append=function(s,o){this._pairs.push([s,o])};nr.toString=function(s){const o=s?function(r){return s.call(this,r,lo)}:lo;return this._pairs.map(function(n){return o(n[0])+"="+o(n[1])},"").join("&")};function qu(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ar(t,s,o){if(!s)return t;const r=o&&o.encode||qu;D.isFunction(o)&&(o={serialize:o});const n=o&&o.serialize;let a;if(n?a=n(s,o):a=D.isURLSearchParams(s)?s.toString():new As(s,o).toString(r),a){const u=t.indexOf("#");u!==-1&&(t=t.slice(0,u)),t+=(t.indexOf("?")===-1?"?":"&")+a}return t}class Uu{constructor(){this.handlers=[]}use(s,o,r){return this.handlers.push({fulfilled:s,rejected:o,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(s){this.handlers[s]&&(this.handlers[s]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(s){D.forEach(this.handlers,function(r){r!==null&&s(r)})}}const io=Uu,lr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Hu=typeof URLSearchParams<"u"?URLSearchParams:As,Wu=typeof FormData<"u"?FormData:null,Ku=typeof Blob<"u"?Blob:null,Yu={isBrowser:!0,classes:{URLSearchParams:Hu,FormData:Wu,Blob:Ku},protocols:["http","https","file","blob","url","data"]},js=typeof window<"u"&&typeof document<"u",vs=typeof navigator=="object"&&navigator||void 0,Xu=js&&(!vs||["ReactNative","NativeScript","NS"].indexOf(vs.product)<0),Qu=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Gu=js&&window.location.href||"http://localhost",Ju=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:js,hasStandardBrowserEnv:Xu,hasStandardBrowserWebWorkerEnv:Qu,navigator:vs,origin:Gu},Symbol.toStringTag,{value:"Module"})),Se={...Ju,...Yu};function Zu(t,s){return Ft(t,new Se.classes.URLSearchParams,Object.assign({visitor:function(o,r,n,a){return Se.isNode&&D.isBuffer(o)?(this.append(r,o.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},s))}function ec(t){return D.matchAll(/\w+|\[(\w*)]/g,t).map(s=>s[0]==="[]"?"":s[1]||s[0])}function tc(t){const s={},o=Object.keys(t);let r;const n=o.length;let a;for(r=0;r<n;r++)a=o[r],s[a]=t[a];return s}function ir(t){function s(o,r,n,a){let u=o[a++];if(u==="__proto__")return!0;const h=Number.isFinite(+u),c=a>=o.length;return u=!u&&D.isArray(n)?n.length:u,c?(D.hasOwnProp(n,u)?n[u]=[n[u],r]:n[u]=r,!h):((!n[u]||!D.isObject(n[u]))&&(n[u]=[]),s(o,r,n[u],a)&&D.isArray(n[u])&&(n[u]=tc(n[u])),!h)}if(D.isFormData(t)&&D.isFunction(t.entries)){const o={};return D.forEachEntry(t,(r,n)=>{s(ec(r),n,o,0)}),o}return null}function sc(t,s,o){if(D.isString(t))try{return(s||JSON.parse)(t),D.trim(t)}catch(r){if(r.name!=="SyntaxError")throw r}return(o||JSON.stringify)(t)}const Es={transitional:lr,adapter:["xhr","http","fetch"],transformRequest:[function(s,o){const r=o.getContentType()||"",n=r.indexOf("application/json")>-1,a=D.isObject(s);if(a&&D.isHTMLForm(s)&&(s=new FormData(s)),D.isFormData(s))return n?JSON.stringify(ir(s)):s;if(D.isArrayBuffer(s)||D.isBuffer(s)||D.isStream(s)||D.isFile(s)||D.isBlob(s)||D.isReadableStream(s))return s;if(D.isArrayBufferView(s))return s.buffer;if(D.isURLSearchParams(s))return o.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),s.toString();let h;if(a){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Zu(s,this.formSerializer).toString();if((h=D.isFileList(s))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Ft(h?{"files[]":s}:s,c&&new c,this.formSerializer)}}return a||n?(o.setContentType("application/json",!1),sc(s)):s}],transformResponse:[function(s){const o=this.transitional||Es.transitional,r=o&&o.forcedJSONParsing,n=this.responseType==="json";if(D.isResponse(s)||D.isReadableStream(s))return s;if(s&&D.isString(s)&&(r&&!this.responseType||n)){const u=!(o&&o.silentJSONParsing)&&n;try{return JSON.parse(s)}catch(h){if(u)throw h.name==="SyntaxError"?me.from(h,me.ERR_BAD_RESPONSE,this,null,this.response):h}}return s}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Se.classes.FormData,Blob:Se.classes.Blob},validateStatus:function(s){return s>=200&&s<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],t=>{Es.headers[t]={}});const Is=Es,oc=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),rc=t=>{const s={};let o,r,n;return t&&t.split(`
`).forEach(function(u){n=u.indexOf(":"),o=u.substring(0,n).trim().toLowerCase(),r=u.substring(n+1).trim(),!(!o||s[o]&&oc[o])&&(o==="set-cookie"?s[o]?s[o].push(r):s[o]=[r]:s[o]=s[o]?s[o]+", "+r:r)}),s},uo=Symbol("internals");function ft(t){return t&&String(t).trim().toLowerCase()}function Mt(t){return t===!1||t==null?t:D.isArray(t)?t.map(Mt):String(t)}function nc(t){const s=Object.create(null),o=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=o.exec(t);)s[r[1]]=r[2];return s}const ac=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function es(t,s,o,r,n){if(D.isFunction(r))return r.call(this,s,o);if(n&&(s=o),!!D.isString(s)){if(D.isString(r))return s.indexOf(r)!==-1;if(D.isRegExp(r))return r.test(s)}}function lc(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(s,o,r)=>o.toUpperCase()+r)}function ic(t,s){const o=D.toCamelCase(" "+s);["get","set","has"].forEach(r=>{Object.defineProperty(t,r+o,{value:function(n,a,u){return this[r].call(this,s,n,a,u)},configurable:!0})})}class qt{constructor(s){s&&this.set(s)}set(s,o,r){const n=this;function a(h,c,i){const l=ft(c);if(!l)throw new Error("header name must be a non-empty string");const f=D.findKey(n,l);(!f||n[f]===void 0||i===!0||i===void 0&&n[f]!==!1)&&(n[f||c]=Mt(h))}const u=(h,c)=>D.forEach(h,(i,l)=>a(i,l,c));if(D.isPlainObject(s)||s instanceof this.constructor)u(s,o);else if(D.isString(s)&&(s=s.trim())&&!ac(s))u(rc(s),o);else if(D.isObject(s)&&D.isIterable(s)){let h={},c,i;for(const l of s){if(!D.isArray(l))throw TypeError("Object iterator must return a key-value pair");h[i=l[0]]=(c=h[i])?D.isArray(c)?[...c,l[1]]:[c,l[1]]:l[1]}u(h,o)}else s!=null&&a(o,s,r);return this}get(s,o){if(s=ft(s),s){const r=D.findKey(this,s);if(r){const n=this[r];if(!o)return n;if(o===!0)return nc(n);if(D.isFunction(o))return o.call(this,n,r);if(D.isRegExp(o))return o.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(s,o){if(s=ft(s),s){const r=D.findKey(this,s);return!!(r&&this[r]!==void 0&&(!o||es(this,this[r],r,o)))}return!1}delete(s,o){const r=this;let n=!1;function a(u){if(u=ft(u),u){const h=D.findKey(r,u);h&&(!o||es(r,r[h],h,o))&&(delete r[h],n=!0)}}return D.isArray(s)?s.forEach(a):a(s),n}clear(s){const o=Object.keys(this);let r=o.length,n=!1;for(;r--;){const a=o[r];(!s||es(this,this[a],a,s,!0))&&(delete this[a],n=!0)}return n}normalize(s){const o=this,r={};return D.forEach(this,(n,a)=>{const u=D.findKey(r,a);if(u){o[u]=Mt(n),delete o[a];return}const h=s?lc(a):String(a).trim();h!==a&&delete o[a],o[h]=Mt(n),r[h]=!0}),this}concat(...s){return this.constructor.concat(this,...s)}toJSON(s){const o=Object.create(null);return D.forEach(this,(r,n)=>{r!=null&&r!==!1&&(o[n]=s&&D.isArray(r)?r.join(", "):r)}),o}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([s,o])=>s+": "+o).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(s){return s instanceof this?s:new this(s)}static concat(s,...o){const r=new this(s);return o.forEach(n=>r.set(n)),r}static accessor(s){const r=(this[uo]=this[uo]={accessors:{}}).accessors,n=this.prototype;function a(u){const h=ft(u);r[h]||(ic(n,u),r[h]=!0)}return D.isArray(s)?s.forEach(a):a(s),this}}qt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(qt.prototype,({value:t},s)=>{let o=s[0].toUpperCase()+s.slice(1);return{get:()=>t,set(r){this[o]=r}}});D.freezeMethods(qt);const De=qt;function ts(t,s){const o=this||Is,r=s||o,n=De.from(r.headers);let a=r.data;return D.forEach(t,function(h){a=h.call(o,a,n.normalize(),s?s.status:void 0)}),n.normalize(),a}function dr(t){return!!(t&&t.__CANCEL__)}function ut(t,s,o){me.call(this,t??"canceled",me.ERR_CANCELED,s,o),this.name="CanceledError"}D.inherits(ut,me,{__CANCEL__:!0});function ur(t,s,o){const r=o.config.validateStatus;!o.status||!r||r(o.status)?t(o):s(new me("Request failed with status code "+o.status,[me.ERR_BAD_REQUEST,me.ERR_BAD_RESPONSE][Math.floor(o.status/100)-4],o.config,o.request,o))}function dc(t){const s=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return s&&s[1]||""}function uc(t,s){t=t||10;const o=new Array(t),r=new Array(t);let n=0,a=0,u;return s=s!==void 0?s:1e3,function(c){const i=Date.now(),l=r[a];u||(u=i),o[n]=c,r[n]=i;let f=a,b=0;for(;f!==n;)b+=o[f++],f=f%t;if(n=(n+1)%t,n===a&&(a=(a+1)%t),i-u<s)return;const v=l&&i-l;return v?Math.round(b*1e3/v):void 0}}function cc(t,s){let o=0,r=1e3/s,n,a;const u=(i,l=Date.now())=>{o=l,n=null,a&&(clearTimeout(a),a=null),t.apply(null,i)};return[(...i)=>{const l=Date.now(),f=l-o;f>=r?u(i,l):(n=i,a||(a=setTimeout(()=>{a=null,u(n)},r-f)))},()=>n&&u(n)]}const It=(t,s,o=3)=>{let r=0;const n=uc(50,250);return cc(a=>{const u=a.loaded,h=a.lengthComputable?a.total:void 0,c=u-r,i=n(c),l=u<=h;r=u;const f={loaded:u,total:h,progress:h?u/h:void 0,bytes:c,rate:i||void 0,estimated:i&&h&&l?(h-u)/i:void 0,event:a,lengthComputable:h!=null,[s?"download":"upload"]:!0};t(f)},o)},co=(t,s)=>{const o=t!=null;return[r=>s[0]({lengthComputable:o,total:t,loaded:r}),s[1]]},mo=t=>(...s)=>D.asap(()=>t(...s)),mc=Se.hasStandardBrowserEnv?((t,s)=>o=>(o=new URL(o,Se.origin),t.protocol===o.protocol&&t.host===o.host&&(s||t.port===o.port)))(new URL(Se.origin),Se.navigator&&/(msie|trident)/i.test(Se.navigator.userAgent)):()=>!0,fc=Se.hasStandardBrowserEnv?{write(t,s,o,r,n,a){const u=[t+"="+encodeURIComponent(s)];D.isNumber(o)&&u.push("expires="+new Date(o).toGMTString()),D.isString(r)&&u.push("path="+r),D.isString(n)&&u.push("domain="+n),a===!0&&u.push("secure"),document.cookie=u.join("; ")},read(t){const s=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return s?decodeURIComponent(s[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function pc(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function gc(t,s){return s?t.replace(/\/?\/$/,"")+"/"+s.replace(/^\/+/,""):t}function cr(t,s,o){let r=!pc(s);return t&&(r||o==!1)?gc(t,s):s}const fo=t=>t instanceof De?{...t}:t;function Je(t,s){s=s||{};const o={};function r(i,l,f,b){return D.isPlainObject(i)&&D.isPlainObject(l)?D.merge.call({caseless:b},i,l):D.isPlainObject(l)?D.merge({},l):D.isArray(l)?l.slice():l}function n(i,l,f,b){if(D.isUndefined(l)){if(!D.isUndefined(i))return r(void 0,i,f,b)}else return r(i,l,f,b)}function a(i,l){if(!D.isUndefined(l))return r(void 0,l)}function u(i,l){if(D.isUndefined(l)){if(!D.isUndefined(i))return r(void 0,i)}else return r(void 0,l)}function h(i,l,f){if(f in s)return r(i,l);if(f in t)return r(void 0,i)}const c={url:a,method:a,data:a,baseURL:u,transformRequest:u,transformResponse:u,paramsSerializer:u,timeout:u,timeoutMessage:u,withCredentials:u,withXSRFToken:u,adapter:u,responseType:u,xsrfCookieName:u,xsrfHeaderName:u,onUploadProgress:u,onDownloadProgress:u,decompress:u,maxContentLength:u,maxBodyLength:u,beforeRedirect:u,transport:u,httpAgent:u,httpsAgent:u,cancelToken:u,socketPath:u,responseEncoding:u,validateStatus:h,headers:(i,l,f)=>n(fo(i),fo(l),f,!0)};return D.forEach(Object.keys(Object.assign({},t,s)),function(l){const f=c[l]||n,b=f(t[l],s[l],l);D.isUndefined(b)&&f!==h||(o[l]=b)}),o}const mr=t=>{const s=Je({},t);let{data:o,withXSRFToken:r,xsrfHeaderName:n,xsrfCookieName:a,headers:u,auth:h}=s;s.headers=u=De.from(u),s.url=ar(cr(s.baseURL,s.url,s.allowAbsoluteUrls),t.params,t.paramsSerializer),h&&u.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let c;if(D.isFormData(o)){if(Se.hasStandardBrowserEnv||Se.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);else if((c=u.getContentType())!==!1){const[i,...l]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];u.setContentType([i||"multipart/form-data",...l].join("; "))}}if(Se.hasStandardBrowserEnv&&(r&&D.isFunction(r)&&(r=r(s)),r||r!==!1&&mc(s.url))){const i=n&&a&&fc.read(a);i&&u.set(n,i)}return s},hc=typeof XMLHttpRequest<"u",vc=hc&&function(t){return new Promise(function(o,r){const n=mr(t);let a=n.data;const u=De.from(n.headers).normalize();let{responseType:h,onUploadProgress:c,onDownloadProgress:i}=n,l,f,b,v,x;function g(){v&&v(),x&&x(),n.cancelToken&&n.cancelToken.unsubscribe(l),n.signal&&n.signal.removeEventListener("abort",l)}let S=new XMLHttpRequest;S.open(n.method.toUpperCase(),n.url,!0),S.timeout=n.timeout;function P(){if(!S)return;const w=De.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),M={data:!h||h==="text"||h==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:w,config:t,request:S};ur(function(I){o(I),g()},function(I){r(I),g()},M),S=null}"onloadend"in S?S.onloadend=P:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(P)},S.onabort=function(){S&&(r(new me("Request aborted",me.ECONNABORTED,t,S)),S=null)},S.onerror=function(){r(new me("Network Error",me.ERR_NETWORK,t,S)),S=null},S.ontimeout=function(){let _=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const M=n.transitional||lr;n.timeoutErrorMessage&&(_=n.timeoutErrorMessage),r(new me(_,M.clarifyTimeoutError?me.ETIMEDOUT:me.ECONNABORTED,t,S)),S=null},a===void 0&&u.setContentType(null),"setRequestHeader"in S&&D.forEach(u.toJSON(),function(_,M){S.setRequestHeader(M,_)}),D.isUndefined(n.withCredentials)||(S.withCredentials=!!n.withCredentials),h&&h!=="json"&&(S.responseType=n.responseType),i&&([b,x]=It(i,!0),S.addEventListener("progress",b)),c&&S.upload&&([f,v]=It(c),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",v)),(n.cancelToken||n.signal)&&(l=w=>{S&&(r(!w||w.type?new ut(null,t,S):w),S.abort(),S=null)},n.cancelToken&&n.cancelToken.subscribe(l),n.signal&&(n.signal.aborted?l():n.signal.addEventListener("abort",l)));const j=dc(n.url);if(j&&Se.protocols.indexOf(j)===-1){r(new me("Unsupported protocol "+j+":",me.ERR_BAD_REQUEST,t));return}S.send(a||null)})},yc=(t,s)=>{const{length:o}=t=t?t.filter(Boolean):[];if(s||o){let r=new AbortController,n;const a=function(i){if(!n){n=!0,h();const l=i instanceof Error?i:this.reason;r.abort(l instanceof me?l:new ut(l instanceof Error?l.message:l))}};let u=s&&setTimeout(()=>{u=null,a(new me(`timeout ${s} of ms exceeded`,me.ETIMEDOUT))},s);const h=()=>{t&&(u&&clearTimeout(u),u=null,t.forEach(i=>{i.unsubscribe?i.unsubscribe(a):i.removeEventListener("abort",a)}),t=null)};t.forEach(i=>i.addEventListener("abort",a));const{signal:c}=r;return c.unsubscribe=()=>D.asap(h),c}},bc=yc,xc=function*(t,s){let o=t.byteLength;if(!s||o<s){yield t;return}let r=0,n;for(;r<o;)n=r+s,yield t.slice(r,n),r=n},wc=async function*(t,s){for await(const o of _c(t))yield*xc(o,s)},_c=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const s=t.getReader();try{for(;;){const{done:o,value:r}=await s.read();if(o)break;yield r}}finally{await s.cancel()}},po=(t,s,o,r)=>{const n=wc(t,s);let a=0,u,h=c=>{u||(u=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:i,value:l}=await n.next();if(i){h(),c.close();return}let f=l.byteLength;if(o){let b=a+=f;o(b)}c.enqueue(new Uint8Array(l))}catch(i){throw h(i),i}},cancel(c){return h(c),n.return()}},{highWaterMark:2})},Ut=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",fr=Ut&&typeof ReadableStream=="function",kc=Ut&&(typeof TextEncoder=="function"?(t=>s=>t.encode(s))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),pr=(t,...s)=>{try{return!!t(...s)}catch{return!1}},Cc=fr&&pr(()=>{let t=!1;const s=new Request(Se.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!s}),go=64*1024,ys=fr&&pr(()=>D.isReadableStream(new Response("").body)),Tt={stream:ys&&(t=>t.body)};Ut&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(s=>{!Tt[s]&&(Tt[s]=D.isFunction(t[s])?o=>o[s]():(o,r)=>{throw new me(`Response type '${s}' is not supported`,me.ERR_NOT_SUPPORT,r)})})})(new Response);const $c=async t=>{if(t==null)return 0;if(D.isBlob(t))return t.size;if(D.isSpecCompliantForm(t))return(await new Request(Se.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(D.isArrayBufferView(t)||D.isArrayBuffer(t))return t.byteLength;if(D.isURLSearchParams(t)&&(t=t+""),D.isString(t))return(await kc(t)).byteLength},Sc=async(t,s)=>{const o=D.toFiniteNumber(t.getContentLength());return o??$c(s)},Mc=Ut&&(async t=>{let{url:s,method:o,data:r,signal:n,cancelToken:a,timeout:u,onDownloadProgress:h,onUploadProgress:c,responseType:i,headers:l,withCredentials:f="same-origin",fetchOptions:b}=mr(t);i=i?(i+"").toLowerCase():"text";let v=bc([n,a&&a.toAbortSignal()],u),x;const g=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let S;try{if(c&&Cc&&o!=="get"&&o!=="head"&&(S=await Sc(l,r))!==0){let M=new Request(s,{method:"POST",body:r,duplex:"half"}),T;if(D.isFormData(r)&&(T=M.headers.get("content-type"))&&l.setContentType(T),M.body){const[I,U]=co(S,It(mo(c)));r=po(M.body,go,I,U)}}D.isString(f)||(f=f?"include":"omit");const P="credentials"in Request.prototype;x=new Request(s,{...b,signal:v,method:o.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:P?f:void 0});let j=await fetch(x,b);const w=ys&&(i==="stream"||i==="response");if(ys&&(h||w&&g)){const M={};["status","statusText","headers"].forEach(te=>{M[te]=j[te]});const T=D.toFiniteNumber(j.headers.get("content-length")),[I,U]=h&&co(T,It(mo(h),!0))||[];j=new Response(po(j.body,go,I,()=>{U&&U(),g&&g()}),M)}i=i||"text";let _=await Tt[D.findKey(Tt,i)||"text"](j,t);return!w&&g&&g(),await new Promise((M,T)=>{ur(M,T,{data:_,headers:De.from(j.headers),status:j.status,statusText:j.statusText,config:t,request:x})})}catch(P){throw g&&g(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new me("Network Error",me.ERR_NETWORK,t,x),{cause:P.cause||P}):me.from(P,P&&P.code,t,x)}}),bs={http:Ou,xhr:vc,fetch:Mc};D.forEach(bs,(t,s)=>{if(t){try{Object.defineProperty(t,"name",{value:s})}catch{}Object.defineProperty(t,"adapterName",{value:s})}});const ho=t=>`- ${t}`,Ac=t=>D.isFunction(t)||t===null||t===!1,gr={getAdapter:t=>{t=D.isArray(t)?t:[t];const{length:s}=t;let o,r;const n={};for(let a=0;a<s;a++){o=t[a];let u;if(r=o,!Ac(o)&&(r=bs[(u=String(o)).toLowerCase()],r===void 0))throw new me(`Unknown adapter '${u}'`);if(r)break;n[u||"#"+a]=r}if(!r){const a=Object.entries(n).map(([h,c])=>`adapter ${h} `+(c===!1?"is not supported by the environment":"is not available in the build"));let u=s?a.length>1?`since :
`+a.map(ho).join(`
`):" "+ho(a[0]):"as no adapter specified";throw new me("There is no suitable adapter to dispatch the request "+u,"ERR_NOT_SUPPORT")}return r},adapters:bs};function ss(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new ut(null,t)}function vo(t){return ss(t),t.headers=De.from(t.headers),t.data=ts.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),gr.getAdapter(t.adapter||Is.adapter)(t).then(function(r){return ss(t),r.data=ts.call(t,t.transformResponse,r),r.headers=De.from(r.headers),r},function(r){return dr(r)||(ss(t),r&&r.response&&(r.response.data=ts.call(t,t.transformResponse,r.response),r.response.headers=De.from(r.response.headers))),Promise.reject(r)})}const hr="1.10.0",Ht={};["object","boolean","number","function","string","symbol"].forEach((t,s)=>{Ht[t]=function(r){return typeof r===t||"a"+(s<1?"n ":" ")+t}});const yo={};Ht.transitional=function(s,o,r){function n(a,u){return"[Axios v"+hr+"] Transitional option '"+a+"'"+u+(r?". "+r:"")}return(a,u,h)=>{if(s===!1)throw new me(n(u," has been removed"+(o?" in "+o:"")),me.ERR_DEPRECATED);return o&&!yo[u]&&(yo[u]=!0,console.warn(n(u," has been deprecated since v"+o+" and will be removed in the near future"))),s?s(a,u,h):!0}};Ht.spelling=function(s){return(o,r)=>(console.warn(`${r} is likely a misspelling of ${s}`),!0)};function jc(t,s,o){if(typeof t!="object")throw new me("options must be an object",me.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let n=r.length;for(;n-- >0;){const a=r[n],u=s[a];if(u){const h=t[a],c=h===void 0||u(h,a,t);if(c!==!0)throw new me("option "+a+" must be "+c,me.ERR_BAD_OPTION_VALUE);continue}if(o!==!0)throw new me("Unknown option "+a,me.ERR_BAD_OPTION)}}const At={assertOptions:jc,validators:Ht},Fe=At.validators;class Bt{constructor(s){this.defaults=s||{},this.interceptors={request:new io,response:new io}}async request(s,o){try{return await this._request(s,o)}catch(r){if(r instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const a=n.stack?n.stack.replace(/^.+\n/,""):"";try{r.stack?a&&!String(r.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+a):r.stack=a}catch{}}throw r}}_request(s,o){typeof s=="string"?(o=o||{},o.url=s):o=s||{},o=Je(this.defaults,o);const{transitional:r,paramsSerializer:n,headers:a}=o;r!==void 0&&At.assertOptions(r,{silentJSONParsing:Fe.transitional(Fe.boolean),forcedJSONParsing:Fe.transitional(Fe.boolean),clarifyTimeoutError:Fe.transitional(Fe.boolean)},!1),n!=null&&(D.isFunction(n)?o.paramsSerializer={serialize:n}:At.assertOptions(n,{encode:Fe.function,serialize:Fe.function},!0)),o.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?o.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:o.allowAbsoluteUrls=!0),At.assertOptions(o,{baseUrl:Fe.spelling("baseURL"),withXsrfToken:Fe.spelling("withXSRFToken")},!0),o.method=(o.method||this.defaults.method||"get").toLowerCase();let u=a&&D.merge(a.common,a[o.method]);a&&D.forEach(["delete","get","head","post","put","patch","common"],x=>{delete a[x]}),o.headers=De.concat(u,a);const h=[];let c=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(o)===!1||(c=c&&g.synchronous,h.unshift(g.fulfilled,g.rejected))});const i=[];this.interceptors.response.forEach(function(g){i.push(g.fulfilled,g.rejected)});let l,f=0,b;if(!c){const x=[vo.bind(this),void 0];for(x.unshift.apply(x,h),x.push.apply(x,i),b=x.length,l=Promise.resolve(o);f<b;)l=l.then(x[f++],x[f++]);return l}b=h.length;let v=o;for(f=0;f<b;){const x=h[f++],g=h[f++];try{v=x(v)}catch(S){g.call(this,S);break}}try{l=vo.call(this,v)}catch(x){return Promise.reject(x)}for(f=0,b=i.length;f<b;)l=l.then(i[f++],i[f++]);return l}getUri(s){s=Je(this.defaults,s);const o=cr(s.baseURL,s.url,s.allowAbsoluteUrls);return ar(o,s.params,s.paramsSerializer)}}D.forEach(["delete","get","head","options"],function(s){Bt.prototype[s]=function(o,r){return this.request(Je(r||{},{method:s,url:o,data:(r||{}).data}))}});D.forEach(["post","put","patch"],function(s){function o(r){return function(a,u,h){return this.request(Je(h||{},{method:s,headers:r?{"Content-Type":"multipart/form-data"}:{},url:a,data:u}))}}Bt.prototype[s]=o(),Bt.prototype[s+"Form"]=o(!0)});const jt=Bt;class Ts{constructor(s){if(typeof s!="function")throw new TypeError("executor must be a function.");let o;this.promise=new Promise(function(a){o=a});const r=this;this.promise.then(n=>{if(!r._listeners)return;let a=r._listeners.length;for(;a-- >0;)r._listeners[a](n);r._listeners=null}),this.promise.then=n=>{let a;const u=new Promise(h=>{r.subscribe(h),a=h}).then(n);return u.cancel=function(){r.unsubscribe(a)},u},s(function(a,u,h){r.reason||(r.reason=new ut(a,u,h),o(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(s){if(this.reason){s(this.reason);return}this._listeners?this._listeners.push(s):this._listeners=[s]}unsubscribe(s){if(!this._listeners)return;const o=this._listeners.indexOf(s);o!==-1&&this._listeners.splice(o,1)}toAbortSignal(){const s=new AbortController,o=r=>{s.abort(r)};return this.subscribe(o),s.signal.unsubscribe=()=>this.unsubscribe(o),s.signal}static source(){let s;return{token:new Ts(function(n){s=n}),cancel:s}}}const Ec=Ts;function Ic(t){return function(o){return t.apply(null,o)}}function Tc(t){return D.isObject(t)&&t.isAxiosError===!0}const xs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xs).forEach(([t,s])=>{xs[s]=t});const Bc=xs;function vr(t){const s=new jt(t),o=Yo(jt.prototype.request,s);return D.extend(o,jt.prototype,s,{allOwnKeys:!0}),D.extend(o,s,null,{allOwnKeys:!0}),o.create=function(n){return vr(Je(t,n))},o}const we=vr(Is);we.Axios=jt;we.CanceledError=ut;we.CancelToken=Ec;we.isCancel=dr;we.VERSION=hr;we.toFormData=Ft;we.AxiosError=me;we.Cancel=we.CanceledError;we.all=function(s){return Promise.all(s)};we.spread=Ic;we.isAxiosError=Tc;we.mergeConfig=Je;we.AxiosHeaders=De;we.formToJSON=t=>ir(D.isHTMLForm(t)?new FormData(t):t);we.getAdapter=gr.getAdapter;we.HttpStatusCode=Bc;we.default=we;const je=we,Bs="vertigo_cart_state",Pc="vertigo_cart_sync",yr=t=>({id:t.id,name:t.name,title:t.title,description:t.description,target_amount:t.target_amount,bid_amount:t.bid_amount,auction_id:t.auction_id,auction_type_id:t.auction_type_id,status:t.status,type:t.type,cropped:t.cropped,image:t.image,code:t.code,reference_number:t.reference_number,date_from:t.date_from,date_to:t.date_to,closed_by:t.closed_by,branch_id:t.branch_id,created_at:t.created_at,updated_at:t.updated_at,quantity:t.quantity,optimistic:t.optimistic,lastUpdated:t.lastUpdated}),st=(t,s)=>{try{const o={items:t.map(yr),lastSyncTime:s,version:"2.0"};localStorage.setItem(Bs,JSON.stringify(o))}catch(o){console.warn("Failed to save cart to localStorage:",o)}},Rc=()=>{try{const t=localStorage.getItem(Bs);if(t){const s=JSON.parse(t);if(s.version==="2.0")return s}}catch(t){console.warn("Failed to load cart from localStorage:",t)}return null},os=()=>{try{localStorage.removeItem(Bs)}catch(t){console.warn("Failed to clear cart storage:",t)}},Ue=Ze("cart",()=>{const t=L([]),s=L(!1),o=L(null),r=L(Date.now()),n=L(0),a=L(navigator.onLine);let u=null;typeof BroadcastChannel<"u"&&(u=new BroadcastChannel(Pc),u.onmessage=W=>{W.data.type==="CART_UPDATED"&&W.data.timestamp>r.value&&(t.value=W.data.items,r.value=W.data.timestamp,i())});const h=()=>{a.value=navigator.onLine,a.value&&n.value>0&&l()};window.addEventListener("online",h),window.addEventListener("offline",h),Me(t,W=>{se(W,r.value),c(W)},{deep:!0});const c=W=>{if(u){const Y=Date.now();r.value=Y;const ee=W.map(yr);u.postMessage({type:"CART_UPDATED",items:ee,timestamp:Y})}},i=()=>{window.dispatchEvent(new CustomEvent("cart-updated",{detail:{count:t.value.length}}))},l=async()=>{if(a.value)try{await U(),n.value=0}catch{n.value++,console.warn("Cart sync failed, will retry when online")}},f=N(()=>t.value.length),b=N(()=>t.value.reduce((W,Y)=>{const ee=Y.target_amount||0,re=Y.quantity||1;return W+ee*re},0)),v=N(()=>t.value.length===0),x=N(()=>t.value.filter(W=>{var Y;return((Y=W.auction_type)==null?void 0:Y.type)==="cash"})),g=N(()=>t.value.filter(W=>{var Y;return((Y=W.auction_type)==null?void 0:Y.type)==="online"})),S=N(()=>t.value.filter(W=>{var Y;return((Y=W.auction_type)==null?void 0:Y.type)==="live"})),P=async(W,Y=!1)=>{s.value=!0,o.value=null;try{if(Y)return window.location.href=`/add-to-cart/${W.id}?checkout=true`,!0;if(T(W.id))return o.value="Item is already in cart",!1;const ee={...W,quantity:1,optimistic:!0,lastUpdated:Date.now()};if(t.value.push(ee),i(),!a.value)return n.value++,!0;const re=await je.post(`/api/cart/add/${W.id}`,{quantity:1,source:"frontend"});if(re.data.success){const pe=t.value.findIndex(xe=>xe.id===W.id);return pe>-1&&(t.value[pe].optimistic=!1),st(t.value,r.value),i(),!0}else{const pe=t.value.findIndex(xe=>xe.id===W.id&&xe.optimistic);return pe>-1&&(t.value.splice(pe,1),i()),o.value=re.data.message||"Failed to add item to cart",!1}}catch(ee){const re=t.value.findIndex(pe=>pe.id===W.id&&pe.optimistic);return re>-1&&(t.value.splice(re,1),i()),a.value?o.value="Failed to add item to cart":(o.value="No internet connection. Item will be added when connection is restored.",n.value++),console.error("Error adding to cart:",ee),!1}finally{s.value=!1}},j=async W=>{s.value=!0,o.value=null;try{const Y=t.value.findIndex(pe=>pe.id===W.id);if(Y===-1)return o.value="Item not found in cart",!1;const ee={...t.value[Y]};if(t.value.splice(Y,1),i(),!a.value)return n.value++,!0;const re=await je.delete(`/api/cart/remove/${W.id}`);return re.data.success?(st(t.value,r.value),i(),!0):(t.value.splice(Y,0,ee),i(),o.value=re.data.message||"Failed to remove item from cart",!1)}catch(Y){if(t.value.findIndex(re=>re.id===W.id)===-1){const re={...W,quantity:1};t.value.push(re),i()}return a.value?o.value="Failed to remove item from cart":(o.value="No internet connection. Item will be removed when connection is restored.",n.value++),console.error("Error removing from cart:",Y),!1}finally{s.value=!1}},w=async(W,Y)=>{const ee=t.value.find(pe=>pe.id===W);if(!ee)return o.value="Item not found in cart",!1;if(Y<=0)return await j(ee);const re=ee.quantity||1;if(ee.quantity=Y,ee.lastUpdated=Date.now(),i(),!a.value)return n.value++,!0;try{const pe=await je.put(`/api/cart/update/${W}`,{quantity:Y});return pe.data.success?(st(t.value,r.value),i(),!0):(ee.quantity=re,i(),o.value=pe.data.message||"Failed to update item quantity",!1)}catch(pe){return ee.quantity=re,i(),a.value?o.value="Failed to update item quantity":(o.value="No internet connection. Quantity will be updated when connection is restored.",n.value++),console.error("Error updating quantity:",pe),!1}},_=async(W=!0)=>{try{return W&&a.value&&!(await je.delete("/api/cart/clear")).data.success?(o.value="Failed to clear cart on server",!1):(t.value=[],o.value=null,os(),i(),!0)}catch(Y){return a.value?(o.value="Failed to clear cart",console.error("Error clearing cart:",Y),!1):(t.value=[],os(),i(),n.value++,!0)}},M=async()=>{const W=[];if(t.value.length===0)return{valid:!0,issues:[]};try{const Y=t.value.map(pe=>pe.id),ee=await je.post("/api/cart/validate",{item_ids:Y});ee.data.issues&&W.push(...ee.data.issues);const re=ee.data.unavailable_items||[];return re.length>0&&(t.value=t.value.filter(pe=>!re.includes(pe.id)),i(),W.push(`${re.length} item(s) are no longer available and have been removed from your cart.`)),{valid:W.length===0,issues:W}}catch(Y){return console.error("Cart validation error:",Y),{valid:!0,issues:[]}}},T=W=>t.value.some(Y=>Y.id===W),I=W=>{if(W&&W.length>0)t.value=W.map(Y=>({...Y,quantity:Y.quantity||1,lastUpdated:Date.now()})),r.value=Date.now(),st(t.value,r.value);else{const Y=Rc();Y&&Y.items.length>0&&(t.value=Y.items,r.value=Y.lastSyncTime,a.value&&M().then(({valid:ee,issues:re})=>{!ee&&re.length>0&&console.warn("Cart validation issues:",re)}))}i()},U=async(W=!0)=>{if(!a.value&&W)return console.warn("Cannot fetch cart while offline"),!1;s.value=!0,o.value=null;try{const Y=await je.get("/api/cart");return Y.data.success&&Y.data.items?(t.value=Y.data.items.map(ee=>({id:ee.id,name:ee.name,target_amount:ee.target_amount,quantity:ee.quantity||1,auction_type:ee.auction_type,image:ee.image,cropped:ee.image,lastUpdated:Date.now()})),r.value=Date.now(),st(t.value,r.value)):(t.value=[],os()),i(),n.value=0,!0}catch(Y){return o.value="Failed to fetch cart",console.error("Error fetching cart:",Y),W&&n.value<3&&(n.value++,setTimeout(()=>{U(!1)},Math.pow(2,n.value)*1e3)),!1}finally{s.value=!1}},te=W=>!W||W<=0?"MK 0":`MK ${W.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,X=W=>{const Y=W.target_amount||0,ee=W.quantity||1;return Y*ee},oe=()=>{t.value=[],s.value=!1,o.value=null},G=(W,Y={})=>{try{const ee={event:W,timestamp:Date.now(),cart_count:t.value.length,cart_total:b.value,...Y};typeof gtag<"u"&&gtag("event",W,{event_category:"cart",event_label:Y.item_name||"",value:Y.item_price||0});const re=JSON.parse(localStorage.getItem("cart_analytics")||"[]");re.push(ee),re.length>100&&re.splice(0,re.length-100),localStorage.setItem("cart_analytics",JSON.stringify(re))}catch(ee){console.warn("Failed to track cart event:",ee)}};let V=null;const se=(W,Y)=>{V&&clearTimeout(V),V=setTimeout(()=>{st(W,Y)},300)};return{items:t,loading:s,error:o,lastSyncTime:r,retryCount:n,isOnline:a,cartCount:f,cartTotal:b,isEmpty:v,cashItems:x,onlineItems:g,liveItems:S,addToCart:P,removeFromCart:j,updateQuantity:w,clearCart:_,isInCart:T,initializeCart:I,fetchCart:U,validateCartItems:M,syncWithServer:l,formatCurrency:te,getItemSubtotal:X,reset:oe,setLastShoppingPage:W=>{try{!W.includes("/cart")&&!W.includes("/checkout")&&sessionStorage.setItem("lastShoppingPage",W)}catch(Y){console.warn("Could not save last shopping page:",Y)}},getLastShoppingPage:()=>{try{return sessionStorage.getItem("lastShoppingPage")}catch(W){return console.warn("Could not retrieve last shopping page:",W),null}},trackCartEvent:G,cleanup:()=>{u&&u.close(),window.removeEventListener("online",h),window.removeEventListener("offline",h),V&&clearTimeout(V)}}});function br(){const t=be(),s=et(),o=L(!1),r=L("login"),n=N(()=>t.isAuthenticated),a=N(()=>t.user),u=N(()=>t.isLoading),h=async M=>{try{return await t.login(M)}catch(T){throw console.error("Login failed:",T),T}},c=async()=>{try{await t.logout(),s.push("/")}catch(M){throw console.error("Logout failed:",M),M}},i=async M=>{try{const T=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(M)});if(!T.ok){const U=await T.json();throw new Error(U.message||"Registration failed")}const I=await T.json();return await h({email:M.email,password:M.password,remember:!1}),I}catch(T){throw console.error("Registration failed:",T),T}},l=M=>n.value?(M==null||M(),!0):(b(),!1),f=async M=>n.value?(await(M==null?void 0:M()),!0):(b(),!1),b=()=>{r.value="login",o.value=!0},v=()=>{r.value="register",o.value=!0},x=()=>{o.value=!1},g=M=>{var T;return a.value&&((T=a.value.roles)==null?void 0:T.some(I=>I.name===M))||!1},S=M=>{var T;return a.value&&((T=a.value.permissions)==null?void 0:T.includes(M))||!1};return{isAuthenticated:n,user:a,isLoading:u,showAuthModal:o,authModalTab:r,login:h,logout:c,register:i,initialize:async()=>{await t.initialize()},requireAuth:l,requireAuthAsync:f,showLoginModal:b,showRegisterModal:v,hideAuthModal:x,hasRole:g,hasPermission:S,canPlaceBids:()=>n.value&&S("place_bids"),redirectToLogin:M=>{const T=M?{redirect:M}:{};s.push({name:"login",query:T})},redirectIfAuthenticated:(M="/")=>{n.value&&s.push(M)}}}const Nc={class:"group relative bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1"},Lc={class:"relative aspect-[4/3] overflow-hidden bg-gray-100"},Dc=["src","alt"],zc={key:1,class:"w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200"},Oc={class:"absolute top-3 left-3"},Vc={class:"flex items-center space-x-1"},Fc={class:"text-xs font-semibold"},qc={key:2,class:"absolute top-3 right-3"},Uc={class:"text-xs font-medium"},Hc={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100"},Wc={class:"p-5"},Kc={class:"mb-3"},Yc={class:"font-bold text-gray-900 text-lg line-clamp-2 mb-1"},Xc={key:0,class:"text-xs text-gray-500 font-mono"},Qc={class:"mb-4"},Gc={class:"flex items-baseline justify-between mb-1"},Jc={class:"text-sm font-medium text-gray-600"},Zc={key:0,class:"text-xs text-gray-500"},e0={class:"flex items-baseline space-x-2"},t0={class:"text-2xl font-bold text-gray-900"},s0={key:0,class:"text-sm text-gray-500 line-through"},o0={key:0,class:"mb-4 p-3 bg-gray-50 rounded-lg"},r0={class:"flex items-center justify-between text-sm"},n0={class:"text-gray-600"},a0={class:"font-medium text-gray-900"},l0={key:0,class:"mt-2"},i0={class:"w-full bg-gray-200 rounded-full h-1.5"},d0={key:1,class:"space-y-2"},u0={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},c0={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m0={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},f0={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},p0={key:2,class:"space-y-2"},g0=["fill"],h0=ge({__name:"ItemCard",props:{item:{}},emits:["view-details","place-bid","preview","add-to-cart","remove-from-cart","checkout","watch-item"],setup(t,{emit:s}){const o=t,r=s,n=Ue(),a=_t(),u=Le(),{isAuthenticated:h,user:c,initialize:i}=br(),l=L(!1),f=L(!1),b=L(!1),v=()=>We("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[We("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})]),x=()=>We("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[We("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})]),g=()=>We("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[We("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})]),S=$=>{switch($){case"cash":return"Daily Sale";case"online":return"Online";case"live":return"Live";default:return"Auction"}},P=$=>{switch($){case"cash":return v;case"online":return x;case"live":return g;default:return x}},j=$=>{const C="px-2 py-1 rounded-full text-white shadow-sm";switch($){case"cash":return`${C} bg-green-500`;case"online":return`${C} bg-blue-500`;case"live":return`${C} bg-orange-500`;default:return`${C} bg-gray-500`}},w=$=>{var C,q;return $.closed_by?"SOLD":((C=$.auction_type)==null?void 0:C.type)==="live"&&de($)?"LIVE":((q=$.auction_type)==null?void 0:q.type)!=="cash"&&O($)?"ENDING SOON":null},_=$=>{const C=w($),q="px-2 py-1 rounded-full text-white shadow-sm";switch(C){case"SOLD":return`${q} bg-gray-600`;case"LIVE":return`${q} bg-red-500 animate-pulse`;case"ENDING SOON":return`${q} bg-yellow-500`;default:return`${q} bg-blue-500`}},M=$=>{switch($){case"cash":return"Price";case"online":case"live":return"Current Bid";default:return"Price"}},T=$=>{var C;switch((C=$.auction_type)==null?void 0:C.type){case"cash":return $.target_amount||0;case"online":case"live":return $.bid_amount||$.target_amount||0;default:return $.target_amount||0}},I=$=>{var C;return((C=$.auction_type)==null?void 0:C.type)!=="cash"&&$.bid_amount&&$.target_amount&&$.bid_amount>$.target_amount?$.target_amount:null},U=$=>!$||$<=0?"0":$.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,"),te=$=>{var C;return((C=$.auction_type)==null?void 0:C.type)!=="cash"&&($.bid_amount||0)>0},X=$=>Math.floor(Math.random()*10)+1+"",oe=$=>!!($.date_from||$.date_to),G=$=>{switch($){case"cash":return"Available";case"online":return"Ends";case"live":return"Auction Date";default:return"Date"}},V=$=>{var C;if(((C=$.auction_type)==null?void 0:C.type)==="cash")return"Now";if($.date_to){const q=new Date($.date_to),y=new Date,m=q.getTime()-y.getTime();if(m<0)return"Ended";const H=Math.floor(m/(1e3*60*60*24)),B=Math.floor(m%(1e3*60*60*24)/(1e3*60*60));return H>0?`${H}d ${B}h`:B>0?`${B}h`:"Soon"}return"TBD"},se=$=>{if(!$.date_from||!$.date_to)return"0%";const C=new Date($.date_from).getTime(),q=new Date($.date_to).getTime(),y=new Date().getTime();if(y<C)return"0%";if(y>q)return"100%";const m=(y-C)/(q-C)*100;return`${Math.min(100,Math.max(0,m))}%`},de=$=>{if(!$.date_from||!$.date_to)return!1;const C=new Date;return C>=new Date($.date_from)&&C<=new Date($.date_to)},O=$=>{if(!$.date_to)return!1;const C=new Date($.date_to),q=new Date,y=C.getTime()-q.getTime();return y>0&&y<24*60*60*1e3},E=async $=>{l.value=!0;try{r("add-to-cart",$)}finally{setTimeout(()=>{l.value=!1},500)}},W=async $=>{l.value=!0;try{r("remove-from-cart",$)}finally{setTimeout(()=>{l.value=!1},500)}},Y=$=>{r("checkout",$)},ee=$=>{r("place-bid",$)},re=async $=>{if(!h.value){b.value=!0;return}f.value=!0;try{if(await a.toggleWatchlist($)){const q=Pe.value?"added to":"removed from";u.success(`${$.name} ${q} watchlist`)}else u.error(a.error||"Failed to update watchlist")}catch(C){console.error("Watchlist error:",C),u.error("Failed to update watchlist")}finally{f.value=!1}r("watch-item",$)},pe=async $=>{b.value=!1,u.success("You are now signed in and can add items to your watchlist!","Welcome!"),await a.initializeWatchlist(),await re(o.item)},xe=$=>!!($.closed_by||$.status==="sold"),Te=N(()=>n.isInCart(o.item.id)),Pe=N(()=>a.isInWatchlist(o.item.id));Ie(async()=>{await i(),h.value&&a.items.length===0&&await a.syncWatchlistCount()});const _e=$=>$.date_to?new Date>new Date($.date_to):!1,F=$=>{var C;return _e($)?"Auction Ended":((C=$.auction_type)==null?void 0:C.type)==="live"&&de($)?"Bid Live":"Place Bid"};return($,C)=>{var q,y,m,H,B,Z,le;return d(),p("div",Nc,[e("div",Lc,[$.item.cropped||$.item.image?(d(),p("img",{key:0,src:$.item.cropped||$.item.image,alt:$.item.name,class:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 cursor-pointer",onClick:C[0]||(C[0]=ne=>$.$emit("preview",$.item))},null,8,Dc)):(d(),p("div",zc,C[8]||(C[8]=[e("div",{class:"text-center"},[e("svg",{class:"w-16 h-16 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("p",{class:"text-xs text-gray-500"},"No Image")],-1)]))),e("div",Oc,[e("div",{class:K(j((q=$.item.auction_type)==null?void 0:q.type))},[e("div",Vc,[(d(),ce(Ne(P((y=$.item.auction_type)==null?void 0:y.type)),{class:"w-3 h-3"})),e("span",Fc,A(S((m=$.item.auction_type)==null?void 0:m.type)),1)])],2)]),w($.item)?(d(),p("div",qc,[e("div",{class:K(_($.item))},[e("span",Uc,A(w($.item)),1)],2)])):R("",!0),e("div",Hc,[e("button",{onClick:C[1]||(C[1]=ne=>$.$emit("preview",$.item)),class:"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 shadow-lg hover:shadow-xl"}," Quick View ")])]),e("div",Wc,[e("div",Kc,[e("h3",Yc,A($.item.name),1),$.item.code||$.item.reference_number?(d(),p("div",Xc," Ref: "+A($.item.code||$.item.reference_number),1)):R("",!0)]),e("div",Qc,[e("div",Gc,[e("span",Jc,A(M((H=$.item.auction_type)==null?void 0:H.type)),1),te($.item)?(d(),p("span",Zc,A(X($.item))+" bids",1)):R("",!0)]),e("div",e0,[e("span",t0,[C[9]||(C[9]=e("span",{class:"text-sm font-normal text-gray-500 mr-1"},"MK",-1)),Q(A(U(T($.item))),1)]),I($.item)?(d(),p("span",s0,[C[10]||(C[10]=e("span",{class:"text-xs font-normal text-gray-400 mr-1"},"MK",-1)),Q(A(U(I($.item))),1)])):R("",!0)])]),oe($.item)?(d(),p("div",o0,[e("div",r0,[e("span",n0,A(G((B=$.item.auction_type)==null?void 0:B.type)),1),e("span",a0,A(V($.item)),1)]),((Z=$.item.auction_type)==null?void 0:Z.type)!=="cash"?(d(),p("div",l0,[e("div",i0,[e("div",{class:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:Ge({width:se($.item)})},null,4)])])):R("",!0)])):R("",!0),((le=$.item.auction_type)==null?void 0:le.type)==="cash"?(d(),p("div",d0,[Te.value?(d(),ce(k(ae),{key:1,variant:"outline",class:"w-full font-semibold border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700",onClick:C[3]||(C[3]=Ce(ne=>W($.item),["stop"])),disabled:l.value},{default:J(()=>[l.value?(d(),p("svg",m0,C[13]||(C[13]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(d(),p("svg",f0,C[14]||(C[14]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),Q(" "+A(l.value?"Removing...":"Remove"),1)]),_:1},8,["disabled"])):(d(),ce(k(ae),{key:0,variant:"outline",class:"w-full font-semibold border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700",onClick:C[2]||(C[2]=Ce(ne=>E($.item),["stop"])),disabled:xe($.item)||l.value},{default:J(()=>[l.value?(d(),p("svg",u0,C[11]||(C[11]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(d(),p("svg",c0,C[12]||(C[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),Q(" "+A(xe($.item)?"Sold Out":l.value?"Adding...":"Add to Cart"),1)]),_:1},8,["disabled"])),z(k(ae),{variant:"primary",class:"w-full font-semibold bg-green-600 hover:bg-green-700 border-green-600",onClick:C[4]||(C[4]=Ce(ne=>Y($.item),["stop"])),disabled:xe($.item)},{default:J(()=>[C[15]||(C[15]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),Q(" "+A(xe($.item)?"Unavailable":"Buy Now"),1)]),_:1,__:[15]},8,["disabled"])])):(d(),p("div",p0,[z(k(ae),{variant:"primary",class:"w-full font-semibold bg-blue-600 hover:bg-blue-700 border-blue-600",onClick:C[5]||(C[5]=Ce(ne=>ee($.item),["stop"])),disabled:_e($.item)},{default:J(()=>[C[16]||(C[16]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1)),Q(" "+A(F($.item)),1)]),_:1,__:[16]},8,["disabled"]),z(k(ae),{variant:"outline",class:K(["w-full font-medium border-gray-300 transition-all duration-200",Pe.value?"text-red-600 border-red-300 bg-red-50 hover:bg-red-100":"text-gray-700 hover:bg-gray-50"]),loading:f.value,onClick:C[6]||(C[6]=Ce(ne=>re($.item),["stop"]))},{default:J(()=>[(d(),p("svg",{class:"w-4 h-4 mr-2",fill:Pe.value?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},C[17]||(C[17]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,g0)),Q(" "+A(Pe.value?"Remove from Watchlist":"Add to Watchlist"),1)]),_:1},8,["class","loading"])]))]),z(Lt,{show:b.value,"onUpdate:show":C[7]||(C[7]=ne=>b.value=ne),title:"Sign in to add to watchlist",subtitle:"Create an account or sign in to save items to your watchlist and track your favorite auctions.",onSuccess:pe},null,8,["show"])])}}});const xr=Ae(h0,[["__scopeId","data-v-9c64a7b0"]]);const v0={key:1},y0={class:"text-center"},b0={key:0,class:"mb-4"},x0={class:"text-lg font-medium text-gray-900 mb-2"},w0={class:"text-sm text-gray-600 mb-6"},_0={class:"flex flex-col sm:flex-row gap-3 justify-center"},k0=ge({__name:"AuthGuard",props:{title:{default:"Authentication Required"},message:{default:"Please sign in to continue with this action."},loginButtonText:{default:"Sign In"},registerButtonText:{default:"Create Account"},showRegisterButton:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},fallbackStyle:{default:"card"},requireAuth:{type:Boolean,default:!0}},emits:["auth-required","auth-success"],setup(t,{expose:s,emit:o}){const r=t,n=o,a=be(),u=L(!1),h=L("login"),c=N(()=>{if(!r.requireAuth)return!0;const v=window.user;if(v)return console.log("AuthGuard: User authenticated via server data",v),!0;const x=!!a.user,g=a.sessionAuth,S=!!a.token,P=a.isAuthenticated,j=x||g||P;return console.log("AuthGuard: authentication check",{serverUser:!!v,hasUser:x,hasSession:g,hasToken:S,storeAuth:P,finalResult:j}),j}),i=N(()=>a.isLoading),l=N(()=>{const v="auth-guard-fallback";switch(r.fallbackStyle){case"card":return`${v} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`;case"inline":return`${v} py-6`;case"minimal":return`${v} py-4`;default:return`${v} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`}}),f=(v="login")=>{h.value=v,u.value=!0,n("auth-required")},b=v=>{u.value=!1,n("auth-success",v)};return Ie(async()=>{const v=window.user;v&&!a.user&&(console.log("AuthGuard: Syncing server user data with auth store"),a.setUser(v))}),s({showAuthModal:f,isAuthenticated:c}),(v,x)=>(d(),p("div",null,[c.value?ze(v.$slots,"default",{key:0},void 0,!0):(d(),p("div",v0,[ze(v.$slots,"fallback",{},()=>[e("div",{class:K(l.value)},[e("div",y0,[v.showIcon?(d(),p("div",b0,[z(k($t),{class:"mx-auto h-12 w-12 text-gray-400"})])):R("",!0),e("h3",x0,A(v.title),1),e("p",w0,A(v.message),1),e("div",_0,[z(k(ae),{variant:"primary",onClick:f,loading:i.value},{default:J(()=>[Q(A(v.loginButtonText),1)]),_:1},8,["loading"]),v.showRegisterButton?(d(),ce(k(ae),{key:0,variant:"outline",onClick:x[0]||(x[0]=g=>f("register")),loading:i.value},{default:J(()=>[Q(A(v.registerButtonText),1)]),_:1},8,["loading"])):R("",!0)])])],2)],!0)])),z(Lt,{show:u.value,"onUpdate:show":x[1]||(x[1]=g=>u.value=g),"start-with-register":h.value==="register",title:v.title,subtitle:v.message,onSuccess:b,onClose:x[2]||(x[2]=g=>u.value=!1)},null,8,["show","start-with-register","title","subtitle"])]))}});const Ps=Ae(k0,[["__scopeId","data-v-7401f350"]]);const C0={class:"auction-filters"},$0={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},S0={class:"flex flex-wrap gap-2"},M0=["onClick"],A0={class:"space-y-8"},j0={class:"filter-section"},E0={class:"space-y-3"},I0=["value"],T0={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},B0={key:0,class:"text-xs text-gray-500 font-medium"},P0={key:0,class:"filter-section"},R0={class:"space-y-3 max-h-48 overflow-y-auto"},N0=["value"],L0={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},D0={key:0,class:"text-xs text-gray-500 font-medium"},z0={class:"filter-section"},O0={class:"space-y-4"},V0={class:"grid grid-cols-2 gap-2"},F0=["onClick"],q0={class:"pt-2"},U0={class:"grid grid-cols-2 gap-3"},H0=ge({__name:"AuctionFilters",props:{auctionTypeOptions:{default:()=>[{label:"Daily Sale",value:"cash"},{label:"Online Auctions",value:"online"},{label:"Live Auctions",value:"live"}]},locationOptions:{default:()=>[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]},showLocationFilter:{type:Boolean,default:!0}},emits:["filter-change","apply-filters","reset-filters"],setup(t,{emit:s}){const o=t,r=s,n=L([]),a=L([]),u=L({min:"",max:""}),h=[{label:"Under 50,000",min:null,max:5e4},{label:"50,000 - 100,000",min:5e4,max:1e5},{label:"100,000 - 500,000",min:1e5,max:5e5},{label:"500,000 - 1,000,000",min:5e5,max:1e6},{label:"1,000,000 - 5,000,000",min:1e6,max:5e6},{label:"Over 5,000,000",min:5e6,max:null}],c=N(()=>n.value.length>0||a.value.length>0||u.value.min||u.value.max),i=N(()=>{const j=[];if(n.value.forEach(w=>{var M;const _=(M=o.auctionTypeOptions)==null?void 0:M.find(T=>T.value===w);_&&j.push({key:`type-${w}`,label:_.label,type:"auctionType",value:w})}),a.value.forEach(w=>{var M;const _=(M=o.locationOptions)==null?void 0:M.find(T=>T.value===w);_&&j.push({key:`location-${w}`,label:_.label,type:"location",value:w})}),u.value.min||u.value.max){const w=u.value.min||"0",_=u.value.max||"∞";j.push({key:"price-range",label:`${w} - ${_}`,type:"priceRange",value:"custom"})}return j}),l=N(()=>({auctionTypes:n.value,locations:a.value,priceMin:u.value.min?parseFloat(u.value.min):null,priceMax:u.value.max?parseFloat(u.value.max):null})),f=j=>{var w,_;u.value={min:((w=j.min)==null?void 0:w.toString())||"",max:((_=j.max)==null?void 0:_.toString())||""},g()},b=j=>{const w=u.value.min?parseFloat(u.value.min):null,_=u.value.max?parseFloat(u.value.max):null;return w===j.min&&_===j.max},v=()=>{g()},x=(j,w)=>{switch(j){case"auctionType":n.value=n.value.filter(_=>_!==w);break;case"location":a.value=a.value.filter(_=>_!==w);break;case"priceRange":u.value={min:"",max:""};break}g()},g=()=>{r("filter-change",l.value)},S=()=>{n.value=[],a.value=[],u.value={min:"",max:""},r("reset-filters")},P=()=>{S(),g()};return(j,w)=>(d(),p("div",C0,[c.value?(d(),p("div",$0,[e("div",{class:"flex items-center justify-between mb-3"},[w[4]||(w[4]=e("h4",{class:"text-sm font-medium text-blue-900"},"Active Filters",-1)),e("button",{onClick:P,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")]),e("div",S0,[(d(!0),p(ue,null,fe(i.value,_=>(d(),p("span",{key:_.key,class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white text-blue-800 border border-blue-200"},[Q(A(_.label)+" ",1),e("button",{onClick:M=>x(_.type,_.value),class:"ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-100 transition-colors"},w[5]||(w[5]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,M0)]))),128))])])):R("",!0),e("div",A0,[e("div",j0,[w[6]||(w[6]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Auction Type",-1)),e("div",E0,[(d(!0),p(ue,null,fe(j.auctionTypeOptions,_=>(d(),p("label",{key:_.value,class:"flex items-center cursor-pointer group"},[ve(e("input",{"onUpdate:modelValue":w[0]||(w[0]=M=>n.value=M),type:"checkbox",value:_.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:g},null,40,I0),[[nt,n.value]]),e("span",T0,A(_.label),1),_.count!==void 0?(d(),p("span",B0,A(_.count),1)):R("",!0)]))),128))])]),j.showLocationFilter?(d(),p("div",P0,[w[7]||(w[7]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Location",-1)),e("div",R0,[(d(!0),p(ue,null,fe(j.locationOptions,_=>(d(),p("label",{key:_.value,class:"flex items-center cursor-pointer group"},[ve(e("input",{"onUpdate:modelValue":w[1]||(w[1]=M=>a.value=M),type:"checkbox",value:_.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:g},null,40,N0),[[nt,a.value]]),e("span",L0,A(_.label),1),_.count!==void 0?(d(),p("span",D0,A(_.count),1)):R("",!0)]))),128))])])):R("",!0),e("div",z0,[w[10]||(w[10]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Price Range (MWK)",-1)),e("div",O0,[e("div",V0,[(d(),p(ue,null,fe(h,_=>e("button",{key:_.label,onClick:M=>f(_),class:K(["px-3 py-2 text-xs border rounded-lg transition-all duration-200 font-medium text-center",b(_)?"bg-blue-600 border-blue-600 text-white":"bg-white border-gray-300 text-gray-700 hover:border-blue-300 hover:text-blue-600"])},A(_.label),11,F0)),64))]),e("div",q0,[e("div",U0,[e("div",null,[w[8]||(w[8]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Min Price",-1)),ve(e("input",{"onUpdate:modelValue":w[2]||(w[2]=_=>u.value.min=_),type:"number",min:"0",step:"1",placeholder:"0",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:v},null,544),[[Re,u.value.min]])]),e("div",null,[w[9]||(w[9]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Max Price",-1)),ve(e("input",{"onUpdate:modelValue":w[3]||(w[3]=_=>u.value.max=_),type:"number",min:"0",step:"1",placeholder:"Any",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:v},null,544),[[Re,u.value.max]])])])])])])])]))}});const W0=Ae(H0,[["__scopeId","data-v-76900880"]]);const K0={class:"min-h-screen bg-gray-50"},Y0={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},X0=ge({__name:"AppLayout",setup(t){return(s,o)=>(d(),p("div",K0,[o[0]||(o[0]=Ye('<nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex"><div class="flex-shrink-0 flex items-center"><h1 class="text-xl font-bold text-gray-900"> Vertigo AMS </h1></div><div class="hidden sm:ml-6 sm:flex sm:space-x-8"><a href="#" class="border-primary-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Dashboard </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Auctions </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Items </a></div></div><div class="hidden sm:ml-6 sm:flex sm:items-center"><div class="ml-3 relative"><button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true"><span class="sr-only">Open user menu</span><div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center"><span class="text-sm font-medium text-gray-700">U</span></div></button></div></div></div></div></nav>',1)),e("main",null,[e("div",Y0,[ze(s.$slots,"default")])])]))}}),Q0={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"},G0={key:0,class:"col-span-1 lg:col-span-2"},J0={class:"flex items-center mb-4"},Z0=["src","alt"],em={key:1,class:"text-lg font-bold text-gray-900"},tm={key:0,class:"text-sm text-gray-600 mb-4 max-w-md"},sm={key:1,class:"flex space-x-4"},om=["href","aria-label"],rm={class:"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4"},nm={class:"space-y-3"},am=["href"],lm=["href","target","rel"],im={class:"max-w-md"},dm={class:"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4"},um={key:0,class:"text-sm text-gray-600 mb-4"},cm=["placeholder"],mm=["disabled"],fm={class:"flex flex-col md:flex-row md:items-center md:justify-between"},pm={class:"flex items-center space-x-4"},gm={class:"text-sm text-gray-500"},hm={key:0,class:"flex items-center space-x-6 mt-4 md:mt-0"},vm=["href"],ym=["href","target"],bm={key:2,class:"mt-8"};`${new Date().getFullYear()}`;const Ee=ge({__name:"Container",props:{size:{default:"xl"},fluid:{type:Boolean,default:!1},centered:{type:Boolean,default:!0},padding:{type:[Boolean,String],default:!0},paddingX:{type:[Boolean,String]},paddingY:{type:[Boolean,String]}},setup(t){const s=t,o=N(()=>{const r=[];if(s.fluid)r.push("w-full");else if(s.size!=="none"){const n={sm:"max-w-screen-sm",md:"max-w-screen-md",lg:"max-w-screen-lg",xl:"max-w-screen-xl","2xl":"max-w-screen-2xl",full:"max-w-full"};r.push(n[s.size]||"max-w-screen-xl")}if(s.centered&&!s.fluid&&r.push("mx-auto"),s.padding)if(typeof s.padding=="boolean")r.push("px-4 sm:px-6 lg:px-8");else{const n={sm:"px-2 sm:px-4",md:"px-4 sm:px-6",lg:"px-4 sm:px-6 lg:px-8",xl:"px-6 sm:px-8 lg:px-12"};r.push(n[s.padding]||"px-4 sm:px-6 lg:px-8")}if(s.paddingX)if(typeof s.paddingX=="boolean")r.push("px-4 sm:px-6 lg:px-8");else{const n={sm:"px-2 sm:px-4",md:"px-4 sm:px-6",lg:"px-4 sm:px-6 lg:px-8",xl:"px-6 sm:px-8 lg:px-12"};r.push(n[s.paddingX]||"px-4 sm:px-6 lg:px-8")}if(s.paddingY)if(typeof s.paddingY=="boolean")r.push("py-4 sm:py-6 lg:py-8");else{const n={sm:"py-2 sm:py-4",md:"py-4 sm:py-6",lg:"py-4 sm:py-6 lg:py-8",xl:"py-6 sm:py-8 lg:py-12"};r.push(n[s.paddingY]||"py-4 sm:py-6 lg:py-8")}return r.filter(Boolean).join(" ")});return(r,n)=>(d(),p("div",{class:K(o.value)},[ze(r.$slots,"default")],2))}}),xm={class:"relative"},wm={key:0,class:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},_m={class:"flex items-center justify-between p-4 border-b border-gray-200"},km={class:"text-lg font-semibold text-gray-900"},Cm={class:"flex-1 overflow-y-auto p-4"},$m={key:0,class:"text-center py-8"},Sm={key:1,class:"space-y-4"},Mm={class:"flex-shrink-0"},Am=["src","alt"],jm={class:"flex-1 min-w-0"},Em={class:"text-sm font-medium text-gray-900 truncate"},Im={class:"text-sm text-gray-500"},Tm={class:"mt-1"},Bm={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Pm={class:"flex-shrink-0"},Rm=["onClick","disabled"],Nm={key:0,class:"border-t border-gray-200 p-4 space-y-4"},Lm={class:"flex justify-between items-center"},Dm={class:"text-lg font-semibold text-gray-900"},zm={class:"space-y-2"},Om={key:1,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},wr=ge({__name:"CartDrawer",setup(t){const s=Ue(),o=L(!1),r=()=>{o.value=!o.value},n=()=>{o.value=!1},a=async i=>{await s.removeFromCart(i)&&console.log(`${i.name} removed from cart`)},u=()=>{s.clearCart(),n()},h=()=>{n(),window.location.href="/checkout"},c=i=>{switch(i){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return i||"Unknown"}};return(i,l)=>(d(),p("div",xm,[e("button",{onClick:r,class:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200"},[l[1]||(l[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),k(s).cartCount>0?(d(),p("span",wm,A(k(s).cartCount>9?"9+":k(s).cartCount),1)):R("",!0)]),o.value?(d(),p("div",{key:0,class:"fixed inset-0 z-50 overflow-hidden",onClick:n},[l[9]||(l[9]=e("div",{class:"absolute inset-0 bg-black bg-opacity-50 transition-opacity"},null,-1)),e("div",{class:"absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl transform transition-transform duration-300 ease-in-out",onClick:l[0]||(l[0]=Ce(()=>{},["stop"]))},[e("div",_m,[e("h2",km," Shopping Cart ("+A(k(s).cartCount)+") ",1),e("button",{onClick:n,class:"p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"},l[2]||(l[2]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Cm,[k(s).isEmpty?(d(),p("div",$m,l[3]||(l[3]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Your cart is empty",-1),e("p",{class:"text-xs text-gray-400"},"Add some items to get started",-1)]))):(d(),p("div",Sm,[(d(!0),p(ue,null,fe(k(s).items,f=>{var b;return d(),p("div",{key:f.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[e("div",Mm,[e("img",{src:f.image||"/images/placeholder.jpg",alt:f.name,class:"w-16 h-16 object-cover rounded-md"},null,8,Am)]),e("div",jm,[e("h3",Em,A(f.name),1),e("p",Im,A(k(s).formatCurrency(f.target_amount||0)),1),e("div",Tm,[e("span",Bm,A(c((b=f.auction_type)==null?void 0:b.type)),1)])]),e("div",Pm,[e("button",{onClick:v=>a(f),disabled:k(s).loading,class:"p-1 text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50"},l[4]||(l[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Rm)])])}),128))]))]),k(s).isEmpty?R("",!0):(d(),p("div",Nm,[e("div",Lm,[l[5]||(l[5]=e("span",{class:"text-base font-medium text-gray-900"},"Total:",-1)),e("span",Dm,A(k(s).formatCurrency(k(s).cartTotal)),1)]),e("div",zm,[z(k(ae),{onClick:h,class:"w-full bg-blue-600 hover:bg-blue-700 text-white",disabled:k(s).loading},{default:J(()=>l[6]||(l[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),Q(" Proceed to Checkout ")])),_:1,__:[6]},8,["disabled"]),z(k(ae),{onClick:u,variant:"outline",class:"w-full border-gray-300 text-gray-700 hover:bg-gray-50",disabled:k(s).loading},{default:J(()=>l[7]||(l[7]=[Q(" Clear Cart ")])),_:1,__:[7]},8,["disabled"])])])),k(s).loading?(d(),p("div",Om,l[8]||(l[8]=[e("div",{class:"text-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e("p",{class:"mt-2 text-sm text-gray-500"},"Updating cart...")],-1)]))):R("",!0)])])):R("",!0)]))}}),Vm={class:"relative"},Fm=ge({__name:"CartIcon",props:{count:{default:0},buttonClass:{default:""},badgeClass:{default:""}},emits:["click"],setup(t){return(s,o)=>(d(),p("div",Vm,[e("button",{onClick:o[0]||(o[0]=r=>s.$emit("click")),class:K(["relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200",s.buttonClass])},[o[1]||(o[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),s.count>0?(d(),p("span",{key:0,class:K(["absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",s.badgeClass])},A(s.count>99?"99+":s.count),3)):R("",!0)],2)]))}}),qm={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},Um=ge({__name:"CartNavIcon",setup(t){const s=Ue(),o=N(()=>{const r=window.location.pathname;return r==="/cart"||r.startsWith("/spa")||r.startsWith("/home-vue")});return(r,n)=>(d(),ce(Ne(o.value?"router-link":"a"),{to:o.value?"/cart":void 0,href:o.value?void 0:"/cart",class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200 inline-block"},{default:J(()=>[n[0]||(n[0]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),k(s).cartCount>0?(d(),p("span",qm,A(k(s).cartCount>9?"9+":k(s).cartCount),1)):R("",!0)]),_:1,__:[0]},8,["to","href"]))}}),Hm={class:"bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"},Wm={class:"p-2"},Km={class:"flex items-start space-x-4"},Ym={class:"flex-shrink-0"},Xm={class:"relative"},Qm=["src","alt"],Gm={class:"absolute -top-1 -right-1"},Jm={class:"flex-1 min-w-0"},Zm={class:"flex items-start justify-between"},ef={class:"flex-1 pr-4"},tf={class:"text-lg font-semibold text-gray-900 mb-2 leading-tight hover:text-blue-600 transition-colors cursor-pointer"},sf={class:"flex items-center flex-wrap gap-2 mb-2"},of={class:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"},rf={key:0,class:"text-sm text-gray-600 line-clamp-1 mb-2"},nf={class:"flex items-center space-x-3"},af={class:"text-lg font-bold text-gray-900"},lf={key:0,class:"text-sm text-gray-500"},df={class:"flex items-center space-x-1"},uf={class:"mb-2 pt-3 border-t border-gray-100"},cf={class:"flex items-center justify-between px-3"},mf={class:"flex items-center space-x-3"},ff={class:"flex items-center border border-gray-300 rounded-md bg-white"},pf=["disabled"],gf={class:"text-right"},hf={class:"text-lg font-bold text-gray-900"},vf=ge({__name:"CartItemRow",props:{item:{},loading:{type:Boolean,default:!1}},emits:["update-quantity","remove"],setup(t,{emit:s}){const o=t,r=s,n=L(o.item.quantity||1),a=N(()=>{const x=o.item.target_amount||0,g=n.value||1;return x*g});Me(()=>o.item.quantity,x=>{x!==void 0&&x!==n.value&&(n.value=x)});const u=x=>!x||x<=0?"MK 0":`MK ${x.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,h=x=>{switch(x){case"cash":return"success";case"online":return"primary";case"live":return"danger";default:return"secondary"}},c=x=>{switch(x){case"cash":return"Cash Auction";case"online":return"Online Auction";case"live":return"Live Auction";default:return"Unknown"}},i=x=>{switch(x){case"cash":return"bg-green-500";case"online":return"bg-blue-500";case"live":return"bg-red-500";default:return"bg-gray-500"}},l=()=>{n.value<99&&(n.value++,b())},f=()=>{n.value>1&&(n.value--,b())},b=()=>{n.value<1?n.value=1:n.value>99&&(n.value=99),n.value!==o.item.quantity&&r("update-quantity",o.item,n.value)},v=x=>{const g=x.target;g.src="/img/product.jpeg"};return(x,g)=>{var S,P;return d(),p("div",Hm,[e("div",Wm,[e("div",Km,[e("div",Ym,[e("div",Xm,[e("img",{src:x.item.image||x.item.cropped||"/img/product.jpeg",alt:x.item.name,class:"w-16 h-16 object-cover rounded-lg border border-gray-200",onError:v},null,40,Qm),e("div",Gm,[e("div",{class:K([i((S=x.item.auction_type)==null?void 0:S.type),"w-4 h-4 rounded-full border-2 border-white shadow-sm"])},null,2)])])]),e("div",Jm,[e("div",Zm,[e("div",ef,[e("h4",tf,A(x.item.name),1),e("div",sf,[z(k(it),{variant:h((P=x.item.auction_type)==null?void 0:P.type),size:"sm",class:"font-medium px-2 py-1 text-xs"},{default:J(()=>{var j;return[Q(A(c((j=x.item.auction_type)==null?void 0:j.type)),1)]}),_:1},8,["variant"]),e("span",of," ID: "+A(x.item.id),1)]),x.item.description?(d(),p("p",rf,A(x.item.description),1)):R("",!0),e("div",nf,[e("div",af,A(u(x.item.target_amount)),1),(x.item.quantity||1)>1?(d(),p("div",lf," × "+A(x.item.quantity||1),1)):R("",!0)])]),e("div",df,[z(k(ae),{variant:"ghost",size:"sm",onClick:g[0]||(g[0]=j=>x.$emit("remove",x.item)),disabled:x.loading,class:"text-gray-400 hover:text-red-600 hover:bg-red-50 p-2 rounded-lg transition-all duration-200",title:"Remove item from cart"},{default:J(()=>g[2]||(g[2]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:1,__:[2]},8,["disabled"])])])])])]),e("div",uf,[e("div",cf,[e("div",mf,[g[5]||(g[5]=e("span",{class:"text-sm text-gray-600 font-medium"},"Qty:",-1)),e("div",ff,[z(k(ae),{variant:"ghost",size:"xs",onClick:f,disabled:x.loading||(x.item.quantity||1)<=1,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:J(()=>g[3]||(g[3]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),_:1,__:[3]},8,["disabled"]),ve(e("input",{"onUpdate:modelValue":g[1]||(g[1]=j=>n.value=j),type:"number",min:"1",max:"99",class:"w-12 px-2 py-1 text-center text-sm font-medium border-0 focus:ring-0 focus:outline-none",onBlur:b,onKeyup:Ir(b,["enter"]),disabled:x.loading},null,40,pf),[[Re,n.value,void 0,{number:!0}]]),z(k(ae),{variant:"ghost",size:"xs",onClick:l,disabled:x.loading||(x.item.quantity||1)>=99,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:J(()=>g[4]||(g[4]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1,__:[4]},8,["disabled"])])]),e("div",gf,[g[6]||(g[6]=e("div",{class:"text-xs text-gray-500 mb-1"},"Subtotal",-1)),e("div",hf,A(u(a.value)),1)])])])])}}});const Ct=Ae(vf,[["__scopeId","data-v-c5ab099c"]]),_r=Ze("items",()=>{const t=L(null),s=L([]),o=L([]),r=L(!1),n=L(null),a=N(()=>{var I;return((I=t.value)==null?void 0:I.total)||0}),u=N(()=>{var I;return((I=t.value)==null?void 0:I.current_page)||1}),h=N(()=>{var I;return((I=t.value)==null?void 0:I.last_page)||1}),c=N(()=>u.value<h.value),i=async(I={})=>{r.value=!0,n.value=null;try{const U=new URLSearchParams;I.search&&U.append("search",I.search),I.type&&U.append("type",I.type),I.branch_id&&U.append("branch_id",I.branch_id),I.auction_type_id&&U.append("auction_type_id",I.auction_type_id),I.per_page&&U.append("per_page",I.per_page.toString()),I.sort_by&&U.append("sort_by",I.sort_by),I.statuses&&I.statuses.length>0&&I.statuses.forEach(X=>U.append("statuses[]",X)),I.auctionTypes&&I.auctionTypes.length>0&&I.auctionTypes.forEach(X=>U.append("auction_types[]",X)),I.locations&&I.locations.length>0&&I.locations.forEach(X=>U.append("locations[]",X)),I.conditions&&I.conditions.length>0&&I.conditions.forEach(X=>U.append("conditions[]",X)),I.priceMin!==void 0&&I.priceMin!==null&&U.append("price_min",I.priceMin.toString()),I.priceMax!==void 0&&I.priceMax!==null&&U.append("price_max",I.priceMax.toString()),I.dateRange&&U.append("date_range",I.dateRange),I.customDateRange&&(I.customDateRange.from&&U.append("date_from",I.customDateRange.from),I.customDateRange.to&&U.append("date_to",I.customDateRange.to)),I.hasImages&&U.append("has_images","1"),I.hasReserve&&U.append("has_reserve","1"),I.featuredOnly&&U.append("featured_only","1"),I.acceptsPayPal&&U.append("accepts_paypal","1"),I.freeShipping&&U.append("free_shipping","1");const te=await je.get(`/ajax-items?${U.toString()}`);t.value=te.data}catch(U){n.value="Failed to fetch auction items",console.error("Error fetching items:",U)}finally{r.value=!1}};return{items:t,branches:s,auctionTypes:o,loading:r,error:n,totalItems:a,currentPage:u,lastPage:h,hasMorePages:c,fetchItems:i,fetchBranches:async()=>{try{const I=await je.get("/ajax-branches");s.value=I.data}catch(I){console.error("Error fetching branches:",I),s.value=[]}},fetchAuctionTypes:async()=>{try{const I=await je.get("/ajax-auction-types");o.value=I.data}catch(I){console.error("Error fetching auction types:",I),o.value=[]}},getItemById:async I=>{try{return(await je.get(`/ajax-item/${I}`)).data}catch(U){return console.error("Error fetching item:",U),null}},placeBid:async(I,U)=>{try{const te=await je.post(`/place-a-bid/${I}`,{bid_amount:U});return await i(),te.data}catch(te){throw console.error("Error placing bid:",te),te}},addToCart:async I=>{try{return(await je.get(`/add-to-cart/${I}`)).data}catch(U){throw console.error("Error adding to cart:",U),U}},removeFromCart:async I=>{try{return(await je.get(`/remove-from-cart/${I}`)).data}catch(U){throw console.error("Error removing from cart:",U),U}},initializeBranches:I=>{s.value=I},formatCurrency:I=>!I||I<=0?"MK 0":`MK ${I.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,getItemTypeLabel:I=>{switch(I){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return I}},getItemActionLabel:I=>{switch(I){case"cash":return"VIEW DETAILS";case"online":case"live":return"PLACE A BID";default:return"VIEW DETAILS"}},getPriceLabel:I=>{switch(I){case"cash":return"Price:";case"online":case"live":return"Current Bid:";default:return"Price:"}},getItemPrice:I=>{var U;switch((U=I.auction_type)==null?void 0:U.type){case"cash":return I.target_amount||0;case"online":case"live":return I.bid_amount||0;default:return I.target_amount||0}},reset:()=>{t.value=null,s.value=[],o.value=[],r.value=!1,n.value=null}}}),kr=Ze("adverts",()=>{const t=L([]),s=L(!1),o=L(null);return{adverts:t,loading:s,error:o,fetchAdverts:async()=>{s.value=!0,o.value=null;try{const u=await je.get("/api/adverts");t.value=u.data}catch(u){o.value="Failed to fetch advertisements",console.error("Error fetching adverts:",u),t.value=[]}finally{s.value=!1}},initializeAdverts:u=>{t.value=u},reset:()=>{t.value=[],s.value=!1,o.value=null}}}),yf={class:"homepage"},bf={key:0,class:"hero-section relative overflow-hidden"},xf={class:"py-16 lg:py-12"},wf={class:"text-center max-w-5xl mx-auto"},_f={class:"flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4 mb-10"},kf={class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",style:{color:"#0068ff"}},Cf={class:"absolute bottom-0 left-0 w-full overflow-hidden leading-none"},$f={class:"relative block w-full h-16 md:h-20 lg:h-24 text-white",fill:"currentColor",viewBox:"0 0 1200 120",preserveAspectRatio:"none",style:{transform:"rotate(180deg)"}},Sf={class:"category-navigation bg-white border-b relative z-10"},Mf={class:"py-6"},Af={class:"flex justify-center"},jf={class:"relative w-full max-w-lg"},Ef={id:"auctions",class:"auctions-section py-8 bg-white"},If={class:"flex flex-col xl:flex-row gap-6"},Tf={class:"w-full xl:w-72 flex-shrink-0"},Bf={class:"sticky top-6"},Pf={class:"bg-white border border-gray-200 rounded-xl overflow-hidden"},Rf={class:"px-6 py-4 border-b border-gray-100 bg-gray-50"},Nf={class:"flex items-center justify-between"},Lf={class:"p-6"},Df={class:"flex-1 min-w-0"},zf={class:"mb-6"},Of={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},Vf={class:"flex items-center space-x-4"},Ff={class:"text-2xl font-bold text-gray-900"},qf={key:0,class:"text-sm text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg font-medium"},Uf={class:"flex items-center space-x-3"},Hf={key:0,class:"flex justify-center py-16"},Wf={class:"text-center"},Kf={key:1,class:"space-y-8"},Yf={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Xf={key:0,class:"flex justify-center pt-8"},Qf={key:0,class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Gf={key:2,class:"text-center py-16"},Jf={class:"max-w-md mx-auto"},Zf={class:"space-y-3"},ep={class:"stats-section bg-gray-50 py-16"},tp={key:1,class:"fixed bottom-6 right-6 z-50"},sp={class:"absolute -top-2 -right-2 h-6 w-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},op=ge({__name:"Homepage",setup(t){const s=_r(),o=kr(),r=Ue(),n=Le(),a=et(),u=Oo(),h=L(!0),c=L(!1),i=L(""),l=L("cash"),f=L(1),b=L(""),v=L(""),x=L(10),g=L("newest"),S=L({}),P=N(()=>s.items),j=N(()=>s.branches),w=N(()=>s.auctionTypes),_=N(()=>o.adverts),M=N(()=>{if(!_.value)return[];const y=new Date;return _.value.filter(m=>m.date_to?new Date(m.date_to)>y:!0)}),T=N(()=>P.value?P.value.current_page>=P.value.last_page:!0),I=N(()=>{var y;return[{key:"all",label:"All Items",badge:((y=P.value)==null?void 0:y.total)||0},{key:"cash",label:"Daily Sale",badge:void 0},{key:"online",label:"Online Auctions",badge:void 0},{key:"live",label:"Live Auctions",badge:void 0}]}),U=N(()=>w.value.map(y=>({value:y.type,label:y.name}))),te=N(()=>j.value&&j.value.length>0?j.value.map(y=>({value:y.id.toString(),label:y.name})):[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]),X=N(()=>i.value||l.value!=="cash"||Object.keys(S.value).length>0),oe=async()=>{h.value=!0;try{const y={search:i.value,type:l.value,branch_id:b.value,auction_type_id:v.value,per_page:x.value,sort_by:g.value,...S.value};await s.fetchItems(y)}finally{h.value=!1}},G=async()=>{c.value=!0;try{x.value+=10,await oe()}finally{c.value=!1}},V=(y,m)=>{f.value=y;const H=m.key;H==="all"?l.value="":l.value=H,v.value="",oe()},se=()=>{i.value="",oe()},de=xa(()=>{oe()},300),O=()=>{var y;(y=document.getElementById("auctions"))==null||y.scrollIntoView({behavior:"smooth"})},E=()=>{a.push("/about")},W=y=>{a.push(`/item/${y.id}`)},Y=y=>{a.push(`/item/${y.id}`)},ee=y=>{a.push(`/item/${y.id}`)},re=async y=>{var H;console.log("Add to cart:",y.id),await r.addToCart(y)?(n.success(`${y.name} added to cart`),r.trackCartEvent("item_added",{item_id:y.id,item_name:y.name,item_price:y.target_amount,auction_type:(H=y.auction_type)==null?void 0:H.type,source_page:"homepage"})):n.error(r.error||"Failed to add item to cart")},pe=async y=>{var H;console.log("Remove from cart:",y.id),await r.removeFromCart(y)?(n.success(`${y.name} removed from cart`),r.trackCartEvent("item_removed",{item_id:y.id,item_name:y.name,item_price:y.target_amount,auction_type:(H=y.auction_type)==null?void 0:H.type,source_page:"homepage"})):n.error(r.error||"Failed to remove item from cart")},xe=async y=>{console.log("Checkout item:",y.id),await r.addToCart(y,!0)||n.error("Failed to proceed to checkout")},Te=y=>{console.log("Watch item:",y.id)},Pe=y=>{S.value=y,oe()},_e=()=>{S.value={},i.value="",l.value="cash",f.value=1,oe()},F=()=>{oe()},$=()=>({"":"All Items",cash:"Daily Sale Items",online:"Online Auction Items",live:"Live Auction Items"})[l.value]||"Auction Items",C=()=>{var B,Z,le,ne;const y=new URLSearchParams;if(i.value&&y.set("search",i.value),l.value&&y.set("type",l.value),g.value&&g.value!=="newest"&&y.set("sort",g.value),S.value){const ie=S.value;(B=ie.statuses)!=null&&B.length&&y.set("statuses",ie.statuses.join(",")),(Z=ie.auctionTypes)!=null&&Z.length&&y.set("auction_types",ie.auctionTypes.join(",")),(le=ie.locations)!=null&&le.length&&y.set("locations",ie.locations.join(",")),(ne=ie.conditions)!=null&&ne.length&&y.set("conditions",ie.conditions.join(",")),ie.priceMin&&y.set("price_min",ie.priceMin.toString()),ie.priceMax&&y.set("price_max",ie.priceMax.toString()),ie.hasImages&&y.set("has_images","1"),ie.hasReserve&&y.set("has_reserve","1"),ie.featuredOnly&&y.set("featured","1")}const m=y.toString(),H=m?`${window.location.pathname}?${m}`:window.location.pathname;window.history.replaceState({},"",H)},q=()=>{var H,B,Z,le;const y=new URLSearchParams(window.location.search);if(y.get("search")&&(i.value=y.get("search")||""),y.get("type")){const ne=y.get("type")||"";l.value=ne;const ie=I.value.findIndex($e=>$e.key===(ne||"all"));ie!==-1&&(f.value=ie)}y.get("sort")&&(g.value=y.get("sort")||"newest");const m={};y.get("statuses")&&(m.statuses=((H=y.get("statuses"))==null?void 0:H.split(","))||[]),y.get("auction_types")&&(m.auctionTypes=((B=y.get("auction_types"))==null?void 0:B.split(","))||[]),y.get("locations")&&(m.locations=((Z=y.get("locations"))==null?void 0:Z.split(","))||[]),y.get("conditions")&&(m.conditions=((le=y.get("conditions"))==null?void 0:le.split(","))||[]),y.get("price_min")&&(m.priceMin=parseFloat(y.get("price_min")||"0")),y.get("price_max")&&(m.priceMax=parseFloat(y.get("price_max")||"0")),y.get("has_images")&&(m.hasImages=!0),y.get("has_reserve")&&(m.hasReserve=!0),y.get("featured")&&(m.featuredOnly=!0),Object.keys(m).length>0&&(S.value=m)};return Me([i,l,g,S],()=>{C()},{deep:!0}),Ie(async()=>{if(q(),r.isEmpty){const y=window.cartItems||[];y.length>0?r.initializeCart(y):await r.fetchCart()}r.setLastShoppingPage(window.location.href),await Promise.all([s.fetchBranches(),s.fetchAuctionTypes(),o.fetchAdverts(),oe()])}),Me(()=>u.path,y=>{(y==="/"||y==="/homepage")&&r.setLastShoppingPage(window.location.href)},{immediate:!1}),(y,m)=>{const H=Cs("router-link");return d(),p("div",yf,[z(k(Wo),{adverts:M.value},null,8,["adverts"]),!M.value||M.value.length===0?(d(),p("section",bf,[m[11]||(m[11]=Ye('<div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-900" data-v-87e05839></div><div class="absolute inset-0 bg-black bg-opacity-20" data-v-87e05839></div><div class="absolute top-0 left-0 w-full h-full" data-v-87e05839><div class="absolute top-20 left-10 w-72 h-72 bg-white bg-opacity-10 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-300 bg-opacity-20 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-white/5 to-transparent rounded-full" data-v-87e05839></div></div>',3)),z(k(Ee),{class:"relative z-10"},{default:J(()=>[e("div",xf,[e("div",wf,[m[7]||(m[7]=e("div",{class:"inline-flex items-center px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6 border border-white border-opacity-30"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" Trusted by thousands of auctioneers in Malawi ")],-1)),m[8]||(m[8]=e("h1",{class:"text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"},[Q(" Welcome to "),e("span",{class:"block bg-gradient-to-r from-blue-200 to-white bg-clip-text text-transparent"}," Trust Auctioneers ")],-1)),m[9]||(m[9]=e("p",{class:"text-xl lg:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed"}," Experience the future of auction management with our cutting-edge platform. Discover exceptional items, place confident bids, and join a community of passionate collectors. ",-1)),e("div",_f,[z(k(ae),{variant:"primary",size:"md",onClick:O,class:"bg-white text-blue-600 hover:bg-blue-50 hover:text-blue-700 px-6 py-3 text-base font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border-0",style:{color:"#0068ff !important"}},{default:J(()=>[(d(),p("svg",kf,m[4]||(m[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"},null,-1)]))),m[5]||(m[5]=Q(" Explore Live Auctions "))]),_:1,__:[5]}),e("button",{onClick:E,class:"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-6 py-3 text-base font-semibold rounded-lg backdrop-blur-sm transition-all duration-300 inline-flex items-center"},m[6]||(m[6]=[e("svg",{class:"w-4 h-4 mr-2 text-white hover:text-blue-600 transition-colors duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),Q(" Learn More ")]))])])])]),_:1}),e("div",Cf,[(d(),p("svg",$f,m[10]||(m[10]=[e("path",{d:"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z",opacity:".25"},null,-1),e("path",{d:"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z",opacity:".5"},null,-1),e("path",{d:"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"},null,-1)])))])])):R("",!0),e("section",Sf,[z(k(Ee),null,{default:J(()=>[e("div",Mf,[z(k(pd),{modelValue:f.value,"onUpdate:modelValue":m[0]||(m[0]=B=>f.value=B),tabs:I.value,variant:"underline",size:"lg",centered:"",onTabChange:V,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Af,[e("div",jf,[m[13]||(m[13]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),ve(e("input",{type:"text","onUpdate:modelValue":m[1]||(m[1]=B=>i.value=B),placeholder:"Search auctions...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full",onInput:m[2]||(m[2]=(...B)=>k(de)&&k(de)(...B))},null,544),[[Re,i.value]]),i.value?(d(),p("button",{key:0,onClick:se,class:"absolute inset-y-0 right-0 pr-3 flex items-center"},m[12]||(m[12]=[e("svg",{class:"h-4 w-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):R("",!0)])])])]),_:1})]),e("section",Ef,[z(k(Ee),null,{default:J(()=>{var B;return[e("div",If,[e("aside",Tf,[e("div",Bf,[e("div",Pf,[e("div",Rf,[e("div",Nf,[m[14]||(m[14]=e("h3",{class:"text-base font-semibold text-gray-900"},"Filters",-1)),X.value?(d(),p("button",{key:0,onClick:_e,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")):R("",!0)])]),e("div",Lf,[z(k(W0),{"auction-type-options":U.value,"location-options":te.value,"show-location-filter":!0,onFilterChange:Pe,onResetFilters:_e},null,8,["auction-type-options","location-options"])])])])]),e("main",Df,[e("div",zf,[e("div",Of,[e("div",Vf,[e("h1",Ff,A($()),1),(B=P.value)!=null&&B.total?(d(),p("span",qf,A(P.value.total.toLocaleString())+" "+A(P.value.total===1?"item":"items"),1)):R("",!0)]),e("div",Uf,[m[16]||(m[16]=e("span",{class:"text-sm text-gray-600 font-medium"},"Sort by",-1)),ve(e("select",{"onUpdate:modelValue":m[3]||(m[3]=Z=>g.value=Z),onChange:F,class:"text-sm border border-gray-300 rounded-lg px-4 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-medium"},m[15]||(m[15]=[e("option",{value:"newest"},"Newest First",-1),e("option",{value:"oldest"},"Oldest First",-1),e("option",{value:"price_low"},"Price: Low to High",-1),e("option",{value:"price_high"},"Price: High to Low",-1),e("option",{value:"ending_soon"},"Ending Soon",-1)]),544),[[ko,g.value]])])])]),h.value?(d(),p("div",Hf,[e("div",Wf,[z(k(tt),{size:"lg"}),m[17]||(m[17]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading auction items...",-1))])])):P.value&&P.value.data&&P.value.data.length>0?(d(),p("div",Kf,[e("div",Yf,[(d(!0),p(ue,null,fe(P.value.data,Z=>(d(),ce(k(xr),{key:Z.id,item:Z,onViewDetails:W,onPlaceBid:Y,onPreview:ee,onAddToCart:re,onRemoveFromCart:pe,onCheckout:xe,onWatchItem:Te},null,8,["item"]))),128))]),T.value?R("",!0):(d(),p("div",Xf,[z(k(ae),{variant:"outline",size:"lg",onClick:G,loading:c.value,class:"px-8 py-3 bg-white border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-all duration-200 rounded-xl font-medium"},{default:J(()=>[c.value?R("",!0):(d(),p("svg",Qf,m[18]||(m[18]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]))),Q(" "+A(c.value?"Loading...":"Load More Items"),1)]),_:1},8,["loading"])]))])):(d(),p("div",Gf,[e("div",Jf,[m[21]||(m[21]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),m[22]||(m[22]=e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"No auction items found",-1)),m[23]||(m[23]=e("p",{class:"text-gray-600 mb-6"},"We couldn't find any items matching your criteria. Try adjusting your filters or search terms.",-1)),e("div",Zf,[z(k(ae),{variant:"primary",onClick:_e,class:"px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"},{default:J(()=>m[19]||(m[19]=[Q(" Clear All Filters ")])),_:1,__:[19]}),m[20]||(m[20]=e("p",{class:"text-sm text-gray-500"},"or check back later for new auctions",-1))])])]))])])]}),_:1})]),e("section",ep,[z(k(Ee),null,{default:J(()=>m[24]||(m[24]=[e("div",{class:"text-center mb-12"},[e("h2",{class:"text-3xl font-bold text-gray-900 mb-4"},"Why Choose Vertigo AMS?"),e("p",{class:"text-lg text-gray-600 max-w-2xl mx-auto"}," Join thousands of satisfied customers who trust our platform for their auction needs ")],-1),e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-8"},[e("div",{class:"text-center"},[e("div",{class:"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Secure Bidding"),e("p",{class:"text-gray-600"},"Safe and secure bidding process with verified payments")]),e("div",{class:"text-center"},[e("div",{class:"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Real-time Updates"),e("p",{class:"text-gray-600"},"Live auction updates and instant bid notifications")]),e("div",{class:"text-center"},[e("div",{class:"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Trusted Platform"),e("p",{class:"text-gray-600"},"Established reputation with thousands of successful auctions")])],-1)])),_:1,__:[24]})]),z(k(wr)),z(k(Ho)),k(r).cartCount>0?(d(),p("div",tp,[z(H,{to:"/cart",class:"flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"},{default:J(()=>[m[25]||(m[25]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),e("span",sp,A(k(r).cartCount>9?"9+":k(r).cartCount),1)]),_:1,__:[25]})])):R("",!0)])}}});const rs=Ae(op,[["__scopeId","data-v-87e05839"]]),rp={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},np={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},ap={class:"py-6"},lp={class:"flex items-center justify-between"},ip={class:"flex items-center space-x-4"},dp={class:"text-2xl font-bold text-gray-100 tracking-tight"},up={key:0,class:"flex items-center space-x-3"},cp={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},mp={key:0,class:"flex justify-center py-20"},fp={class:"text-center"},pp={key:1,class:"text-center py-20"},gp={class:"max-w-4xl mx-auto"},hp={class:"bg-white rounded-3xl shadow-xl p-12 mb-8"},vp={class:"mb-10"},yp={key:2,class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},bp={class:"lg:col-span-2 space-y-4"},xp={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},wp={class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},_p={class:"flex items-center justify-between"},kp={class:"flex items-center space-x-3"},Cp={class:"flex items-center space-x-3"},$p={class:"text-xs text-gray-600"},Sp={class:"flex items-center space-x-3"},Mp={class:"text-right"},Ap={class:"text-lg font-bold text-gray-900"},jp={class:"flex items-center space-x-1"},Ep={class:"p-6"},Ip={class:"space-y-3"},Tp={key:0,class:"space-y-6"},Bp={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Pp={class:"px-6 py-3 border-b border-gray-200 bg-green-50"},Rp={class:"flex items-center justify-between"},Np={class:"flex items-center space-x-2"},Lp={class:"text-xs text-green-700 bg-green-100 px-2 py-1 rounded-full font-medium"},Dp={class:"text-sm font-medium text-green-700"},zp={class:"p-4 space-y-3"},Op={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Vp={class:"px-6 py-3 border-b border-gray-200 bg-blue-50"},Fp={class:"flex items-center justify-between"},qp={class:"flex items-center space-x-2"},Up={class:"text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full font-medium"},Hp={class:"text-sm font-medium text-blue-700"},Wp={class:"p-4 space-y-3"},Kp={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Yp={class:"px-6 py-3 border-b border-gray-200 bg-red-50"},Xp={class:"flex items-center justify-between"},Qp={class:"flex items-center space-x-2"},Gp={class:"text-xs text-red-700 bg-red-100 px-2 py-1 rounded-full font-medium"},Jp={class:"text-sm font-medium text-red-700"},Zp={class:"p-4 space-y-3"},e1={class:"bg-gradient-to-r from-white to-gray-50 rounded-xl shadow-md border border-gray-200 p-4"},t1={class:"space-y-4"},s1={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},o1={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},r1={class:"bg-white rounded-lg p-4 border border-gray-200"},n1={class:"flex items-center justify-between"},a1={class:"flex items-center space-x-3"},l1={class:"text-sm font-bold text-gray-900"},i1={class:"text-right"},d1={class:"text-lg font-bold text-blue-600"},u1={class:"lg:col-span-1"},c1={class:"sticky top-8 space-y-4"},m1={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},f1={class:"p-6 space-y-4"},p1={class:"space-y-4"},g1={class:"flex justify-between items-center py-3 border-b border-gray-100"},h1={class:"text-gray-700 font-semibold"},v1={class:"text-xl font-bold text-gray-900"},y1={key:0,class:"flex justify-between items-center py-3 bg-green-50 rounded-lg px-4"},b1={class:"flex items-center space-x-3"},x1={class:"text-sm font-medium text-gray-700"},w1={class:"text-sm font-bold text-green-700"},_1={key:1,class:"flex justify-between items-center py-3 bg-blue-50 rounded-lg px-4"},k1={class:"flex items-center space-x-3"},C1={class:"text-sm font-medium text-gray-700"},$1={class:"text-sm font-bold text-blue-700"},S1={key:2,class:"flex justify-between items-center py-3 bg-red-50 rounded-lg px-4"},M1={class:"flex items-center space-x-3"},A1={class:"text-sm font-medium text-gray-700"},j1={class:"text-sm font-bold text-red-700"},E1={class:"bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-6 text-white"},I1={class:"flex justify-between items-center"},T1={class:"text-2xl font-bold"},B1={key:0,class:"w-6 h-6 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},P1=ge({__name:"Cart",setup(t){const s=Ue(),o=Le(),r=L(!1),n=L(!1),a=N(()=>s.isEmpty?"Your Cart is Empty":"Shopping Cart"),u=N(()=>s.cashItems.reduce((x,g)=>x+s.getItemSubtotal(g),0)),h=N(()=>s.onlineItems.reduce((x,g)=>x+s.getItemSubtotal(g),0)),c=N(()=>s.liveItems.reduce((x,g)=>x+s.getItemSubtotal(g),0)),i=async(x,g)=>{if(g<=0){l(x);return}try{await s.updateQuantity(x.id,g)?(o.success("Quantity updated"),s.trackCartEvent("quantity_updated",{item_id:x.id,item_name:x.name,new_quantity:g,item_price:x.target_amount})):o.error(s.error||"Failed to update item quantity")}catch(S){console.error("Error updating quantity:",S),o.error("Failed to update item quantity")}},l=async x=>{try{await s.removeFromCart(x)?(o.success(`${x.name} removed from cart`),s.trackCartEvent("item_removed",{item_id:x.id,item_name:x.name,item_price:x.target_amount,quantity:x.quantity||1})):o.error(s.error||"Failed to remove item from cart")}catch(g){console.error("Error removing item:",g),o.error("Failed to remove item from cart")}},f=async()=>{if(s.isEmpty){o.warning("Your cart is already empty");return}const x=s.cartCount,g=`Are you sure you want to remove all ${x} ${x===1?"item":"items"} from your cart? This action cannot be undone.`;if(confirm(g))try{await s.clearCart()?(o.success(`Cart cleared successfully! ${x} ${x===1?"item":"items"} removed.`),s.trackCartEvent("cart_cleared",{items_count:x,cart_total:s.cartTotal})):o.error(s.error||"Failed to clear cart")}catch(S){console.error("Error clearing cart:",S),o.error("Failed to clear cart")}},b=()=>{try{o.info("Returning to shopping...");const x=s.getLastShoppingPage();let g="/home-vue";x&&x!==window.location.href?(g=x,console.log("Returning to last shopping page:",g)):console.log("No last shopping page found, going to homepage"),setTimeout(()=>{window.location.href=g},500)}catch(x){console.error("Error in handleContinueShopping:",x),window.location.href="/home-vue"}},v=async()=>{if(s.isEmpty){o.warning("Your cart is empty. Add some items before proceeding to checkout.");return}if(s.cartTotal<=0){o.warning("Cart total must be greater than zero to proceed.");return}r.value=!0;try{o.info("Validating cart items...");const x=await s.validateCartItems();if(!x.valid&&(x.issues.forEach(g=>{o.warning(g)}),s.isEmpty)){o.error("Your cart is now empty. Please add items before proceeding."),r.value=!1;return}o.success(`Proceeding to checkout with ${s.cartCount} ${s.cartCount===1?"item":"items"}...`),s.trackCartEvent("checkout_initiated",{items_count:s.cartCount,cart_total:s.cartTotal}),await new Promise(g=>setTimeout(g,500)),window.location.href="/checkout"}catch(x){console.error("Checkout error:",x),o.error("Failed to proceed to checkout. Please try again."),r.value=!1}};return Ie(async()=>{const x=window.cartItems;if(s.initializeCart(x),s.trackCartEvent("cart_page_viewed",{items_count:s.cartCount,cart_total:s.cartTotal}),!s.isEmpty&&s.isOnline)try{const g=await s.validateCartItems();!g.valid&&g.issues.length>0&&g.issues.forEach(S=>{o.warning(S)})}catch(g){console.warn("Cart validation failed on mount:",g)}}),(x,g)=>(d(),p("div",rp,[e("div",np,[g[5]||(g[5]=Ye('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-06704d34></div><div class="absolute inset-0" data-v-06704d34><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-06704d34></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-06704d34></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-06704d34></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-06704d34></div></div>',2)),z(k(Ee),{class:"relative z-10"},{default:J(()=>[e("div",ap,[e("div",lp,[e("div",ip,[g[2]||(g[2]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[e("h1",dp,A(a.value),1),g[1]||(g[1]=e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Shopping Cart")])],-1))])]),k(s).isEmpty?R("",!0):(d(),p("div",up,[z(k(ae),{variant:"outline",size:"sm",onClick:b,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:J(()=>g[3]||(g[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Continue Shopping ")])),_:1,__:[3]}),z(k(ae),{size:"sm",onClick:v,loading:r.value,class:"!bg-gradient-to-r !from-blue-500 !to-indigo-600 !text-white hover:!from-blue-600 hover:!to-indigo-700 font-semibold shadow-lg hover:shadow-xl text-sm transition-all duration-200 border-0"},{default:J(()=>[r.value?R("",!0):(d(),p("svg",cp,g[4]||(g[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),Q(" "+A(r.value?"Processing...":"Checkout"),1)]),_:1},8,["loading"])]))])])]),_:1})]),z(k(Ee),{class:"py-12"},{default:J(()=>[k(s).loading?(d(),p("div",mp,[e("div",fp,[z(k(tt),{size:"lg"}),g[6]||(g[6]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading your cart...",-1))])])):k(s).isEmpty?(d(),p("div",pp,[e("div",gp,[e("div",hp,[e("div",vp,[g[8]||(g[8]=e("div",{class:"relative inline-block mb-8"},[e("div",{class:"w-32 h-32 bg-gradient-to-br from-blue-100 via-blue-200 to-blue-300 rounded-full flex items-center justify-center mx-auto shadow-lg"},[e("svg",{class:"w-16 h-16 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])]),e("div",{class:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"}),e("div",{class:"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 rounded-full animate-pulse"})],-1)),g[9]||(g[9]=e("h3",{class:"text-3xl font-bold text-gray-900 mb-4"},"Your cart is waiting for treasures!",-1)),g[10]||(g[10]=e("p",{class:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto"}," Discover amazing auction items and start building your collection. From vintage collectibles to modern masterpieces. ",-1)),z(k(ae),{size:"lg",onClick:b,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"},{default:J(()=>g[7]||(g[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1),Q(" Start Shopping Now ")])),_:1,__:[7]})])]),g[11]||(g[11]=e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-6"},[e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Cash Auctions"),e("p",{class:"text-gray-600 mb-4"},"Immediate purchases with instant ownership transfer"),e("div",{class:"text-sm text-green-600 font-medium"},"• Quick checkout • Instant delivery")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Online Auctions"),e("p",{class:"text-gray-600 mb-4"},"Bid from anywhere, anytime with our online platform"),e("div",{class:"text-sm text-blue-600 font-medium"},"• Remote bidding • Extended timeframes")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Live Auctions"),e("p",{class:"text-gray-600 mb-4"},"Experience the thrill of real-time competitive bidding"),e("div",{class:"text-sm text-red-600 font-medium"},"• Real-time bidding • Live auctioneer")])],-1))])])):(d(),p("div",yp,[e("div",bp,[e("div",xp,[e("div",wp,[e("div",_p,[e("div",kp,[e("div",Cp,[g[13]||(g[13]=e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[g[12]||(g[12]=e("h3",{class:"text-lg font-bold text-gray-900"},"Your Items",-1)),e("p",$p,A(k(s).cartCount)+" "+A(k(s).cartCount===1?"item":"items")+" selected",1)])]),z(k(it),{variant:"default",size:"sm",class:"bg-blue-100 text-blue-800 font-bold px-2 py-1"},{default:J(()=>[Q(A(k(s).cartCount),1)]),_:1})]),e("div",Sp,[e("div",Mp,[g[14]||(g[14]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",Ap,A(k(s).formatCurrency(k(s).cartTotal)),1)]),e("div",jp,[z(k(ae),{variant:"ghost",size:"sm",onClick:g[0]||(g[0]=S=>n.value=!n.value),class:"text-gray-500 hover:text-blue-600 hover:bg-blue-50 p-2 rounded-md",title:n.value?"Show unified view":"Group by auction type"},{default:J(()=>g[15]||(g[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)])),_:1,__:[15]},8,["title"]),z(k(ae),{variant:"ghost",size:"sm",onClick:f,disabled:k(s).loading,class:"text-gray-500 hover:text-red-600 hover:bg-red-50 p-2 rounded-md",title:"Clear all items"},{default:J(()=>g[16]||(g[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:1,__:[16]},8,["disabled"])])])])]),e("div",Ep,[e("div",Ip,[(d(!0),p(ue,null,fe(k(s).items,S=>(d(),ce(k(Ct),{key:`all-${S.id}`,item:S,onUpdateQuantity:i,onRemove:l},null,8,["item"]))),128))])])]),n.value?(d(),p("div",Tp,[k(s).cashItems.length>0?(d(),p("div",Bp,[e("div",Pp,[e("div",Rp,[e("div",Np,[g[17]||(g[17]=e("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),g[18]||(g[18]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Cash Auction",-1)),e("span",Lp,A(k(s).cashItems.length),1)]),e("span",Dp,A(k(s).formatCurrency(u.value)),1)])]),e("div",zp,[(d(!0),p(ue,null,fe(k(s).cashItems,S=>(d(),ce(k(Ct),{key:`cash-${S.id}`,item:S,onUpdateQuantity:i,onRemove:l},null,8,["item"]))),128))])])):R("",!0),k(s).onlineItems.length>0?(d(),p("div",Op,[e("div",Vp,[e("div",Fp,[e("div",qp,[g[19]||(g[19]=e("div",{class:"w-3 h-3 bg-blue-500 rounded-full"},null,-1)),g[20]||(g[20]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Online Auction",-1)),e("span",Up,A(k(s).onlineItems.length),1)]),e("span",Hp,A(k(s).formatCurrency(h.value)),1)])]),e("div",Wp,[(d(!0),p(ue,null,fe(k(s).onlineItems,S=>(d(),ce(k(Ct),{key:`online-${S.id}`,item:S,onUpdateQuantity:i,onRemove:l},null,8,["item"]))),128))])])):R("",!0),k(s).liveItems.length>0?(d(),p("div",Kp,[e("div",Yp,[e("div",Xp,[e("div",Qp,[g[21]||(g[21]=e("div",{class:"w-3 h-3 bg-red-500 rounded-full"},null,-1)),g[22]||(g[22]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Live Auction",-1)),e("span",Gp,A(k(s).liveItems.length),1)]),e("span",Jp,A(k(s).formatCurrency(c.value)),1)])]),e("div",Zp,[(d(!0),p(ue,null,fe(k(s).liveItems,S=>(d(),ce(k(Ct),{key:`live-${S.id}`,item:S,onUpdateQuantity:i,onRemove:l},null,8,["item"]))),128))])])):R("",!0)])):R("",!0),e("div",e1,[e("div",t1,[e("div",s1,[z(k(ae),{variant:"outline",onClick:f,disabled:k(s).loading||k(s).isEmpty,class:"border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:J(()=>g[23]||(g[23]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),Q(" Clear Cart ")])),_:1,__:[23]},8,["disabled"]),z(k(ae),{variant:"outline",onClick:b,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:J(()=>g[24]||(g[24]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Continue Shopping ")])),_:1,__:[24]}),z(k(ae),{onClick:v,loading:r.value,disabled:k(s).isEmpty,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:J(()=>[r.value?R("",!0):(d(),p("svg",o1,g[25]||(g[25]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),Q(" "+A(r.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading","disabled"])]),e("div",r1,[e("div",n1,[e("div",a1,[g[27]||(g[27]=e("div",{class:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[g[26]||(g[26]=e("p",{class:"text-xs text-gray-600"},"Items in Cart",-1)),e("p",l1,A(k(s).cartCount)+" "+A(k(s).cartCount===1?"item":"items"),1)])]),e("div",i1,[g[28]||(g[28]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",d1,A(k(s).formatCurrency(k(s).cartTotal)),1)])])])])])]),e("div",u1,[e("div",c1,[e("div",m1,[g[34]||(g[34]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold"},"Order Summary"),e("p",{class:"text-blue-100 text-xs"},"Review your selection")])])],-1)),e("div",f1,[e("div",p1,[e("div",g1,[e("span",h1,"Items ("+A(k(s).cartCount)+")",1),e("span",v1,A(k(s).formatCurrency(k(s).cartTotal)),1)]),k(s).cashItems.length>0?(d(),p("div",y1,[e("div",b1,[g[29]||(g[29]=e("div",{class:"w-4 h-4 bg-green-500 rounded-full"},null,-1)),e("span",x1,"Cash Items ("+A(k(s).cashItems.length)+")",1)]),e("span",w1,A(k(s).formatCurrency(u.value)),1)])):R("",!0),k(s).onlineItems.length>0?(d(),p("div",_1,[e("div",k1,[g[30]||(g[30]=e("div",{class:"w-4 h-4 bg-blue-500 rounded-full"},null,-1)),e("span",C1,"Online Items ("+A(k(s).onlineItems.length)+")",1)]),e("span",$1,A(k(s).formatCurrency(h.value)),1)])):R("",!0),k(s).liveItems.length>0?(d(),p("div",S1,[e("div",M1,[g[31]||(g[31]=e("div",{class:"w-4 h-4 bg-red-500 rounded-full"},null,-1)),e("span",A1,"Live Items ("+A(k(s).liveItems.length)+")",1)]),e("span",j1,A(k(s).formatCurrency(c.value)),1)])):R("",!0)]),e("div",E1,[e("div",I1,[g[32]||(g[32]=e("span",{class:"text-lg font-bold"},"Grand Total",-1)),e("span",T1,A(k(s).formatCurrency(k(s).cartTotal)),1)])]),z(k(ae),{size:"lg",class:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",loading:r.value,onClick:v},{default:J(()=>[r.value?R("",!0):(d(),p("svg",B1,g[33]||(g[33]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),Q(" "+A(r.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading"])])]),g[35]||(g[35]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),Q(" Secure & Trusted ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" SSL encrypted checkout process ")])])],-1))])])]))]),_:1})]))}});const ns=Ae(P1,[["__scopeId","data-v-06704d34"]]),R1={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},N1={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},L1={class:"py-6"},D1={class:"flex items-center justify-between"},z1={class:"flex items-center space-x-3"},O1={key:0,class:"flex justify-center py-20"},V1={class:"text-center"},F1={key:1,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},q1={class:"lg:col-span-2 space-y-6"},U1={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},H1={class:"p-6"},W1={class:"flex items-center justify-between"},K1={class:"flex items-center"},Y1={key:0,class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20"},X1={key:1},Q1={class:"ml-3"},G1={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},J1={key:0,class:"p-6"},Z1={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},eg={class:"flex justify-end space-x-3"},tg={key:1,class:"p-6"},sg={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},og={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},rg={class:"flex justify-between space-x-3"},ng={key:2,class:"p-6"},ag={class:"space-y-4"},lg={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ig=["onClick"],dg={class:"flex items-center space-x-3"},ug={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},cg={class:"flex items-center space-x-2"},mg={key:0,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fg={key:1,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},pg={key:2,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gg={key:3,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},hg={key:4,class:"ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full"},vg={key:0,class:"space-y-4 p-4 bg-blue-50 rounded-lg"},yg={class:"bg-white rounded-lg p-4 border border-blue-200"},bg={class:"bg-gray-50 rounded-lg p-3 border"},xg={class:"flex justify-between items-center"},wg={key:0,class:"mt-2 p-2 bg-red-50 rounded border border-red-200"},_g={key:1,class:"space-y-4 p-4 bg-gray-50 rounded-lg"},kg={class:"grid grid-cols-1 gap-4"},Cg={class:"grid grid-cols-2 gap-4"},$g={key:0,class:"space-y-2"},Sg={key:0,class:"flex items-center space-x-2 text-sm"},Mg={key:0,class:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},Ag={key:1,class:"flex items-center space-x-2 text-xs text-gray-500"},jg={class:"flex-1 bg-gray-200 rounded-full h-1 ml-2"},Eg={key:2,class:"space-y-4 p-4 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border border-green-200"},Ig={class:"grid grid-cols-1 gap-4"},Tg={class:"grid grid-cols-2 gap-4"},Bg={class:"flex justify-between space-x-3"},Pg={key:3,class:"p-6"},Rg={class:"space-y-6"},Ng={class:"bg-gray-50 rounded-lg p-4"},Lg={class:"space-y-3"},Dg={class:"flex justify-between"},zg={class:"text-gray-600"},Og={class:"font-medium"},Vg={class:"flex justify-between"},Fg={class:"font-medium"},qg={class:"border-t pt-3 flex justify-between"},Ug={class:"text-lg font-bold text-gray-900"},Hg={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Wg={class:"bg-gray-50 rounded-lg p-4"},Kg={class:"space-y-2 text-sm"},Yg={class:"bg-gray-50 rounded-lg p-4"},Xg={class:"space-y-2 text-sm"},Qg={class:"bg-gray-50 rounded-lg p-4"},Gg={class:"text-sm"},Jg={key:0,class:"text-gray-600"},Zg={key:1,class:"text-gray-600"},eh={key:2,class:"text-gray-600"},th={key:3,class:"text-gray-600"},sh={key:0,class:"p-4 bg-blue-50 border border-blue-200 rounded-lg"},oh={class:"flex items-center space-x-3"},rh={class:"flex-1"},nh={class:"text-sm text-blue-700"},ah={key:1,class:"p-4 bg-red-50 border border-red-200 rounded-lg"},lh={class:"flex items-start space-x-3"},ih={class:"flex-1"},dh={class:"text-sm text-red-700"},uh={class:"flex items-start space-x-3"},ch={class:"flex justify-between space-x-3"},mh={key:0},fh={key:1},ph={key:2},gh={class:"lg:col-span-1"},hh={class:"sticky top-8 space-y-4"},vh={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},yh={class:"px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white"},bh={class:"flex items-center space-x-3"},xh={class:"text-green-100 text-xs"},wh={class:"p-6 space-y-4"},_h={class:"space-y-3 max-h-64 overflow-y-auto"},kh={class:"flex-1 min-w-0"},Ch={class:"text-sm font-medium text-gray-900 truncate"},$h={class:"text-xs text-gray-500"},Sh={class:"text-sm font-medium text-gray-900"},Mh={class:"space-y-3 pt-4 border-t border-gray-200"},Ah={class:"flex justify-between text-sm"},jh={class:"font-medium"},Eh={class:"flex justify-between text-sm"},Ih={class:"font-medium"},Th={class:"flex justify-between text-lg font-bold pt-3 border-t border-gray-200"},Bh={class:"text-green-600"},Ph=ge({__name:"Checkout",setup(t){const s=Ue(),o=Le(),r=L(!1),n=L(!1),a=L(!1),u=L(!1),h=L(!1),c=L(""),i=L(""),l=L("customer"),f=L(!1),b=L(""),v=L(""),x=L(!1),g=L([{id:"customer",title:"Customer Info",description:"Contact details",active:!0,completed:!1},{id:"billing",title:"Billing Address",description:"Billing information",active:!1,completed:!1},{id:"payment",title:"Payment",description:"Payment method",active:!1,completed:!1},{id:"review",title:"Review",description:"Confirm order",active:!1,completed:!1}]),S=L({firstName:"",lastName:"",email:"",phone:""}),P=L({firstName:"",lastName:"",email:"",phone:""}),j=L({address:"",city:"",state:"",postalCode:"",country:""}),w=L({address:"",city:"",state:"",postalCode:"",country:""}),_=L({method:"dpopay",cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:"",currency:"MWK"}),M=L({cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""}),T=L([{id:"dpopay",name:"DPO Pay",description:"Pay with DPO Pay - Credit Cards, Mobile Money & Bank Transfer",enabled:!0,icon:"credit-card",currencies:["MWK","USD","EUR","GBP","ZAR"]},{id:"cash",name:"Cash on Delivery",description:"Pay when you receive your items",enabled:!0},{id:"bank",name:"Bank Transfer",description:"Direct bank transfer (Coming Soon)",enabled:!1},{id:"card",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card (Coming Soon)",enabled:!1}]),I=(y,m)=>y.completed?"bg-green-500 text-white":y.active?"bg-blue-500 text-white":"bg-gray-200 text-gray-500",U=()=>{window.location.href="/cart"},te=()=>{P.value={firstName:"",lastName:"",email:"",phone:""};let y=!1;if(S.value.firstName.trim()||(P.value.firstName="First name is required",y=!0),S.value.lastName.trim()||(P.value.lastName="Last name is required",y=!0),S.value.email.trim()?/\S+@\S+\.\S+/.test(S.value.email)||(P.value.email="Please enter a valid email address",y=!0):(P.value.email="Email is required",y=!0),S.value.phone.trim()||(P.value.phone="Phone number is required",y=!0),y){o.error("Please fix the errors below");return}n.value=!0,setTimeout(()=>{O(),n.value=!1,o.success("Customer information saved successfully")},1e3)},X=()=>{w.value={address:"",city:"",state:"",postalCode:"",country:""};let y=!1;if(j.value.address.trim()||(w.value.address="Street address is required",y=!0),j.value.city.trim()||(w.value.city="City is required",y=!0),j.value.state.trim()||(w.value.state="State/Province is required",y=!0),j.value.postalCode.trim()||(w.value.postalCode="Postal code is required",y=!0),j.value.country.trim()||(w.value.country="Country is required",y=!0),y){o.error("Please fix the errors below");return}a.value=!0,setTimeout(()=>{O(),a.value=!1,o.success("Billing address saved successfully")},1e3)},oe=()=>{M.value={cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""};let y=!1;if(_.value.method==="dpopay")_.value.customerName.trim()?_.value.customerName.trim().length<2&&(M.value.customerName="Please enter a valid name",y=!0):(M.value.customerName="Full name is required",y=!0),_.value.customerEmail.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(_.value.customerEmail)||(M.value.customerEmail="Please enter a valid email address",y=!0):(M.value.customerEmail="Email address is required",y=!0),_.value.customerPhone.trim()&&(/^[\+]?[0-9\-\(\)\s]+$/.test(_.value.customerPhone)||(M.value.customerPhone="Please enter a valid phone number",y=!0));else if(_.value.method==="card"){if(_.value.cardNumber.replace(/\s/g,"")?x.value||(M.value.cardNumber||(M.value.cardNumber="Please enter a valid card number"),y=!0):(M.value.cardNumber="Card number is required",y=!0),!_.value.expiryDate.trim())M.value.expiryDate="Expiry date is required",y=!0;else if(_.value.expiryDate.length!==5)M.value.expiryDate="Please enter a valid expiry date (MM/YY)",y=!0;else{const[H,B]=_.value.expiryDate.split("/"),Z=new Date,le=Z.getFullYear()%100,ne=Z.getMonth()+1,ie=parseInt(H),$e=parseInt(B);ie<1||ie>12?(M.value.expiryDate="Invalid month",y=!0):($e<le||$e===le&&ie<ne)&&(M.value.expiryDate="Card has expired",y=!0)}if(!_.value.cvv.trim())M.value.cvv="CVV is required",y=!0;else{const H=b.value==="American Express"?4:3;_.value.cvv.length!==H&&(M.value.cvv=`CVV must be ${H} digits`,y=!0)}_.value.cardName.trim()?_.value.cardName.trim().length<2&&(M.value.cardName="Please enter a valid name",y=!0):(M.value.cardName="Name on card is required",y=!0)}else _.value.method;if(y){o.error("Please fix the errors below");return}u.value=!0,setTimeout(()=>{O(),u.value=!1,o.success("Payment method saved successfully")},1e3)},G=y=>{let m=0,H=!1;for(let B=y.length-1;B>=0;B--){let Z=parseInt(y.charAt(B));H&&(Z*=2,Z>9&&(Z-=9)),m+=Z,H=!H}return m%10===0},V=async()=>{if(c.value="",i.value="",!f.value){const y="Please agree to the terms and conditions to continue";c.value=y,o.error(y);return}h.value=!0,i.value="Initializing order...";try{if(_.value.method==="dpopay")i.value="Preparing DPO Pay transaction...",await se();else if(_.value.method==="cash")i.value="Processing cash on delivery order...",await de();else{const y="Selected payment method is not available yet";c.value=y,o.error(y),h.value=!1,i.value=""}}catch(y){console.error("Order placement error:",y);let m="Failed to place order. Please try again.";y.message&&(m=y.message),c.value=m,o.error(m),h.value=!1,i.value=""}},se=async()=>{var y;try{if(s.isEmpty)throw new Error("Your cart is empty. Please add items before checkout.");if(s.cartTotal<=0)throw new Error("Invalid cart total. Please refresh and try again.");if(s.cartTotal>999999.99)throw new Error("Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.");const m={customer:{firstName:S.value.firstName,lastName:S.value.lastName,email:S.value.email,phone:S.value.phone},billing:{address:j.value.address,city:j.value.city,state:j.value.state,postalCode:j.value.postalCode,country:j.value.country},cart:{items:s.items,total:s.cartTotal},currency:"MWK"};i.value="Creating secure payment token...",o.info("Creating secure payment token...");const H=await fetch("/dpopay/checkout-payment",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||"",Accept:"application/json"},body:JSON.stringify(m)});if(!H.ok){let Z="Payment processing failed. Please try again.",le=null,ne=null;try{const ie=await H.json();ie.message&&(Z=ie.message),le=ie.error_code,ne=ie.technical_error||ie.error,(le||ne)&&console.error("DPO Pay API Error:",{code:le,message:Z,technical:ne})}catch{try{const $e=await H.text();console.error("DPO Pay Raw Error Response:",$e),$e.includes("Payment amount cannot exceed")?Z="Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.":$e.includes("Invalid company token")?Z="Payment gateway configuration error. Please contact support.":$e.includes("Service Type not active")?Z="Payment service temporarily unavailable. Please try again later.":$e&&(Z="Payment processing error. Please try again or contact support.")}catch{Z=`Connection error (${H.status}). Please check your internet connection and try again.`}}throw new Error(Z)}const B=await H.json();if(B.success&&B.payment_url){i.value="Payment token created! Redirecting to DPO Pay...",o.success("Payment token created! Redirecting to DPO Pay..."),localStorage.setItem("vertigo_order_ref",B.order_reference),localStorage.setItem("vertigo_transaction_token",B.transaction_token),localStorage.setItem("vertigo_payment_amount",s.cartTotal.toString()),localStorage.setItem("vertigo_payment_currency",_.value.currency);let Z=3;const le=setInterval(()=>{i.value=`Redirecting to DPO Pay in ${Z} seconds...`,Z--,Z<0&&(clearInterval(le),i.value="Redirecting to payment gateway...",window.location.href=B.payment_url)},1e3)}else{let Z=B.message||"Failed to create payment token";throw(B.error_code||B.technical_error)&&console.error("DPO Pay API Error Details:",{code:B.error_code,message:Z,technical:B.technical_error}),new Error(Z)}}catch(m){console.error("DPO Payment error:",m);let H="Failed to process DPO payment. Please try again.";m.message.includes("Cart total exceeds")?H=m.message:m.message.includes("HTTP error")?H="Unable to connect to payment gateway. Please check your internet connection and try again.":m.message.includes("cart is empty")?H="Your cart is empty. Please add items before proceeding to checkout.":m.message.includes("Invalid cart total")?H="There was an issue with your cart total. Please refresh the page and try again.":m.message.includes("Failed to create payment token")&&(H="Payment token creation failed. This may be a temporary issue with the payment gateway. Please try again in a few moments."),c.value=H,o.error(H),h.value=!1,i.value=""}},de=async()=>{try{o.info("Processing cash on delivery order..."),await new Promise(y=>setTimeout(y,2e3)),s.clearCart(),o.success("Order placed successfully! You will pay on delivery."),setTimeout(()=>{window.location.href="/"},2e3),h.value=!1}catch(y){console.error("Cash on delivery error:",y),o.error("Failed to process cash on delivery order"),h.value=!1}},O=()=>{const y=g.value.findIndex(m=>m.id===l.value);g.value[y].completed=!0,g.value[y].active=!1,y+1<g.value.length&&(g.value[y+1].active=!0,l.value=g.value[y+1].id)},E=()=>{const y=g.value.findIndex(m=>m.id===l.value);y>0&&(g.value[y].active=!1,g.value[y-1].active=!0,g.value[y-1].completed=!1,l.value=g.value[y-1].id)},W=()=>T.value.find(y=>y.id===_.value.method),Y=()=>{c.value="",i.value=""},ee=y=>{const m=y.replace(/\s/g,"");return/^4/.test(m)?"Visa":/^5[1-5]/.test(m)||/^2[2-7]/.test(m)?"Mastercard":/^3[47]/.test(m)?"American Express":/^6/.test(m)?"Discover":""},re=y=>{switch(y){case"American Express":return{minLength:15,maxLength:15,formatted:18};case"Visa":case"Mastercard":case"Discover":return{minLength:16,maxLength:16,formatted:19};default:return{minLength:13,maxLength:19,formatted:23}}},pe=()=>re(b.value).formatted,xe=y=>{let m=y.target.value.replace(/\s/g,"");const H=ee(m);b.value=H;const B=re(H);m.length>B.maxLength&&(m=m.substring(0,B.maxLength));let Z="";if(H==="American Express")for(let le=0;le<m.length;le++)(le===4||le===10)&&(Z+=" "),Z+=m[le];else for(let le=0;le<m.length;le++)le>0&&le%4===0&&(Z+=" "),Z+=m[le];_.value.cardNumber=Z,Te(m)},Te=y=>{const m=re(b.value);if(M.value.cardNumber="",v.value="",x.value=!1,y.length!==0){if(y.length<m.minLength){y.length>=4&&(M.value.cardNumber=`Card number must be ${m.minLength} digits`);return}if(b.value&&y.length!==m.maxLength){M.value.cardNumber=`${b.value} cards must be ${m.maxLength} digits`;return}if(!b.value&&y.length>16){M.value.cardNumber="Card number is too long";return}y.length>=m.minLength&&(G(y)?(v.value=`Valid ${b.value||"card"} number`,x.value=!0,M.value.cardNumber=""):(M.value.cardNumber="Invalid card number",x.value=!1))}},Pe=y=>{const m=y.target.value.replace(/\s/g,""),H=re(b.value);if(!([8,9,27,13,46,37,38,39,40].indexOf(y.keyCode)!==-1||y.keyCode===65&&y.ctrlKey===!0||y.keyCode===67&&y.ctrlKey===!0||y.keyCode===86&&y.ctrlKey===!0||y.keyCode===88&&y.ctrlKey===!0||y.keyCode===90&&y.ctrlKey===!0)){if(m.length>=H.maxLength){y.preventDefault();return}(y.shiftKey||y.keyCode<48||y.keyCode>57)&&(y.keyCode<96||y.keyCode>105)&&y.preventDefault()}},_e=y=>{let m=y.target.value.replace(/\D/g,"");if(m.length>=2&&(m=m.substring(0,2)+"/"+m.substring(2,4)),_.value.expiryDate=m,m.length===5){const[H,B]=m.split("/"),Z=new Date,le=Z.getFullYear()%100,ne=Z.getMonth()+1,ie=parseInt(H),$e=parseInt(B);ie<1||ie>12?M.value.expiryDate="Invalid month":$e<le||$e===le&&ie<ne?M.value.expiryDate="Card has expired":M.value.expiryDate=""}},F=y=>{[8,9,27,13,46].indexOf(y.keyCode)!==-1||y.keyCode===65&&y.ctrlKey===!0||y.keyCode===67&&y.ctrlKey===!0||y.keyCode===86&&y.ctrlKey===!0||y.keyCode===88&&y.ctrlKey===!0||(y.shiftKey||y.keyCode<48||y.keyCode>57)&&(y.keyCode<96||y.keyCode>105)&&y.preventDefault()},$=y=>{let m=y.target.value.replace(/\D/g,"");const H=b.value==="American Express"?4:3;m=m.substring(0,H),_.value.cvv=m,m.length>=3&&(M.value.cvv="")},C=y=>{[8,9,27,13,46].indexOf(y.keyCode)!==-1||y.keyCode===65&&y.ctrlKey===!0||y.keyCode===67&&y.ctrlKey===!0||y.keyCode===86&&y.ctrlKey===!0||y.keyCode===88&&y.ctrlKey===!0||(y.shiftKey||y.keyCode<48||y.keyCode>57)&&(y.keyCode<96||y.keyCode>105)&&y.preventDefault()},q=y=>{let m=y.target.value.replace(/[^a-zA-Z\s\-']/g,"");m=m.replace(/\b\w/g,H=>H.toUpperCase()),_.value.cardName=m,m.trim().length>0&&(M.value.cardName="")};return Ie(()=>{const y=window.cartItems||[];if(s.initializeCart(y),s.isEmpty&&l.value==="customer"&&(o.warning("Your cart is empty. Redirecting to homepage..."),setTimeout(()=>{window.location.href="/"},2e3)),_.value.cardNumber){const m=_.value.cardNumber.replace(/\s/g,"");Te(m)}}),(y,m)=>(d(),p("div",R1,[e("div",N1,[m[20]||(m[20]=Ye('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-a7a2dac4></div><div class="absolute inset-0" data-v-a7a2dac4><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-a7a2dac4></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-a7a2dac4></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-a7a2dac4></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-a7a2dac4></div></div>',2)),z(k(Ee),{class:"relative z-10"},{default:J(()=>[e("div",L1,[e("div",D1,[m[19]||(m[19]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Secure Checkout "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-blue-200 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Checkout")])])])],-1)),e("div",z1,[z(k(ae),{variant:"outline",size:"sm",onClick:U,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:J(()=>m[18]||(m[18]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Back to Cart ")])),_:1,__:[18]})])])])]),_:1})]),z(k(Ee),{class:"py-12"},{default:J(()=>{var H;return[r.value?(d(),p("div",O1,[e("div",V1,[z(k(tt),{size:"lg"}),m[21]||(m[21]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading checkout...",-1))])])):(d(),p("div",F1,[e("div",q1,[e("div",U1,[m[23]||(m[23]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900"},"Checkout Progress")],-1)),e("div",H1,[e("div",W1,[(d(!0),p(ue,null,fe(g.value,(B,Z)=>(d(),p("div",{key:B.id,class:K(["flex items-center",{"flex-1":Z<g.value.length-1}])},[e("div",K1,[e("div",{class:K(["w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",I(B,Z)])},[B.completed?(d(),p("svg",Y1,m[22]||(m[22]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):(d(),p("span",X1,A(Z+1),1))],2),e("div",Q1,[e("p",{class:K(["text-sm font-medium",B.active||B.completed?"text-gray-900":"text-gray-500"])},A(B.title),3),e("p",{class:K(["text-xs",B.active||B.completed?"text-gray-600":"text-gray-400"])},A(B.description),3)])]),Z<g.value.length-1?(d(),p("div",{key:0,class:K(["flex-1 h-0.5 mx-4 transition-all duration-300",g.value[Z+1].completed||g.value[Z+1].active?"bg-green-500":"bg-gray-200"])},null,2)):R("",!0)],2))),128))])])]),e("div",G1,[l.value==="customer"?(d(),p("div",J1,[m[25]||(m[25]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Customer Information"),e("p",{class:"text-sm text-gray-600"},"Please provide your contact details")])],-1)),e("form",{onSubmit:Ce(te,["prevent"]),class:"space-y-6"},[e("div",Z1,[z(k(ke),{modelValue:S.value.firstName,"onUpdate:modelValue":m[0]||(m[0]=B=>S.value.firstName=B),label:"First Name",placeholder:"Enter your first name",required:"",error:P.value.firstName},null,8,["modelValue","error"]),z(k(ke),{modelValue:S.value.lastName,"onUpdate:modelValue":m[1]||(m[1]=B=>S.value.lastName=B),label:"Last Name",placeholder:"Enter your last name",required:"",error:P.value.lastName},null,8,["modelValue","error"])]),z(k(ke),{modelValue:S.value.email,"onUpdate:modelValue":m[2]||(m[2]=B=>S.value.email=B),type:"email",label:"Email Address",placeholder:"Enter your email address",required:"",error:P.value.email},null,8,["modelValue","error"]),z(k(ke),{modelValue:S.value.phone,"onUpdate:modelValue":m[3]||(m[3]=B=>S.value.phone=B),type:"tel",label:"Phone Number",placeholder:"Enter your phone number",required:"",error:P.value.phone},null,8,["modelValue","error"]),e("div",eg,[z(k(ae),{type:"submit",loading:n.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:J(()=>m[24]||(m[24]=[Q(" Continue to Billing ")])),_:1,__:[24]},8,["loading"])])],32)])):R("",!0),l.value==="billing"?(d(),p("div",tg,[m[28]||(m[28]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Billing Address"),e("p",{class:"text-sm text-gray-600"},"Where should we send your invoice?")])],-1)),e("form",{onSubmit:Ce(X,["prevent"]),class:"space-y-6"},[z(k(ke),{modelValue:j.value.address,"onUpdate:modelValue":m[4]||(m[4]=B=>j.value.address=B),label:"Street Address",placeholder:"Enter your street address",required:"",error:w.value.address},null,8,["modelValue","error"]),e("div",sg,[z(k(ke),{modelValue:j.value.city,"onUpdate:modelValue":m[5]||(m[5]=B=>j.value.city=B),label:"City",placeholder:"Enter your city",required:"",error:w.value.city},null,8,["modelValue","error"]),z(k(ke),{modelValue:j.value.postalCode,"onUpdate:modelValue":m[6]||(m[6]=B=>j.value.postalCode=B),label:"Postal Code",placeholder:"Enter postal code",required:"",error:w.value.postalCode},null,8,["modelValue","error"])]),e("div",og,[z(k(ke),{modelValue:j.value.state,"onUpdate:modelValue":m[7]||(m[7]=B=>j.value.state=B),label:"State/Province",placeholder:"Enter state or province",required:"",error:w.value.state},null,8,["modelValue","error"]),z(k(ke),{modelValue:j.value.country,"onUpdate:modelValue":m[8]||(m[8]=B=>j.value.country=B),label:"Country",placeholder:"Enter country",required:"",error:w.value.country},null,8,["modelValue","error"])]),e("div",rg,[z(k(ae),{type:"button",variant:"outline",onClick:E,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:J(()=>m[26]||(m[26]=[Q(" Back to Customer Info ")])),_:1,__:[26]}),z(k(ae),{type:"submit",loading:a.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:J(()=>m[27]||(m[27]=[Q(" Continue to Payment ")])),_:1,__:[27]},8,["loading"])])],32)])):R("",!0),l.value==="payment"?(d(),p("div",ng,[m[51]||(m[51]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Payment Method"),e("p",{class:"text-sm text-gray-600"},"Choose how you'd like to pay")])],-1)),e("form",{onSubmit:Ce(oe,["prevent"]),class:"space-y-6"},[e("div",ag,[e("div",lg,[(d(!0),p(ue,null,fe(T.value,B=>(d(),p("div",{key:B.id,onClick:Z=>B.enabled&&(_.value.method=B.id),class:K(["border-2 rounded-lg p-4 transition-all duration-200",B.enabled?"cursor-pointer":"cursor-not-allowed opacity-60",_.value.method===B.id&&B.enabled?"border-blue-500 bg-blue-50":B.enabled?"border-gray-200 hover:border-gray-300":"border-gray-200 bg-gray-50"])},[e("div",dg,[e("div",{class:K(["w-4 h-4 rounded-full border-2 transition-all duration-200",_.value.method===B.id?"border-blue-500 bg-blue-500":"border-gray-300"])},[_.value.method===B.id?(d(),p("div",ug)):R("",!0)],2),e("div",cg,[B.id==="dpopay"?(d(),p("svg",mg,m[29]||(m[29]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)]))):B.id==="cash"?(d(),p("svg",fg,m[30]||(m[30]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"},null,-1)]))):B.id==="bank"?(d(),p("svg",pg,m[31]||(m[31]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},null,-1)]))):B.id==="card"?(d(),p("svg",gg,m[32]||(m[32]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))):R("",!0),e("span",{class:K(["font-medium",B.enabled?"text-gray-900":"text-gray-500"])},A(B.name),3),B.enabled?R("",!0):(d(),p("span",hg," Coming Soon "))])]),e("p",{class:K(["text-sm mt-2 ml-7",B.enabled?"text-gray-600":"text-gray-500"])},A(B.description),3)],10,ig))),128))])]),_.value.method==="dpopay"?(d(),p("div",vg,[m[40]||(m[40]=e("h4",{class:"font-medium text-gray-900"},"DPO Payment Details",-1)),e("div",yg,[m[36]||(m[36]=e("div",{class:"flex items-center space-x-2 mb-3"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})]),e("span",{class:"text-sm font-medium text-blue-900"},"Secure Payment via DPO")],-1)),m[37]||(m[37]=e("p",{class:"text-sm text-gray-600 mb-4"}," You will be redirected to DPO's secure payment gateway where you can pay using: ",-1)),m[38]||(m[38]=e("div",{class:"grid grid-cols-2 gap-3 text-xs mb-4"},[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-green-500 rounded-full"}),e("span",null,"Mobile Money (Airtel, TNM)")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-blue-500 rounded-full"}),e("span",null,"Credit/Debit Cards")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"}),e("span",null,"Bank Transfer")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-orange-500 rounded-full"}),e("span",null,"Digital Wallets")])],-1)),e("div",bg,[e("div",xg,[m[33]||(m[33]=e("span",{class:"text-sm text-gray-600"},"Total Amount:",-1)),e("span",{class:K(["text-lg font-bold",k(s).cartTotal>999999.99?"text-red-600":"text-gray-900"])},A(k(s).formatCurrency(k(s).cartTotal)),3)]),m[35]||(m[35]=e("div",{class:"flex justify-between items-center mt-1"},[e("span",{class:"text-xs text-gray-500"},"Currency:"),e("span",{class:"text-xs font-medium text-gray-700"},"MWK (Malawi Kwacha)")],-1)),k(s).cartTotal>999999.99?(d(),p("div",wg,m[34]||(m[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-red-800"},"Amount exceeds DPO Pay limit (MK 999,999.99)")],-1)]))):R("",!0)]),m[39]||(m[39]=e("div",{class:"mt-3 p-2 bg-green-50 rounded border border-green-200"},[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-green-800"},"SSL encrypted and PCI compliant")])],-1))])])):_.value.method==="card"?(d(),p("div",_g,[m[44]||(m[44]=e("h4",{class:"font-medium text-gray-900"},"Card Details",-1)),e("div",kg,[z(k(ke),{modelValue:_.value.cardNumber,"onUpdate:modelValue":m[9]||(m[9]=B=>_.value.cardNumber=B),label:"Card Number",placeholder:"1234 5678 9012 3456",required:"",error:M.value.cardNumber,success:v.value,onInput:xe,onKeydown:Pe,maxlength:pe()},null,8,["modelValue","error","success","maxlength"]),e("div",Cg,[z(k(ke),{modelValue:_.value.expiryDate,"onUpdate:modelValue":m[10]||(m[10]=B=>_.value.expiryDate=B),label:"Expiry Date",placeholder:"MM/YY",required:"",error:M.value.expiryDate,onInput:_e,onKeydown:F,maxlength:"5"},null,8,["modelValue","error"]),z(k(ke),{modelValue:_.value.cvv,"onUpdate:modelValue":m[11]||(m[11]=B=>_.value.cvv=B),label:"CVV",placeholder:"123",required:"",error:M.value.cvv,onInput:$,onKeydown:C,maxlength:"4"},null,8,["modelValue","error"])]),z(k(ke),{modelValue:_.value.cardName,"onUpdate:modelValue":m[12]||(m[12]=B=>_.value.cardName=B),label:"Name on Card",placeholder:"Enter name as shown on card",required:"",error:M.value.cardName,onInput:q},null,8,["modelValue","error"])]),b.value||_.value.cardNumber?(d(),p("div",$g,[b.value?(d(),p("div",Sg,[(d(),p("svg",{class:K(["w-5 h-5",x.value?"text-green-500":"text-blue-500"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[41]||(m[41]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]),2)),e("span",{class:K(x.value?"text-green-600":"text-blue-600")},A(b.value)+" detected ",3),x.value?(d(),p("svg",Mg,m[42]||(m[42]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):R("",!0)])):R("",!0),_.value.cardNumber?(d(),p("div",Ag,[e("span",null,A(_.value.cardNumber.replace(/\s/g,"").length),1),m[43]||(m[43]=e("span",null,"/",-1)),e("span",null,A(re(b.value).maxLength)+" digits",1),e("div",jg,[e("div",{class:K(["h-1 rounded-full transition-all duration-300",x.value?"bg-green-500":M.value.cardNumber?"bg-red-500":"bg-blue-500"]),style:Ge({width:`${Math.min(_.value.cardNumber.replace(/\s/g,"").length/re(b.value).maxLength*100,100)}%`})},null,6)])])):R("",!0)])):R("",!0)])):R("",!0),_.value.method==="dpopay"?(d(),p("div",Eg,[m[47]||(m[47]=e("div",{class:"flex items-center space-x-3 mb-4"},[e("div",{class:"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h4",{class:"font-medium text-gray-900"},"DPO Pay - Secure Payment Gateway"),e("p",{class:"text-sm text-gray-600"},"You'll be redirected to DPO Pay to complete your payment")])],-1)),e("div",Ig,[e("div",Tg,[z(k(ke),{modelValue:_.value.customerName,"onUpdate:modelValue":m[13]||(m[13]=B=>_.value.customerName=B),label:"Full Name",placeholder:"Enter your full name",required:"",error:M.value.customerName},null,8,["modelValue","error"]),e("div",null,[m[46]||(m[46]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Currency",-1)),ve(e("select",{"onUpdate:modelValue":m[14]||(m[14]=B=>_.value.currency=B),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},m[45]||(m[45]=[e("option",{value:"MWK"},"MWK - Malawi Kwacha",-1),e("option",{value:"USD"},"USD - US Dollar",-1),e("option",{value:"EUR"},"EUR - Euro",-1),e("option",{value:"GBP"},"GBP - British Pound",-1),e("option",{value:"ZAR"},"ZAR - South African Rand",-1)]),512),[[ko,_.value.currency]])])]),z(k(ke),{modelValue:_.value.customerEmail,"onUpdate:modelValue":m[15]||(m[15]=B=>_.value.customerEmail=B),label:"Email Address",type:"email",placeholder:"Enter your email address",required:"",error:M.value.customerEmail},null,8,["modelValue","error"]),z(k(ke),{modelValue:_.value.customerPhone,"onUpdate:modelValue":m[16]||(m[16]=B=>_.value.customerPhone=B),label:"Phone Number",placeholder:"+265 123 456 789",error:M.value.customerPhone},null,8,["modelValue","error"])]),m[48]||(m[48]=e("div",{class:"bg-white rounded-lg p-4 border border-gray-200"},[e("h5",{class:"font-medium text-gray-900 mb-3"},"Supported Payment Methods"),e("div",{class:"grid grid-cols-3 gap-4 text-center"},[e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("span",{class:"text-xs text-gray-600"},"Credit Cards")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])]),e("span",{class:"text-xs text-gray-600"},"Mobile Money")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])]),e("span",{class:"text-xs text-gray-600"},"Bank Transfer")])])],-1))])):R("",!0),e("div",Bg,[z(k(ae),{type:"button",variant:"outline",onClick:E,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:J(()=>m[49]||(m[49]=[Q(" Back to Billing ")])),_:1,__:[49]}),z(k(ae),{type:"submit",loading:u.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:J(()=>m[50]||(m[50]=[Q(" Review Order ")])),_:1,__:[50]},8,["loading"])])],32)])):R("",!0),l.value==="review"?(d(),p("div",Pg,[m[68]||(m[68]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Review Your Order"),e("p",{class:"text-sm text-gray-600"},"Please review all details before placing your order")])],-1)),e("div",Rg,[e("div",Ng,[m[55]||(m[55]=e("h4",{class:"font-medium text-gray-900 mb-4"},"Order Summary",-1)),e("div",Lg,[e("div",Dg,[e("span",zg,"Items ("+A(k(s).cartCount)+")",1),e("span",Og,A(k(s).formatCurrency(k(s).cartTotal)),1)]),m[54]||(m[54]=e("div",{class:"flex justify-between"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium"},"Free")],-1)),e("div",Vg,[m[52]||(m[52]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Fg,A(k(s).formatCurrency(0)),1)]),e("div",qg,[m[53]||(m[53]=e("span",{class:"text-lg font-bold text-gray-900"},"Total",-1)),e("span",Ug,A(k(s).formatCurrency(k(s).cartTotal)),1)])])]),e("div",Hg,[e("div",Wg,[m[59]||(m[59]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Customer Information",-1)),e("div",Kg,[e("p",null,[m[56]||(m[56]=e("span",{class:"text-gray-600"},"Name:",-1)),Q(" "+A(S.value.firstName)+" "+A(S.value.lastName),1)]),e("p",null,[m[57]||(m[57]=e("span",{class:"text-gray-600"},"Email:",-1)),Q(" "+A(S.value.email),1)]),e("p",null,[m[58]||(m[58]=e("span",{class:"text-gray-600"},"Phone:",-1)),Q(" "+A(S.value.phone),1)])])]),e("div",Yg,[m[60]||(m[60]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Billing Address",-1)),e("div",Xg,[e("p",null,A(j.value.address),1),e("p",null,A(j.value.city)+", "+A(j.value.state)+" "+A(j.value.postalCode),1),e("p",null,A(j.value.country),1)])])]),e("div",Qg,[m[61]||(m[61]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Payment Method",-1)),e("div",Gg,[e("p",null,A((H=W())==null?void 0:H.name),1),_.value.method==="dpopay"?(d(),p("p",Jg," Secure payment via DPO Gateway ")):_.value.method==="card"?(d(),p("p",Zg," **** **** **** "+A(_.value.cardNumber.slice(-4)),1)):_.value.method==="cash"?(d(),p("p",eh," Payment on delivery ")):_.value.method==="bank"?(d(),p("p",th," Direct bank transfer ")):R("",!0)])]),h.value?(d(),p("div",sh,[e("div",oh,[m[63]||(m[63]=e("div",{class:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"},null,-1)),e("div",rh,[m[62]||(m[62]=e("h4",{class:"text-sm font-medium text-blue-800 mb-1"},"Processing Your Order",-1)),e("p",nh,A(i.value||"Please wait while we process your order..."),1)])])])):c.value?(d(),p("div",ah,[e("div",lh,[m[65]||(m[65]=e("svg",{class:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",ih,[m[64]||(m[64]=e("h4",{class:"text-sm font-medium text-red-800 mb-1"},"Order Processing Error",-1)),e("p",dh,A(c.value),1),e("button",{onClick:Y,class:"mt-2 text-xs text-red-600 hover:text-red-800 underline"}," Dismiss ")])])])):R("",!0),e("div",uh,[ve(e("input",{id:"terms","onUpdate:modelValue":m[17]||(m[17]=B=>f.value=B),type:"checkbox",class:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[nt,f.value]]),m[66]||(m[66]=e("label",{for:"terms",class:"text-sm text-gray-700"},[Q(" I agree to the "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Terms and Conditions"),Q(" and "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Privacy Policy")],-1))]),e("div",ch,[z(k(ae),{type:"button",variant:"outline",onClick:E,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:J(()=>m[67]||(m[67]=[Q(" Back to Payment ")])),_:1,__:[67]}),z(k(ae),{onClick:V,loading:h.value,disabled:!f.value||h.value,class:K(["font-bold px-8 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200",c.value?"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white":"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"])},{default:J(()=>[h.value?(d(),p("span",mh,"Processing Order...")):c.value?(d(),p("span",fh,"Try Again")):(d(),p("span",ph,"Place Order"))]),_:1},8,["loading","disabled","class"])])])])):R("",!0)])]),e("div",gh,[e("div",hh,[e("div",vh,[e("div",yh,[e("div",bh,[m[70]||(m[70]=e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",null,[m[69]||(m[69]=e("h3",{class:"text-lg font-bold"},"Order Summary",-1)),e("p",xh,A(k(s).cartCount)+" "+A(k(s).cartCount===1?"item":"items")+" in cart",1)])])]),e("div",wh,[e("div",_h,[(d(!0),p(ue,null,fe(k(s).items,B=>(d(),p("div",{key:B.id,class:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"},[m[71]||(m[71]=e("div",{class:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",kh,[e("p",Ch,A(B.name),1),e("p",$h,"Qty: "+A(B.quantity),1)]),e("div",Sh,A(k(s).formatCurrency(k(s).getItemSubtotal(B))),1)]))),128))]),e("div",Mh,[e("div",Ah,[m[72]||(m[72]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",jh,A(k(s).formatCurrency(k(s).cartTotal)),1)]),m[75]||(m[75]=e("div",{class:"flex justify-between text-sm"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium text-green-600"},"Free")],-1)),e("div",Eh,[m[73]||(m[73]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Ih,A(k(s).formatCurrency(0)),1)]),e("div",Th,[m[74]||(m[74]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",Bh,A(k(s).formatCurrency(k(s).cartTotal)),1)])])])]),m[76]||(m[76]=e("div",{class:"bg-white rounded-xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),Q(" Secure Checkout ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" 256-bit SSL encryption ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" Secure payment processing ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" Money-back guarantee ")])])],-1))])])]))]}),_:1})]))}});const as=Ae(Ph,[["__scopeId","data-v-a7a2dac4"]]),Rh={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},Nh={class:"relative bg-gradient-to-r from-green-800 via-green-700 to-emerald-600 overflow-hidden border-b border-green-600/30"},Lh={key:0,class:"flex justify-center py-20"},Dh={class:"text-center"},zh={key:1,class:"max-w-4xl mx-auto"},Oh={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8"},Vh={class:"px-6 py-8 text-center"},Fh={class:"inline-flex items-center space-x-2 bg-gray-50 rounded-lg px-4 py-2"},qh={class:"text-sm font-bold text-gray-900 font-mono"},Uh={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},Hh={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},Wh={class:"p-6 space-y-4"},Kh={class:"flex justify-between items-center py-2 border-b border-gray-100"},Yh={class:"text-sm font-mono text-gray-900"},Xh={class:"flex justify-between items-center py-2 border-b border-gray-100"},Qh={class:"text-lg font-bold text-gray-900"},Gh={class:"flex justify-between items-center py-2 border-b border-gray-100"},Jh={class:"text-sm text-gray-900"},Zh={key:0,class:"flex justify-between items-center py-2"},ev={class:"text-sm font-mono text-gray-900"},tv={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},sv={class:"p-6"},ov={class:"flex flex-col sm:flex-row gap-4 justify-center"},rv=ge({__name:"PaymentSuccess",setup(t){const s=Ue(),o=Le(),r=L(!0),n=L(""),a=L(""),u=L(0);L("MWK");const h=L(new Date),c=L(""),i=g=>!g||g<=0?"MK 0":`MK ${g.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,l=g=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(g),f=()=>{window.location.href="/"},b=()=>{window.print()},v=async()=>{var g;try{(await fetch("/api/cart/clear",{method:"POST",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((g=document.querySelector('meta[name="csrf-token"]'))==null?void 0:g.getAttribute("content"))||""}})).ok&&console.log("Backend cart cleared successfully"),s.reset(),localStorage.removeItem("vertigo_cart"),localStorage.removeItem("cart_items"),localStorage.removeItem("vertigo_order_ref"),localStorage.removeItem("vertigo_transaction_token"),window.dispatchEvent(new CustomEvent("cart-cleared",{detail:{reason:"payment_success"}})),console.log("Cart cleared successfully after payment")}catch(S){console.error("Error clearing cart after payment:",S)}},x=()=>{try{const g=new URLSearchParams(window.location.search);n.value=g.get("CompanyRef")||localStorage.getItem("vertigo_order_ref")||"N/A",a.value=g.get("TransID")||"N/A",c.value=g.get("CCDapproval")||"";const S=localStorage.getItem("vertigo_payment_amount");u.value=S?parseFloat(S):0,h.value=new Date}catch(g){console.error("Error loading payment data:",g)}};return Ie(async()=>{x(),await v(),o.success("Payment completed successfully!"),setTimeout(()=>{r.value=!1},1e3)}),(g,S)=>(d(),p("div",Rh,[e("div",Nh,[S[1]||(S[1]=Ye('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-828e1a7e></div><div class="absolute inset-0" data-v-828e1a7e><div class="absolute top-2 left-4 w-12 h-12 bg-green-400/10 rounded-full blur-md" data-v-828e1a7e></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-emerald-400/15 rounded-full blur-lg" data-v-828e1a7e></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-828e1a7e></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-828e1a7e></div></div>',2)),z(k(Ee),{class:"relative z-10"},{default:J(()=>S[0]||(S[0]=[e("div",{class:"py-6"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Payment Successful "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-green-200"},[e("li",null,[e("a",{href:"/",class:"hover:text-green-100 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-green-100 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/checkout",class:"hover:text-green-100 transition-colors duration-200"},"Checkout")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Success")])])])]),e("div",{class:"hidden md:flex items-center space-x-3"},[e("div",{class:"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20"},[e("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e("span",{class:"text-sm font-medium text-green-100"},"Payment Confirmed")])])])],-1)])),_:1,__:[0]})]),z(k(Ee),{class:"py-12"},{default:J(()=>[r.value?(d(),p("div",Lh,[e("div",Dh,[z(k(tt),{size:"lg"}),S[2]||(S[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading payment details...",-1))])])):(d(),p("div",zh,[e("div",Oh,[e("div",Vh,[S[4]||(S[4]=e("div",{class:"flex justify-center mb-6"},[e("div",{class:"w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg success-checkmark"},[e("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),S[5]||(S[5]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-3"},"Payment Successful!",-1)),S[6]||(S[6]=e("p",{class:"text-lg text-gray-600 mb-6"}," Your payment has been processed successfully and your order is confirmed. ",-1)),e("div",Fh,[S[3]||(S[3]=e("span",{class:"text-sm font-medium text-gray-600"},"Order Reference:",-1)),e("span",qh,A(n.value),1)])])]),e("div",Uh,[e("div",Hh,[S[12]||(S[12]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),Q(" Payment Information ")])],-1)),e("div",Wh,[e("div",Kh,[S[7]||(S[7]=e("span",{class:"text-sm font-medium text-gray-600"},"Transaction ID:",-1)),e("span",Yh,A(a.value),1)]),e("div",Xh,[S[8]||(S[8]=e("span",{class:"text-sm font-medium text-gray-600"},"Amount:",-1)),e("span",Qh,A(i(u.value)),1)]),S[11]||(S[11]=e("div",{class:"flex justify-between items-center py-2 border-b border-gray-100"},[e("span",{class:"text-sm font-medium text-gray-600"},"Payment Method:"),e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"}," DPO Pay ")],-1)),e("div",Gh,[S[9]||(S[9]=e("span",{class:"text-sm font-medium text-gray-600"},"Payment Date:",-1)),e("span",Jh,A(l(h.value)),1)]),c.value?(d(),p("div",Zh,[S[10]||(S[10]=e("span",{class:"text-sm font-medium text-gray-600"},"Approval Code:",-1)),e("span",ev,A(c.value),1)])):R("",!0)])]),S[13]||(S[13]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},[e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),Q(" Order Status ")])]),e("div",{class:"p-6"},[e("div",{class:"text-center"},[e("div",{class:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),Q(" Order Confirmed ")]),e("p",{class:"text-gray-600 mb-6"}," Your order has been confirmed and is being processed. You will receive an email confirmation shortly. "),e("div",{class:"bg-blue-50 rounded-lg p-4"},[e("h4",{class:"text-sm font-semibold text-blue-900 mb-2"},"What's Next?"),e("ul",{class:"text-sm text-blue-800 space-y-1"},[e("li",null,"• Email confirmation sent"),e("li",null,"• Items marked as sold"),e("li",null,"• Cart automatically cleared")])])])])],-1))]),e("div",tv,[e("div",sv,[e("div",ov,[z(k(ae),{variant:"primary",size:"lg",onClick:f,class:"flex items-center justify-center"},{default:J(()=>S[14]||(S[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Continue Shopping ")])),_:1,__:[14]}),z(k(ae),{variant:"outline",size:"lg",onClick:b,class:"flex items-center justify-center"},{default:J(()=>S[15]||(S[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),Q(" Print Receipt ")])),_:1,__:[15]})])])])]))]),_:1})]))}});const ls=Ae(rv,[["__scopeId","data-v-828e1a7e"]]),nv={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},av={key:0,class:"flex justify-center items-center min-h-screen"},lv={class:"text-center"},iv={key:1,class:"flex justify-center items-center min-h-screen"},dv={class:"text-center"},uv={class:"text-gray-600 mb-4"},cv={key:2,class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},mv={class:"py-6"},fv={class:"flex items-center justify-between"},pv={class:"flex items-center space-x-4"},gv={class:"mt-1"},hv={class:"flex items-center space-x-2 text-sm text-slate-300"},vv={class:"text-gray-100 font-medium truncate max-w-xs"},yv={class:"flex items-center space-x-3"},bv={key:0,class:"max-w-7xl mx-auto"},xv={class:"grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8"},wv={class:"xl:col-span-2 space-y-6"},_v={class:"relative bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden group"},kv={class:"aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center relative"},Cv=["src","alt"],$v={key:1,class:"text-gray-400 text-center"},Sv={class:"absolute top-4 left-4 right-4 flex justify-between items-start"},Mv={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},Av={class:"text-xs font-bold"},jv={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},Ev={class:"text-xs font-medium text-gray-600"},Iv={key:0,class:"grid grid-cols-5 gap-3"},Tv=["onClick"],Bv=["src","alt"],Pv={class:"xl:col-span-1"},Rv={class:"sticky top-8 space-y-6"},Nv={class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},Lv={class:"p-8"},Dv={class:"text-3xl font-bold text-gray-900 mb-6 leading-tight"},zv={class:"mb-8"},Ov={class:"text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100"},Vv={class:"text-sm font-medium text-gray-600 mb-2"},Fv={class:"text-4xl font-bold text-gray-900 mb-2"},qv={key:0,class:"text-sm text-gray-500"},Uv={class:"space-y-4 mb-8"},Hv={key:0,class:"flex items-center justify-between py-3 border-b border-gray-100"},Wv={class:"font-mono text-gray-900 font-semibold"},Kv={key:1,class:"flex items-center justify-between py-3 border-b border-gray-100"},Yv={class:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold"},Xv={key:2,class:"flex items-center justify-between py-3"},Qv={class:"text-sm font-bold"},Gv={class:"space-y-4"},Jv={key:0},Zv={key:0,class:"bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-4"},e2={class:"flex items-start space-x-3"},t2={class:"text-sm text-yellow-700 mt-1"},s2={class:"mt-3"},o2={class:"relative"},r2={key:0,class:"mt-2 text-sm text-red-600 bg-red-50 p-3 rounded-xl"},n2={key:1,class:"mt-2 text-sm text-green-600 bg-green-50 p-3 rounded-xl"},a2={class:"mt-2 text-xs text-gray-500 bg-gray-50 p-3 rounded-xl"},l2={key:1},i2={class:"space-y-4"},d2={key:1,class:"space-y-3"},u2={class:"grid grid-cols-2 gap-3"},c2={class:"pt-6 border-t border-gray-200"},m2={key:0,class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},f2={class:"p-8"},p2={class:"prose prose-lg max-w-none"},g2={class:"text-gray-700 leading-relaxed text-lg"},h2={class:"max-w-6xl max-h-full p-6 relative"},v2={class:"absolute top-2 left-6 right-6 flex items-center justify-between z-10"},y2={class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},b2={class:"text-white font-medium text-sm"},x2={class:"bg-white rounded-xl shadow-2xl overflow-hidden"},w2=["src","alt"],is=ge({__name:"ItemDetail",setup(t){const s=Ue(),o=_t(),r=Le(),{isAuthenticated:n,user:a,initialize:u}=br(),h=Oo(),c=et(),i=L(null),l=L(!0),f=L(null),b=L(0),v=L(!1),x=L(null),g=L(!1),S=L(!1),P=L(!1);L(!1);const j=L(""),w=L(!1),_=L(null),M=L(!1),T=N(()=>{var C,q,y;if(!((C=i.value)!=null&&C.media)||i.value.media.length===0)return((q=i.value)==null?void 0:q.image)||((y=i.value)==null?void 0:y.cropped)||null;const $=i.value.media[b.value];return($==null?void 0:$.original_url)||($==null?void 0:$.url)||null}),I=N(()=>i.value?s.isInCart(i.value.id):!1);N(()=>i.value?o.isInWatchlist(i.value.id):!1);const U=N(()=>{var $,C;return i.value?(($=i.value.auction_type)==null?void 0:$.type)!=="live"?!0:((C=_.value)==null?void 0:C.subscribed)===!0:!1}),te=N(()=>{var $,C,q;return!i.value||(($=i.value.auction_type)==null?void 0:$.type)!=="live"?null:M.value?"Checking subscription status...":((C=_.value)==null?void 0:C.subscribed)===!1?`You need to subscribe to "${_.value.auction_type||((q=i.value.auction_type)==null?void 0:q.name)||"this live auction"}" to place bids on this live auction item.`:null}),X=async()=>{var $,C,q,y;try{l.value=!0,f.value=null;const m=h.params.id;if(!m)throw new Error("Invalid item ID");const H=await fetch(`/ajax-item/${m}`);if(!H.ok)throw H.status===404?new Error("Item not found"):H.status>=500?new Error("Server error. Please try again later."):new Error("Failed to load item");const B=await H.json();if(!B||!B.id)throw new Error("Invalid item data received");if(i.value=B,(($=B.auction_type)==null?void 0:$.type)!=="cash"){const Z=B.bid_amount||0,le=B.target_amount||0,ne=Math.max(Z,le)+1;x.value=ne.toString();const ie=Y(ne.toString());setTimeout(()=>{const $e=document.getElementById("bidAmount");$e&&($e.value=ie)},100)}((C=B.auction_type)==null?void 0:C.type)==="live"&&n.value&&B.auction_type_id&&(console.log("Item loaded, checking subscription for live auction:",{auction_type_id:B.auction_type_id,auction_type_name:(q=B.auction_type)==null?void 0:q.name,auction_type_type:(y=B.auction_type)==null?void 0:y.type}),await oe(B.auction_type_id))}catch(m){console.error("Error loading item:",m),f.value=m instanceof Error?m.message:"Failed to load item"}finally{l.value=!1}},oe=async $=>{var C;if(!n.value){_.value={subscribed:!1,message:"User not authenticated"};return}try{M.value=!0,console.log("Checking subscription for auction type ID:",$);const q=await fetch(`/api/check-subscription/${$}`,{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content"))||""},credentials:"include"});if(q.ok){const y=await q.json();console.log("Subscription check response:",y),_.value=y}else console.error("Failed to check subscription status:",q.status),_.value={subscribed:!1,message:"Failed to check subscription"}}catch(q){console.error("Error checking subscription status:",q),_.value={subscribed:!1,message:"Error checking subscription"}}finally{M.value=!1}},G=$=>{b.value=$},V=()=>{v.value=!0},se=()=>{v.value=!1},de=$=>new Intl.NumberFormat("en-MW",{style:"currency",currency:"MWK",minimumFractionDigits:0,maximumFractionDigits:0}).format($),O=$=>{var C;switch((C=$.auction_type)==null?void 0:C.type){case"cash":return $.target_amount||0;case"online":case"live":return $.bid_amount||$.target_amount||0;default:return $.target_amount||0}},E=$=>{if(!$.date_to)return null;const C=new Date($.date_to),q=new Date;if(C<q)return"Ended";const y=C.getTime()-q.getTime();return Math.floor(y/(1e3*60*60))<24?"Ending Soon":"Active"},W=$=>{const C=E($),q="px-2 py-1 rounded-full text-xs font-medium";switch(C){case"Ended":return`${q} bg-red-100 text-red-800`;case"Ending Soon":return`${q} bg-yellow-100 text-yellow-800`;case"Active":return`${q} bg-green-100 text-green-800`;default:return`${q} bg-gray-100 text-gray-800`}},Y=$=>$.replace(/[^0-9]/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,","),ee=$=>{const C=$.target;if(C.value&&!C.value.includes(",")){const q=Y(C.value);C.value=q}},re=$=>{var ne,ie;const C=$.target,y=C.value.replace(/[^0-9]/g,""),m=Y(y);if(C.value=m,x.value=y,!y){j.value="",w.value=!1;return}const H=parseInt(y),B=((ne=i.value)==null?void 0:ne.bid_amount)||0,Z=((ie=i.value)==null?void 0:ie.target_amount)||0,le=Math.max(B,Z)+1;if(isNaN(H)||H<=0){j.value="Please enter a valid amount",w.value=!1;return}if(H<le){j.value=`Minimum bid is ${de(le)}`,w.value=!1;return}j.value="",w.value=!0},pe=async()=>{var $;if(!(!i.value||!x.value)){if(!n.value){r.error("You must be signed in to place a bid","Authentication Required");return}try{g.value=!0;const C=typeof x.value=="string"?x.value.replace(/,/g,""):x.value,q=parseInt(C.toString())||0;if(isNaN(q)||q<=0){r.error("Please enter a valid bid amount","Invalid Bid");return}console.log("Placing bid:",{item_id:i.value.id,amount:q});const y=await fetch("/ajax-bid",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":(($=document.querySelector('meta[name="csrf-token"]'))==null?void 0:$.getAttribute("content"))||""},body:JSON.stringify({item_id:i.value.id,amount:q})});if(console.log("Response status:",y.status),!y.ok){const H=await y.text();throw console.error("Bid error response:",H),new Error(`Failed to place bid: ${y.status} ${H}`)}const m=await y.json();console.log("Bid response data:",m),r.success(`Your bid of ${de(q)} has been placed successfully!`,"Bid Placed"),await X()}catch(C){r.error(C instanceof Error?C.message:"Failed to place bid","Bid Failed")}finally{g.value=!1}}},xe=async()=>{if(i.value)try{S.value=!0,await s.addToCart(i.value)&&r.success(`${i.value.name} has been added to your cart.`,"Added to Cart")}catch($){r.error($ instanceof Error?$.message:"Failed to add item to cart","Cart Error")}finally{S.value=!1}},Te=async()=>{if(i.value)try{P.value=!0,await s.removeFromCart(i.value)&&r.info(`${i.value.name} has been removed from your cart.`,"Removed from Cart")}catch($){r.error($ instanceof Error?$.message:"Failed to remove item from cart","Cart Error")}finally{P.value=!1}},Pe=()=>{c.push("/checkout")},_e=()=>{c.back()},F=async $=>{var C,q;r.success("You are now signed in and can place bids!","Welcome!"),((q=(C=i.value)==null?void 0:C.auction_type)==null?void 0:q.type)==="live"&&i.value.auction_type_id&&await oe(i.value.auction_type_id),X()};return Ie(async()=>{await u();const $=window.cartItems||[];$.length>0?s.initializeCart($):await s.fetchCart(),X()}),($,C)=>{var y,m;const q=Cs("router-link");return d(),p("div",nv,[l.value?(d(),p("div",av,[e("div",lv,[z(k(tt),{size:"lg"}),C[2]||(C[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading item details...",-1))])])):f.value?(d(),p("div",iv,[e("div",dv,[C[4]||(C[4]=e("div",{class:"text-red-500 mb-4"},[e("svg",{class:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),C[5]||(C[5]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Item Not Found",-1)),e("p",uv,A(f.value),1),z(k(ae),{onClick:_e,variant:"outline"},{default:J(()=>C[3]||(C[3]=[Q(" Go Back ")])),_:1,__:[3]})])])):i.value?(d(),p("div",cv,[C[13]||(C[13]=Ye('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div><div class="absolute inset-0"><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;"></div></div>',2)),z(k(Ee),{class:"relative z-10"},{default:J(()=>[e("div",mv,[e("div",fv,[e("div",pv,[C[11]||(C[11]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[C[10]||(C[10]=e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Item Details ",-1)),e("nav",gv,[e("ol",hv,[e("li",null,[z(q,{to:"/",class:"hover:text-blue-200 transition-colors duration-200"},{default:J(()=>C[6]||(C[6]=[Q("Home")])),_:1,__:[6]})]),C[8]||(C[8]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",null,[z(q,{to:"/auctions",class:"hover:text-blue-200 transition-colors duration-200"},{default:J(()=>C[7]||(C[7]=[Q("Auctions")])),_:1,__:[7]})]),C[9]||(C[9]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",vv,A(i.value.name),1)])])])]),e("div",yv,[z(k(ae),{variant:"outline",size:"sm",onClick:_e,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:J(()=>C[12]||(C[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Back ")])),_:1,__:[12]})])])])]),_:1})])):R("",!0),z(k(Ee),{class:"py-8"},{default:J(()=>{var H,B,Z,le;return[i.value?(d(),p("div",bv,[e("div",xv,[e("div",wv,[e("div",_v,[e("div",kv,[T.value?(d(),p("img",{key:0,src:T.value,alt:i.value.name,class:"w-full h-full object-cover cursor-pointer group-hover:scale-105 transition-transform duration-500",onClick:V},null,8,Cv)):(d(),p("div",$v,C[14]||(C[14]=[e("svg",{class:"w-20 h-20 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg font-medium"},"No image available",-1)]))),e("div",Sv,[e("div",Mv,[E(i.value)?(d(),p("div",{key:0,class:K(W(i.value))},[e("span",Av,A(E(i.value)),1)],2)):R("",!0)]),e("div",jv,[e("span",Ev,A(((H=i.value.auction_type)==null?void 0:H.name)||"Auction Item"),1)])]),C[15]||(C[15]=e("div",{class:"absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})])],-1))])]),i.value.media&&i.value.media.length>1?(d(),p("div",Iv,[(d(!0),p(ue,null,fe(i.value.media,(ne,ie)=>(d(),p("div",{key:ie,class:K(["aspect-square bg-gray-100 rounded-xl overflow-hidden cursor-pointer border-2 transition-all duration-300 hover:shadow-lg",b.value===ie?"border-blue-500 ring-2 ring-blue-200 shadow-lg":"border-gray-200 hover:border-gray-300"]),onClick:$e=>G(ie)},[e("img",{src:ne.url||ne.original_url,alt:`${i.value.name} - Image ${ie+1}`,class:"w-full h-full object-cover hover:scale-110 transition-transform duration-300"},null,8,Bv)],10,Tv))),128))])):R("",!0)]),e("div",Pv,[e("div",Rv,[e("div",Nv,[e("div",Lv,[e("h1",Dv,A(i.value.name),1),e("div",zv,[e("div",Ov,[e("p",Vv,A(((B=i.value.auction_type)==null?void 0:B.type)==="cash"?"Price":"Current Bid"),1),e("div",Fv,A(de(O(i.value))),1),((Z=i.value.auction_type)==null?void 0:Z.type)!=="cash"?(d(),p("p",qv," Starting: "+A(de(i.value.target_amount||0)),1)):R("",!0)])]),e("div",Uv,[i.value.code||i.value.reference_number?(d(),p("div",Hv,[C[16]||(C[16]=e("span",{class:"text-gray-600 font-medium"},"Reference",-1)),e("span",Wv,A(i.value.code||i.value.reference_number),1)])):R("",!0),i.value.auction_type?(d(),p("div",Kv,[C[17]||(C[17]=e("span",{class:"text-gray-600 font-medium"},"Category",-1)),e("span",Yv,A(i.value.auction_type.name),1)])):R("",!0),E(i.value)?(d(),p("div",Xv,[C[18]||(C[18]=e("span",{class:"text-gray-600 font-medium"},"Status",-1)),e("div",{class:K(W(i.value))},[e("span",Qv,A(E(i.value)),1)],2)])):R("",!0)]),e("div",Gv,[((le=i.value.auction_type)==null?void 0:le.type)!=="cash"?(d(),p("div",Jv,[z(k(Ps),{title:"Sign in to place a bid",message:"You need to be signed in to participate in bidding on this item.","fallback-style":"card",onAuthSuccess:F},{default:J(()=>{var ne;return[((ne=i.value.auction_type)==null?void 0:ne.type)==="live"&&!U.value?(d(),p("div",Zv,[e("div",e2,[C[21]||(C[21]=e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",null,[C[20]||(C[20]=e("h4",{class:"text-sm font-medium text-yellow-800"},"Subscription Required",-1)),e("p",t2,A(te.value),1),e("div",s2,[z(q,{to:"/register-bid",class:"inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},{default:J(()=>C[19]||(C[19]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Q(" Subscribe to Live Auction ")])),_:1,__:[19]})])])])])):R("",!0),U.value?(d(),p("form",{key:1,onSubmit:Ce(pe,["prevent"]),class:"space-y-4"},[e("div",null,[C[24]||(C[24]=e("label",{for:"bidAmount",class:"block text-sm font-bold text-gray-700 mb-3"}," Place Your Bid ",-1)),e("div",o2,[C[22]||(C[22]=e("span",{class:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-bold"},"MWK",-1)),ve(e("input",{id:"bidAmount","onUpdate:modelValue":C[0]||(C[0]=ie=>x.value=ie),type:"text",inputmode:"numeric",pattern:"[0-9]*",class:"w-full pl-16 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg font-semibold transition-all duration-200",placeholder:"Enter your bid",required:"",onInput:re,onFocus:ee},null,544),[[Re,x.value]])]),j.value?(d(),p("div",r2,A(j.value),1)):x.value&&w.value?(d(),p("div",n2," ✓ Valid bid amount: "+A(de(parseInt(x.value.toString().replace(/,/g,"")))),1)):R("",!0),e("p",a2,[C[23]||(C[23]=e("svg",{class:"w-3 h-3 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),Q(" Minimum bid: "+A(de(Math.max(i.value.bid_amount||0,i.value.target_amount||0)+1)),1)])]),z(k(ae),{type:"submit",loading:g.value,disabled:!x.value||!w.value,class:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:J(()=>C[25]||(C[25]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})],-1),Q(" Place Bid ")])),_:1,__:[25]},8,["loading","disabled"])],32)):R("",!0)]}),_:1})])):(d(),p("div",l2,[e("div",i2,[I.value?(d(),p("div",d2,[C[29]||(C[29]=e("div",{class:"p-4 bg-green-50 border-2 border-green-200 rounded-2xl"},[e("div",{class:"flex items-center justify-center text-green-800"},[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"font-bold"},"Item is in your cart")])],-1)),e("div",u2,[z(k(ae),{onClick:Te,loading:P.value,variant:"outline",class:"border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-bold py-3 rounded-xl",size:"lg"},{default:J(()=>C[27]||(C[27]=[Q(" Remove ")])),_:1,__:[27]},8,["loading"]),z(k(ae),{onClick:Pe,class:"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-bold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",size:"lg"},{default:J(()=>C[28]||(C[28]=[Q(" Checkout ")])),_:1,__:[28]})])])):(d(),ce(k(ae),{key:0,onClick:xe,loading:S.value,class:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:J(()=>C[26]||(C[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"})],-1),Q(" Add to Cart ")])),_:1,__:[26]},8,["loading"]))])]))]),e("div",c2,[z(k(ae),{onClick:_e,variant:"outline",class:"w-full border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-bold py-3 rounded-xl",size:"lg"},{default:J(()=>C[30]||(C[30]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),Q(" Back to Listings ")])),_:1,__:[30]})])])])])])]),i.value.description?(d(),p("div",m2,[e("div",f2,[C[31]||(C[31]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900"},"Description"),e("p",{class:"text-gray-600"},"Detailed information about this item")])],-1)),e("div",p2,[e("p",g2,A(i.value.description),1)])])])):R("",!0)])):R("",!0)]}),_:1}),v.value?(d(),p("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 backdrop-blur-sm",onClick:se},[e("div",h2,[e("div",v2,[e("div",y2,[e("h3",b2,A((y=i.value)==null?void 0:y.name),1)]),e("button",{onClick:se,class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-70 transition-all duration-200"},C[32]||(C[32]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",x2,[T.value?(d(),p("img",{key:0,src:T.value,alt:(m=i.value)==null?void 0:m.name,class:"max-w-full max-h-[80vh] object-contain mx-auto block",onClick:C[1]||(C[1]=Ce(()=>{},["stop"]))},null,8,w2)):R("",!0)]),C[33]||(C[33]=e("div",{class:"absolute bottom-2 left-1/2 transform -translate-x-1/2"},[e("div",{class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},[e("p",{class:"text-white text-xs"},"Click outside to close")])],-1))])])):R("",!0)])}}}),_2={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},k2={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},C2={class:"grid grid-cols-1 lg:grid-cols-5 gap-8"},$2={class:"lg:col-span-2 lg:order-1"},S2={class:"space-y-6"},M2={class:"lg:col-span-3 lg:order-2"},A2={class:"p-6"},j2={key:0,class:"mt-1 text-sm text-red-600"},E2={key:1,class:"mt-1 text-sm text-gray-500"},I2={class:"bg-gray-50 rounded-lg p-4"},T2={class:"flex items-start"},B2={class:"flex items-center h-5"},P2=["disabled"],R2={class:"flex justify-end pt-4"},N2=ge({__name:"RegisterBid",setup(t){et();const s=Le(),o=be(),r=L(!1),n=L(!1),a=L(""),u=L(""),h=L([]),c=L({referenceNumber:"",selectedAuction:null,agreedToTerms:!1}),i=L({referenceNumber:"",auctionTypeId:"",agreedToTerms:""}),l=N(()=>h.value.map(P=>({key:P.id.toString(),label:P.name,description:P.description||`${P.type} auction`,value:P.id}))),f=N(()=>c.value.referenceNumber.trim()!==""&&c.value.selectedAuction!==null&&c.value.agreedToTerms&&!r.value),b=()=>{i.value={referenceNumber:"",auctionTypeId:"",agreedToTerms:""},a.value=""},v=()=>{b();let P=!0;return c.value.referenceNumber.trim()||(i.value.referenceNumber="Bank deposit reference number is required",P=!1),c.value.selectedAuction||(i.value.auctionTypeId="Please select an auction to register for",P=!1),c.value.agreedToTerms||(i.value.agreedToTerms="required",P=!1),P},x=async()=>{var P,j;try{n.value=!0;const w={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},_=(P=document.querySelector('meta[name="csrf-token"]'))==null?void 0:P.getAttribute("content");_&&(w["X-CSRF-TOKEN"]=_);const M=await je.get("/api/live-auctions-session",{headers:w,withCredentials:!0});h.value=M.data.data||M.data}catch(w){console.error("Error fetching auction types:",w),((j=w.response)==null?void 0:j.status)===401?console.log("User not authenticated for auction types"):s.error("Failed to load available auctions")}finally{n.value=!1}},g=async()=>{var P,j,w;if(v())try{r.value=!0,b();const _=(P=document.querySelector('meta[name="csrf-token"]'))==null?void 0:P.getAttribute("content");if(!_){a.value="Security token not found. Please refresh the page.",r.value=!1;return}const M=new FormData;M.append("_token",_),M.append("reference_number",c.value.referenceNumber.trim()),M.append("auction_type_id",((j=c.value.selectedAuction)==null?void 0:j.value.toString())||"");const T=await fetch("/register-bid",{method:"POST",body:M,credentials:"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}}),I=await T.json();if(T.ok&&I.success)u.value=I.message||`You have successfully registered to the ${((w=c.value.selectedAuction)==null?void 0:w.label)||"auction"}`,s.success(u.value),setTimeout(()=>{window.location.href="/shop"},1500);else{let U=I.error||I.message||"Registration failed. Please try again.";a.value=U,s.error(U)}}catch(_){console.error("Registration error:",_),a.value="Registration failed. Please check your connection and try again.",s.error(a.value)}finally{r.value=!1}},S=async P=>{console.log("Auth success in RegisterBid:",P),window.user=P,o.setUser(P),await x(),a.value=""};return Me(()=>o.isAuthenticated||!!window.user,async P=>{P&&h.value.length===0&&(console.log("User became authenticated, fetching auction types"),await x())},{immediate:!1}),Ie(async()=>{var j;if(!document.querySelector('meta[name="csrf-token"]')){const w=document.createElement("meta");w.name="csrf-token",w.content=((j=window.Laravel)==null?void 0:j.csrfToken)||"",document.head.appendChild(w)}const P=window.user;P?(console.log("User authenticated via server data:",P),o.setUser(P),await x()):console.log("User not authenticated - AuthGuard will handle login")}),(P,j)=>(d(),p("div",_2,[z(k(Ps),{title:"Sign in to register for auctions",message:"You need to be signed in to register for auction participation.","require-auth":!0,onAuthSuccess:S},{default:J(()=>[j[11]||(j[11]=e("div",{class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},[e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"}),e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"})]),e("div",{class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[e("div",{class:"text-center"},[e("h1",{class:"text-3xl font-bold text-white mb-2"},"Register for Auction"),e("p",{class:"text-slate-200 text-lg"},"Join live auctions and start bidding on amazing items")])])],-1)),e("div",k2,[e("div",C2,[e("div",$2,[e("div",S2,[z(k(pt),{class:"checkout-section order-summary sticky top-8"},{default:J(()=>j[5]||(j[5]=[e("div",{class:"p-8"},[e("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Registration Process"),e("div",{class:"space-y-6"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"1")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Make Bank Deposit"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Visit your bank or use online banking to make a deposit. Keep your deposit receipt as you'll need the reference number for registration. "),e("div",{class:"mt-2 text-xs text-blue-600 font-medium"}," 💡 Tip: Save a photo of your receipt ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"2")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Select Your Auction"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Choose from available live auctions. Each auction has different items and schedules. Review the auction details before registering. "),e("div",{class:"mt-2 text-xs text-green-600 font-medium"}," 📅 Check auction dates and times ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"3")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Complete Registration"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Fill out the registration form with your deposit reference number and agree to the terms and conditions. "),e("div",{class:"mt-2 text-xs text-purple-600 font-medium"}," ✅ Review terms carefully ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"4")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Start Bidding"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Once registered, you can participate in the live auction. Place bids on items you're interested in and track your bidding activity. "),e("div",{class:"mt-2 text-xs text-orange-600 font-medium"}," 🎯 Good luck with your bids! ")])])])],-1)])),_:1,__:[5]}),z(k(pt),{class:"checkout-section"},{default:J(()=>j[6]||(j[6]=[e("div",{class:"p-6"},[e("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"Important Information"),e("div",{class:"space-y-4"},[e("div",{class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-blue-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-blue-900"},"Registration Requirements"),e("p",{class:"text-sm text-blue-700 mt-1"}," A valid bank deposit is required to participate in auctions. This ensures serious bidders and helps maintain auction integrity. ")])])]),e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-yellow-900"},"Auction Rules"),e("p",{class:"text-sm text-yellow-700 mt-1"}," Please familiarize yourself with our auction terms and bidding rules before participating. All sales are final. ")])])]),e("div",{class:"bg-green-50 border border-green-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-green-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-green-900"},"Support Available"),e("p",{class:"text-sm text-green-700 mt-1"}," Need help? Our support team is available during auction hours to assist with registration and bidding questions. ")])])])])],-1)])),_:1,__:[6]}),z(k(pt),{class:"checkout-section security-badge"},{default:J(()=>j[7]||(j[7]=[e("div",{class:"p-6"},[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-green-800"},"Secure & Protected"),e("p",{class:"text-sm text-green-700 mt-1"}," Your registration information is encrypted and securely stored. We use industry-standard security measures to protect your data. ")])])],-1)])),_:1,__:[7]})])]),e("div",M2,[z(k(pt),{class:"checkout-section form-section"},{default:J(()=>[e("div",A2,[j[10]||(j[10]=e("div",{class:"mb-6"},[e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Auction Registration"),e("p",{class:"text-gray-600"},"Complete your registration to participate in live auctions")],-1)),a.value?(d(),ce(k(fs),{key:0,variant:"error",title:a.value,class:"mb-6",onClose:j[0]||(j[0]=w=>a.value="")},null,8,["title"])):R("",!0),u.value?(d(),ce(k(fs),{key:1,variant:"success",title:u.value,class:"mb-6",onClose:j[1]||(j[1]=w=>u.value="")},null,8,["title"])):R("",!0),e("form",{onSubmit:Ce(g,["prevent"]),class:"space-y-6"},[e("div",null,[z(k(ke),{modelValue:c.value.referenceNumber,"onUpdate:modelValue":j[2]||(j[2]=w=>c.value.referenceNumber=w),label:"Bank Deposit Reference Number",placeholder:"Enter your bank deposit reference number",required:"",error:i.value.referenceNumber,disabled:r.value,"help-text":"Enter the reference number from your bank deposit receipt"},null,8,["modelValue","error","disabled"])]),e("div",null,[j[8]||(j[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[Q(" Select Auction "),e("span",{class:"text-red-500"},"*")],-1)),z(k(qo),{modelValue:c.value.selectedAuction,"onUpdate:modelValue":j[3]||(j[3]=w=>c.value.selectedAuction=w),items:l.value,placeholder:"Choose an auction to register for",disabled:r.value||n.value,error:i.value.auctionTypeId,searchable:""},null,8,["modelValue","items","disabled","error"]),i.value.auctionTypeId?(d(),p("p",j2,A(i.value.auctionTypeId),1)):R("",!0),n.value?(d(),p("p",E2," Loading available auctions... ")):R("",!0)]),e("div",I2,[e("div",T2,[e("div",B2,[ve(e("input",{id:"terms","onUpdate:modelValue":j[4]||(j[4]=w=>c.value.agreedToTerms=w),type:"checkbox",class:K(["h-4 w-4 rounded transition-colors duration-200",i.value.agreedToTerms?"border-red-500 text-red-600 focus:ring-red-500 bg-red-50":"text-primary-600 focus:ring-primary-500 border-gray-300"]),disabled:r.value},null,10,P2),[[nt,c.value.agreedToTerms]])]),j[9]||(j[9]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"terms",class:"font-medium text-gray-700"}," I agree to the terms and conditions "),e("p",{class:"text-gray-500"},[Q(" By registering, you agree to our auction terms, bidding rules, and payment policies. "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Read full terms")])],-1))])]),e("div",R2,[z(k(ae),{type:"submit",variant:"primary",size:"lg",loading:r.value,disabled:!f.value,class:"btn-enhanced"},{default:J(()=>[r.value?(d(),p(ue,{key:0},[Q(" Registering... ")],64)):(d(),p(ue,{key:1},[Q(" Register for Auction ")],64))]),_:1},8,["loading","disabled"])])],32)])]),_:1})])])])]),_:1,__:[11]})]))}});const bo=Ae(N2,[["__scopeId","data-v-e8f87a63"]]),L2=Ze("bidDashboard",()=>{const t=L([]),s=L([]),o=L([]),r=L({total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0}),n=L(!1),a=L(null),u=L(0),h=N(()=>t.value.length>0),c=N(()=>s.value.length>0),i=N(()=>o.value.length>0),l=async()=>{var M,T,I,U;const _=be();if(!_.isAuthenticated)return g(),!1;n.value=!0,a.value=null;try{const te={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},X=(M=document.querySelector('meta[name="csrf-token"]'))==null?void 0:M.getAttribute("content");X&&(te["X-CSRF-TOKEN"]=X),_.token&&(te.Authorization=`Bearer ${_.token}`);const oe=await fetch("/api/bid-dashboard",{headers:te,credentials:"include"});if(!oe.ok)throw new Error("Failed to fetch dashboard data");const G=await oe.json();return t.value=Array.isArray(G.active_bids)?G.active_bids:((T=G.active_bids)==null?void 0:T.data)||[],s.value=Array.isArray(G.bid_history)?G.bid_history:((I=G.bid_history)==null?void 0:I.data)||[],o.value=Array.isArray(G.watchlist)?G.watchlist:((U=G.watchlist)==null?void 0:U.data)||[],r.value=G.stats||r.value,u.value=Date.now(),!0}catch(te){return a.value=te instanceof Error?te.message:"Failed to fetch dashboard data",console.error("Dashboard fetch error:",te),!1}finally{n.value=!1}},f=async(_=1,M=15,T="")=>{const I=be();if(!I.isAuthenticated)return{data:[],meta:{}};try{const U=new URLSearchParams({page:_.toString(),per_page:M.toString(),...T&&{search:T}}),te=await fetch(`/api/bid-dashboard/active-bids?${U}`,{headers:{Accept:"application/json",Authorization:`Bearer ${I.token}`}});if(!te.ok)throw new Error("Failed to fetch active bids");const X=await te.json();return _===1?t.value=X.data||[]:t.value.push(...X.data||[]),X}catch(U){return a.value=U instanceof Error?U.message:"Failed to fetch active bids",console.error("Active bids fetch error:",U),{data:[],meta:{}}}},b=async(_=1,M=15,T="",I="")=>{var te;const U=be();if(!U.isAuthenticated)return{data:[],meta:{}};try{const oe=`/api/bid-dashboard/bid-history?${new URLSearchParams({page:_.toString(),per_page:M.toString(),...T&&{search:T},...I&&{status:I}})}`,G={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},V=(te=document.querySelector('meta[name="csrf-token"]'))==null?void 0:te.getAttribute("content");V&&(G["X-CSRF-TOKEN"]=V),U.token&&(G.Authorization=`Bearer ${U.token}`);const se=await fetch(oe,{headers:G,credentials:"include"});if(!se.ok)throw new Error(`Failed to fetch bid history: ${se.status} ${se.statusText}`);const de=await se.json();return _===1?s.value=de.data||[]:s.value.push(...de.data||[]),de}catch(X){return a.value=X instanceof Error?X.message:"Failed to fetch bid history",console.error("Bid history fetch error:",X),{data:[],meta:{}}}},v=async()=>{const _=be();if(!_.isAuthenticated)return!1;try{const M=await fetch("/api/bid-dashboard/stats",{headers:{Accept:"application/json",Authorization:`Bearer ${_.token}`}});if(!M.ok)throw new Error("Failed to fetch stats");const T=await M.json();return r.value=T,!0}catch(M){return a.value=M instanceof Error?M.message:"Failed to fetch stats",console.error("Stats fetch error:",M),!1}},x=async()=>await l(),g=()=>{t.value=[],s.value=[],o.value=[],r.value={total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0},a.value=null,u.value=0};return{activeBids:t,bidHistory:s,watchlistItems:o,stats:r,isLoading:n,error:a,lastSyncTime:u,hasActiveBids:h,hasBidHistory:c,hasWatchlistItems:i,fetchDashboardData:l,fetchActiveBids:f,fetchBidHistory:b,fetchStats:v,refreshDashboard:x,clearDashboard:g,getActiveBidById:_=>t.value.find(M=>M.id===_),getBidHistoryById:_=>s.value.find(M=>M.id===_),isActiveBid:_=>t.value.some(M=>M.id===_),initializeDashboard:async()=>{be().isAuthenticated&&await l()}}}),D2={class:"bg-white -mx-4 -my-5 sm:-m-6"},z2={class:"px-4 py-5 sm:px-6 border-b border-gray-200 bg-gray-50"},O2={class:"flex items-center justify-between"},V2={class:"flex items-center space-x-3"},F2={class:"text-sm text-gray-600 mt-0.5 max-w-md truncate"},q2={class:"p-6"},U2={key:0,class:"flex flex-col items-center justify-center py-16"},H2={key:1,class:"text-center py-16"},W2={class:"text-gray-600 mb-6"},K2={key:2},Y2={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"},X2={class:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},Q2={class:"flex items-center"},G2={class:"ml-3"},J2={class:"text-lg font-bold text-blue-700"},Z2={class:"bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200"},ey={class:"flex items-center"},ty={class:"ml-3"},sy={class:"text-lg font-bold text-green-700"},oy={class:"relative"},ry={class:"space-y-6"},ny={class:"relative flex-shrink-0"},ay={key:0,class:"absolute -top-1 -left-1 w-5 h-5 bg-green-100 rounded-full animate-pulse"},ly={class:"flex-1 min-w-0"},iy={class:"flex items-center justify-between"},dy={class:"flex items-center space-x-3"},uy={class:"w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"},cy={class:"text-sm font-medium text-gray-600"},my={class:"flex items-center space-x-2"},fy={class:"text-sm font-semibold text-gray-900"},py={class:"text-xs text-gray-500 mt-0.5"},gy={class:"text-right"},hy={key:0,class:"flex items-center justify-end mt-1"},vy={class:"text-xs font-medium text-green-600"},yy={key:3,class:"text-center py-16"},by=ge({__name:"BidTimelineModal",props:{show:{type:Boolean},item:{}},emits:["close"],setup(t,{emit:s}){const o=t,r=L([]),n=L(!1),a=L(null),u=N(()=>new Set(r.value.map(l=>l.user_id)).size);N(()=>r.value.length>0?Math.max(...r.value.map(i=>i.bid_amount||0)):0),N(()=>r.value.length===0?0:r.value.reduce((l,f)=>l+(f.bid_amount||0),0)/r.value.length);const h=i=>{const l=new Date(i);return(new Date().getTime()-l.getTime())/(1e3*60)<=5},c=async()=>{var i,l;if((i=o.item)!=null&&i.id){n.value=!0,a.value=null;try{const f={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},b=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");b&&(f["X-CSRF-TOKEN"]=b);const v=localStorage.getItem("auth_token");v&&(f.Authorization=`Bearer ${v}`);const x=await fetch(`/api/open-item/${o.item.id}/bids?limit=10`,{headers:f,credentials:"include"});if(!x.ok)throw new Error("Failed to fetch bid history");const g=await x.json();r.value=g.data||[]}catch(f){a.value=f instanceof Error?f.message:"Failed to load bid history",console.error("Error fetching bids:",f)}finally{n.value=!1}}};return Me(()=>o.show,i=>{i&&o.item&&c()}),(i,l)=>(d(),ce(k(Co),{show:i.show,onClose:l[1]||(l[1]=f=>i.$emit("close")),"max-width":"2xl",closeable:!0},{default:J(()=>{var f;return[e("div",D2,[e("div",z2,[e("div",O2,[e("div",V2,[l[3]||(l[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",null,[l[2]||(l[2]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Bid History",-1)),e("p",F2,A((f=i.item)==null?void 0:f.name),1)])]),e("button",{onClick:l[0]||(l[0]=b=>i.$emit("close")),class:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"},l[4]||(l[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",q2,[n.value?(d(),p("div",U2,l[5]||(l[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"},null,-1),e("p",{class:"text-sm text-gray-500"},"Loading bid history...",-1)]))):a.value?(d(),p("div",H2,[l[7]||(l[7]=e("div",{class:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),l[8]||(l[8]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"Failed to load bid history",-1)),e("p",W2,A(a.value),1),e("button",{onClick:c,class:"inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"},l[6]||(l[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),Q(" Try again ")]))])):r.value.length>0?(d(),p("div",K2,[e("div",Y2,[e("div",X2,[e("div",Q2,[l[10]||(l[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})])])],-1)),e("div",G2,[l[9]||(l[9]=e("p",{class:"text-sm font-medium text-blue-900"},"Total Bids",-1)),e("p",J2,A(r.value.length),1)])])]),e("div",Z2,[e("div",ey,[l[12]||(l[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),e("div",ty,[l[11]||(l[11]=e("p",{class:"text-sm font-medium text-green-900"},"Bidders",-1)),e("p",sy,A(u.value),1)])])])]),e("div",oy,[l[16]||(l[16]=e("div",{class:"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"},null,-1)),e("div",ry,[(d(!0),p(ue,null,fe(r.value,(b,v)=>{var x,g;return d(),p("div",{key:b.id,class:"relative flex items-start space-x-4 group"},[e("div",ny,[e("div",{class:K(["w-3 h-3 rounded-full border-2 border-white shadow-sm transition-all duration-200 group-hover:scale-110",v===0?"bg-green-500":"bg-gray-400"])},null,2),v===0?(d(),p("div",ay)):R("",!0)]),e("div",ly,[e("div",{class:K(["bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-gray-300",v===0?"ring-2 ring-green-100 border-green-200":""])},[e("div",iy,[e("div",dy,[e("div",uy,[e("span",cy,A((((x=b.user)==null?void 0:x.name)||"Anonymous").charAt(0).toUpperCase()),1)]),e("div",null,[e("div",my,[e("span",fy,A(((g=b.user)==null?void 0:g.name)||"Anonymous Bidder"),1),v===0?(d(),ce(k(it),{key:0,variant:"success",size:"sm",class:"animate-pulse"},{default:J(()=>l[13]||(l[13]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3l14 9-14 9V3z"})],-1),Q(" Leading ")])),_:1,__:[13]})):R("",!0),h(b.created_at)?(d(),ce(k(it),{key:1,variant:"info",size:"sm"},{default:J(()=>l[14]||(l[14]=[Q(" New ")])),_:1,__:[14]})):R("",!0)]),e("p",py,A(k(ps)(b.created_at)),1)])]),e("div",gy,[e("div",{class:K(["text-lg font-bold transition-colors duration-200",v===0?"text-green-600":"text-gray-900"])},A(k(ro)(b.bid_amount)),3),v<r.value.length-1?(d(),p("div",hy,[l[15]||(l[15]=e("svg",{class:"w-3 h-3 text-green-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 11l5-5m0 0l5 5m-5-5v12"})],-1)),e("span",vy," +"+A(k(ro)(b.bid_amount-r.value[v+1].bid_amount)),1)])):R("",!0)])])],2)])])}),128))])])])):(d(),p("div",yy,l[17]||(l[17]=[e("div",{class:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1),e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"No bids yet",-1),e("p",{class:"text-gray-500"},"Be the first to place a bid on this item!",-1)])))])])]}),_:1},8,["show"]))}}),xy={key:0,class:"bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg hover:border-gray-300 transition-all duration-300 group"},wy={class:"relative"},_y={class:"flex items-center justify-between"},ky={class:"flex items-center space-x-3"},Cy={class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},$y={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"},Sy={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},My={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},Ay={class:"text-right"},jy={class:"text-xs text-white/80 font-medium uppercase tracking-wide"},Ey={class:"text-sm font-semibold text-white"},Iy={class:"p-6"},Ty={class:"flex items-start space-x-4"},By={class:"flex-shrink-0 relative"},Py={class:"relative overflow-hidden rounded-xl shadow-sm"},Ry=["src","alt"],Ny={key:0,class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse"},Ly={class:"flex-1 min-w-0"},Dy={class:"flex items-start justify-between mb-3"},zy={class:"flex-1"},Oy={class:"text-lg font-bold text-gray-900 truncate group-hover:text-gray-700 transition-colors duration-200"},Vy={class:"flex items-center mt-2 space-x-2"},Fy={class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-gray-100 text-gray-700 rounded-full"},qy={key:0,class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-blue-50 text-blue-700 rounded-full"},Uy={class:"grid grid-cols-2 gap-4 mb-4"},Hy={class:"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},Wy={class:"text-xl font-bold text-blue-900"},Ky={class:"text-xs text-blue-600 mt-1"},Yy={class:"flex items-center justify-between mb-2"},Xy={key:0,class:"mb-4"},Qy={class:"flex items-center justify-between mb-2"},Gy={class:"w-full bg-gray-200 rounded-full h-2"},Jy={class:"mb-4"},Zy={key:0,class:"flex items-center p-3 bg-green-50 border border-green-200 rounded-lg"},eb={key:1,class:"flex items-center p-3 bg-amber-50 border border-amber-200 rounded-lg"},tb={key:2,class:"flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg"},sb={class:"text-xs text-gray-600"},ob={class:"flex items-center justify-between"},rb={class:"flex items-center space-x-4"},nb=["disabled"],ab=["disabled"],lb={key:1,class:"bg-gray-50 border border-gray-200 rounded-xl p-5 opacity-60"},ib=ge({__name:"BidCard",props:{bid:{},variant:{}},emits:["view-details","increase-bid"],setup(t,{emit:s}){const o=t,r=s,n=L(!1),a=L(!1),u=N(()=>o.bid&&o.bid.item&&o.bid.item.id),h=N(()=>{var O;return((O=o.bid.item)==null?void 0:O.name)||"Unknown Item"}),c=N(()=>{var O;return((O=o.bid.auctionType)==null?void 0:O.name)||"Unknown Type"}),i=N(()=>{var O,E;return n.value?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+":((O=o.bid.item)==null?void 0:O.image)||((E=o.bid.item)==null?void 0:E.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}),l=N(()=>parseFloat(o.bid.bid_amount||"0")),f=N(()=>{var O;return parseFloat(((O=o.bid.item)==null?void 0:O.bid_amount)||"0")}),b=N(()=>{var O,E;return!o.bid.date_to&&!((O=o.bid.item)!=null&&O.date_to)?"No end date":Ko(o.bid.date_to||((E=o.bid.item)==null?void 0:E.date_to))}),v=N(()=>ps(o.bid.created_at)),x=N(()=>o.variant==="active"?P.value?"success":"warning":o.bid.closed_by===o.bid.user_id?"success":"secondary"),g=N(()=>o.variant==="active"?P.value?"Winning":"Outbid":o.bid.closed_by===o.bid.user_id?"Won":"Lost"),S=N(()=>{var W;if(o.variant!=="active"||!u.value)return!1;const O=new Date,E=o.bid.date_to?new Date(o.bid.date_to):(W=o.bid.item)!=null&&W.date_to?new Date(o.bid.item.date_to):null;return E&&E<=O?!1:l.value<f.value}),P=N(()=>o.variant!=="active"||!u.value?!1:l.value>=f.value&&l.value>0),j=N(()=>o.variant==="active"?P.value?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-amber-500 to-amber-600":o.bid.closed_by===o.bid.user_id?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-gray-500 to-gray-600"),w=N(()=>o.variant==="active"?P.value?"bg-green-600":"bg-amber-600":o.bid.closed_by===o.bid.user_id?"bg-green-600":"bg-gray-600"),_=N(()=>{var pe,xe;if(o.variant==="history")return ps(o.bid.date_to||((pe=o.bid.item)==null?void 0:pe.date_to)||o.bid.created_at);const O=o.bid.date_to||((xe=o.bid.item)==null?void 0:xe.date_to);if(!O)return"No end date";const E=new Date,Y=new Date(O).getTime()-E.getTime();if(Y<=0)return"Ended";const ee=Math.floor(Y/(1e3*60*60)),re=Math.floor(Y%(1e3*60*60)/(1e3*60));return ee>24?`${Math.floor(ee/24)}d ${ee%24}h`:ee>0?`${ee}h ${re}m`:`${re}m`}),M=N(()=>{var ee;if(o.variant!=="active")return!1;const O=o.bid.date_to||((ee=o.bid.item)==null?void 0:ee.date_to);if(!O)return!1;const E=new Date;return new Date(O).getTime()-E.getTime()<=1e3*60*60&&!P.value}),T=N(()=>P.value?"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200":"bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200"),I=N(()=>P.value?"text-gray-700":"text-red-700"),U=N(()=>P.value?"text-gray-900":"text-red-900"),te=N(()=>{const O=f.value-l.value;return O<=0?"You lead":`+${rt(O)} ahead`}),X=N(()=>{if(f.value===0)return 100;const O=l.value/f.value*100;return Math.min(Math.max(O,10),100)}),oe=N(()=>P.value?"bg-gradient-to-r from-green-400 to-green-500":"bg-gradient-to-r from-amber-400 to-amber-500"),G=()=>{n.value=!0},V=()=>{var O;(O=o.bid.item)!=null&&O.id&&r("view-details",o.bid)},se=()=>{r("increase-bid",o.bid)},de=()=>{var O;(O=o.bid.item)!=null&&O.id&&(a.value=!0)};return(O,E)=>{var W,Y,ee;return d(),p(ue,null,[u.value?(d(),p("div",xy,[e("div",wy,[e("div",{class:K([j.value,"px-6 py-4"])},[e("div",_y,[e("div",ky,[e("div",{class:K([w.value,"p-2 rounded-lg"])},[(d(),p("svg",Cy,[P.value&&O.variant==="active"?(d(),p("path",$y)):O.variant==="active"?(d(),p("path",Sy)):(d(),p("path",My))]))],2),e("div",null,[z(k(it),{variant:x.value,size:"sm",class:"text-white border-white/20"},{default:J(()=>[Q(A(g.value),1)]),_:1},8,["variant"])])]),e("div",Ay,[e("div",jy,A(O.variant==="active"?"Time Left":"Ended"),1),e("div",Ey,A(_.value),1)])])],2)]),e("div",Iy,[e("div",Ty,[e("div",By,[e("div",Py,[e("img",{src:i.value,alt:h.value,class:"w-24 h-24 object-cover group-hover:scale-105 transition-transform duration-300",onError:G},null,40,Ry),E[1]||(E[1]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1))]),M.value?(d(),p("div",Ny,E[2]||(E[2]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):R("",!0)]),e("div",Ly,[e("div",Dy,[e("div",zy,[e("h3",Oy,A(h.value),1),e("div",Vy,[e("span",Fy,A(c.value),1),(W=O.bid.item)!=null&&W.code?(d(),p("span",qy," #"+A(O.bid.item.code),1)):R("",!0)])])]),e("div",Uy,[e("div",Hy,[E[3]||(E[3]=e("div",{class:"flex items-center justify-between mb-2"},[e("span",{class:"text-xs font-bold text-blue-700 uppercase tracking-wide"},"Your Bid"),e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",Wy,A(k(rt)(l.value)),1),e("div",Ky,A(v.value),1)]),e("div",{class:K(T.value)},[e("div",Yy,[e("span",{class:K(["text-xs font-bold uppercase tracking-wide",I.value])},"Current Bid",2),(d(),p("svg",{class:K(["w-4 h-4",I.value]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},E[4]||(E[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"},null,-1)]),2))]),e("div",{class:K(["text-xl font-bold",U.value])},A(k(rt)(f.value)),3),e("div",{class:K(["text-xs mt-1",I.value])},A(te.value),3)],2)]),O.variant==="active"?(d(),p("div",Xy,[e("div",Qy,[E[5]||(E[5]=e("span",{class:"text-xs font-medium text-gray-600"},"Bid Position",-1)),e("span",{class:K(["text-xs font-semibold",P.value?"text-green-600":"text-amber-600"])},A(P.value?"Leading":"Behind"),3)]),e("div",Gy,[e("div",{class:K([oe.value,"h-2 rounded-full transition-all duration-500"]),style:Ge({width:X.value+"%"})},null,6)])])):R("",!0),e("div",Jy,[P.value&&O.variant==="active"?(d(),p("div",Zy,E[6]||(E[6]=[e("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-green-800"},"You're currently winning!"),e("div",{class:"text-xs text-green-600"},"Keep monitoring for new bids")],-1)]))):O.variant==="active"?(d(),p("div",eb,E[7]||(E[7]=[e("svg",{class:"w-5 h-5 text-amber-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-amber-800"},"You've been outbid"),e("div",{class:"text-xs text-amber-600"},"Consider placing a higher bid")],-1)]))):O.variant==="history"?(d(),p("div",tb,[E[9]||(E[9]=e("svg",{class:"w-5 h-5 text-gray-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[E[8]||(E[8]=e("div",{class:"text-sm font-semibold text-gray-800"},"Auction completed",-1)),e("div",sb,A(b.value),1)])])):R("",!0)]),e("div",ob,[e("div",rb,[e("button",{onClick:V,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((Y=O.bid.item)!=null&&Y.id)}," View Details ",8,nb),e("button",{onClick:de,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((ee=O.bid.item)!=null&&ee.id),title:"View bid history"}," History ",8,ab)]),O.variant==="active"&&S.value?(d(),ce(k(ae),{key:0,onClick:se,variant:"primary",size:"sm",class:"font-medium"},{default:J(()=>E[10]||(E[10]=[Q(" Increase Bid ")])),_:1,__:[10]})):R("",!0)])])])])])):(d(),p("div",lb,E[11]||(E[11]=[Ye('<div class="flex items-center justify-center text-gray-500"><svg class="w-8 h-8 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg><div><p class="text-sm font-medium">Invalid Bid Record</p><p class="text-xs">This bid is missing required information</p></div></div>',1)]))),z(by,{show:a.value,item:O.bid.item,onClose:E[0]||(E[0]=re=>a.value=!1)},null,8,["show","item"])],64)}}}),db={class:"py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50/50 transition-colors duration-200 group"},ub={class:"flex items-center space-x-3"},cb={class:"flex-shrink-0"},mb=["src","alt"],fb={class:"flex-1 min-w-0"},pb={class:"flex items-center justify-between"},gb={class:"flex-1 min-w-0"},hb={class:"text-sm font-medium text-gray-900 truncate"},vb={class:"flex items-center mt-1 space-x-3 text-xs text-gray-500"},yb={key:0},bb={key:1},xb={key:0,class:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},wb=ge({__name:"WatchlistItemCard",props:{item:{},variant:{default:"default"}},emits:["view-details","remove-from-watchlist","place-bid"],setup(t,{emit:s}){const o=t,r=s,n=N(()=>{if(!o.item.date_to)return"";const h=new Date,c=new Date(o.item.date_to);return c<=h?"Ended":eu(c)}),a=N(()=>{if(o.item.closed_by)return!1;if(o.item.date_to){const h=new Date;return new Date(o.item.date_to)>h}return!0}),u=()=>{r("place-bid",o.item)};return(h,c)=>{var i;return d(),p("div",db,[e("div",ub,[e("div",cb,[e("img",{src:h.item.image||"/images/placeholder.jpg",alt:h.item.name,class:"w-12 h-12 object-cover rounded"},null,8,mb)]),e("div",fb,[e("div",pb,[e("div",gb,[e("h3",hb,A(h.item.name),1),e("div",vb,[e("span",null,A(((i=h.item.auctionType)==null?void 0:i.name)||"Unknown Type"),1),c[2]||(c[2]=e("span",null,"•",-1)),e("span",null,A(k(rt)(h.item.bid_amount||0)),1),h.item.date_to?(d(),p("span",yb,"•")):R("",!0),h.item.date_to?(d(),p("span",bb,A(n.value),1)):R("",!0)])]),e("button",{onClick:c[0]||(c[0]=l=>h.$emit("remove-from-watchlist",h.item)),class:"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 opacity-0 group-hover:opacity-100",title:"Remove from watchlist"},c[3]||(c[3]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),h.variant!=="compact"?(d(),p("div",xb,[e("button",{onClick:c[1]||(c[1]=l=>h.$emit("view-details",h.item)),class:"text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200"}," View "),a.value?(d(),p("button",{key:0,onClick:u,class:"text-xs text-gray-900 hover:text-gray-700 font-medium transition-colors duration-200"}," Bid ")):R("",!0)])):R("",!0)])])])}}}),_b={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},kb={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},Cb={class:"py-6"},$b={class:"flex items-center justify-between"},Sb={class:"flex items-center space-x-3"},Mb={key:0,class:"flex justify-center py-16"},Ab={class:"text-center"},jb={key:1,class:"space-y-8"},Eb={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Ib={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Tb={class:"flex items-center justify-between"},Bb={class:"text-3xl font-bold text-gray-900 mt-2"},Pb={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Rb={class:"flex items-center justify-between"},Nb={class:"text-3xl font-bold text-gray-900 mt-2"},Lb={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Db={class:"flex items-center justify-between"},zb={class:"text-3xl font-bold text-gray-900 mt-2"},Ob={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Vb={class:"flex items-center justify-between"},Fb={class:"text-3xl font-bold text-gray-900 mt-2"},qb={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Ub={class:"lg:col-span-2"},Hb={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},Wb={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},Kb={class:"flex items-center justify-between"},Yb={class:"p-6"},Xb={key:0,class:"text-center py-12"},Qb={key:1},Gb={class:"w-full"},Jb={key:0,class:"text-center py-4 text-sm text-gray-500"},Zb={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},ex={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},tx={class:"flex items-center justify-between"},sx={key:0,class:"text-center py-12 px-6"},ox={key:1,class:"px-6"},rx={class:"bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden"},nx={class:"bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-4 border-b border-gray-200/50"},ax={class:"flex items-center justify-between"},lx={class:"flex items-center space-x-2"},ix={class:"relative"},dx={class:"p-0"},ux={key:0,class:"text-center py-12 px-6"},cx={key:1},mx={key:0,class:"px-6 py-4 border-t border-gray-200"},xo=ge({__name:"BidDashboard",setup(t){const s=be(),o=L2(),r=_t(),n=Le(),a=et(),u=N(()=>o.activeBids.length>0||o.bidHistory.length>0||r.items.length>0),h=N(()=>o.activeBids.filter(O=>O.item&&O.item.id)),c=N(()=>o.activeBids.length-h.value.length),i=L([]),l=L({current_page:1,last_page:1,per_page:10,total:0,from:0,to:0}),f=L(!1),b=L(""),v=N(()=>i.value.length>0?i.value:o.bidHistory.slice(0,10)),x=[{key:"item",label:"Item",sortable:!1,width:"300px",render:(O,E)=>{const W=X(E),Y=oe(E);return`
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <img
              src="${te(E)}"
              alt="${W}"
              class="w-12 h-12 object-cover rounded-lg"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+'"
            />
          </div>
          <div class="min-w-0 flex-1">
            <div class="text-sm font-medium text-gray-900 truncate">
              ${W}
            </div>
            <div class="text-sm text-gray-500 truncate">
              ${Y}
            </div>
          </div>
        </div>
      `}},{key:"bid_amount",label:"Bid Amount",sortable:!0,align:"right",render:(O,E)=>{const W=parseFloat(E.bid_amount||"0");return`
        <div class="text-right">
          <div class="text-sm font-semibold text-gray-900">
            ${rt(W)}
          </div>
        </div>
      `}},{key:"status",label:"Status",sortable:!1,align:"center",render:(O,E)=>{const W=G(E),Y=V(E);return`
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${W==="success"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}">
          ${Y}
        </span>
      `}},{key:"created_at",label:"Date Placed",sortable:!0,render:(O,E)=>`
        <div class="text-sm text-gray-900">
          ${Ko(E.created_at)}
        </div>
      `},{key:"actions",label:"",sortable:!1,align:"center",width:"60px",render:(O,E)=>{var re;const W=!((re=E.item)!=null&&re.id),Y=W?"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-400 bg-gray-100 cursor-not-allowed":"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-600 bg-gray-50 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer transition-colors duration-200",ee=`
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      `;return`
        <button
          class="${Y}"
          onclick="window.handleViewBidDetails && window.handleViewBidDetails(${JSON.stringify(E).replace(/"/g,"&quot;")})"
          ${W?"disabled":""}
          title="View Details"
        >
          ${ee}
        </button>
      `}}],g=async()=>{!o.activeBids.length&&!o.bidHistory.length&&await S()},S=async()=>{try{await r.initializeWatchlist();const[O,E]=await Promise.all([o.fetchDashboardData(),r.fetchWatchlist(),T(1)]);O||console.warn("Failed to load dashboard data"),E||console.warn("Failed to load watchlist data")}catch(O){console.error("Error loading dashboard data:",O),n.error("Failed to load dashboard data. Please try refreshing the page.")}},P=async()=>{await S(),n.success("Dashboard refreshed successfully")},j=O=>{var E;(E=O.item)!=null&&E.id&&a.push(`/item/${O.item.id}`)},w=O=>{var E;(E=O.item)!=null&&E.id&&a.push(`/item/${O.item.id}`)},_=O=>{a.push(`/item/${O.id}`)},M=async O=>{await r.removeFromWatchlist(O.id)?n.success(`${O.name} removed from watchlist`):n.error(r.error||"Failed to remove item from watchlist")},T=async(O=1)=>{f.value=!0;try{const E=await o.fetchBidHistory(O,l.value.per_page,b.value,"");i.value=E.data||[],l.value={current_page:E.current_page||1,last_page:E.last_page||1,per_page:E.per_page||10,total:E.total||0,from:E.from||0,to:E.to||0}}catch(E){console.error("Failed to load bid history:",E),i.value=[]}finally{f.value=!1}},I=O=>{T(O)},U=()=>{clearTimeout(se),se=setTimeout(()=>{T(1)},500)},te=O=>{var E,W;return((E=O.item)==null?void 0:E.image)||((W=O.item)==null?void 0:W.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"},X=O=>{var E;return((E=O.item)==null?void 0:E.name)||"Unknown Item"},oe=O=>{var E;return((E=O.auctionType)==null?void 0:E.name)||"Unknown Type"},G=O=>O.closed_by===O.user_id?"success":"secondary",V=O=>O.closed_by===O.user_id?"Won":"Lost";let se;const de=async()=>{const O=window.user;O&&!s.user&&s.setUser(O),!s.user&&!s.isLoading&&await s.initialize()};return Ie(async()=>{window.handleViewBidDetails=j,await de(),s.isAuthenticated&&await S()}),Me(()=>s.isAuthenticated,async(O,E)=>{O&&!E?await S():!O&&E&&o.clearDashboard()},{immediate:!1}),(O,E)=>{const W=Cs("router-link");return d(),p("div",_b,[z(Ps,{title:"Sign in to view your bid dashboard",message:"You need to be signed in to access your bidding activity and manage your watchlist.","fallback-style":"page",onAuthSuccess:g},{default:J(()=>[e("div",kb,[E[6]||(E[6]=e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"},null,-1)),E[7]||(E[7]=e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-white/5 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-white/8 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"}),e("div",{class:"absolute inset-0 opacity-5",style:{"background-image":"radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)","background-size":"20px 20px"}})],-1)),z(k(Ee),{class:"relative z-10"},{default:J(()=>[e("div",Cb,[e("div",$b,[E[5]||(E[5]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Bid Dashboard "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-white transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Bid Dashboard")])])])],-1)),e("div",Sb,[z(k(ae),{onClick:P,loading:k(o).isLoading,variant:"outline",size:"sm",class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:J(()=>E[4]||(E[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),Q(" Refresh ")])),_:1,__:[4]},8,["loading"])])])])]),_:1})]),z(k(Ee),{class:"py-8"},{default:J(()=>[k(o).isLoading&&!u.value?(d(),p("div",Mb,[e("div",Ab,[z(k(tt),{size:"lg"}),E[8]||(E[8]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading your dashboard...",-1))])])):(d(),p("div",jb,[e("div",Eb,[e("div",Ib,[e("div",Tb,[e("div",null,[E[9]||(E[9]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Active Bids",-1)),e("p",Bb,A(k(o).stats.active_bids),1),E[10]||(E[10]=e("p",{class:"text-gray-500 text-sm mt-1"},"Currently bidding",-1))]),E[11]||(E[11]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Pb,[e("div",Rb,[e("div",null,[E[12]||(E[12]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Won Auctions",-1)),e("p",Nb,A(k(o).stats.won_auctions),1),E[13]||(E[13]=e("p",{class:"text-gray-500 text-sm mt-1"},"Successful bids",-1))]),E[14]||(E[14]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1))])]),e("div",Lb,[e("div",Db,[e("div",null,[E[15]||(E[15]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Watchlist Items",-1)),e("p",zb,A(k(o).stats.watchlist_count),1),E[16]||(E[16]=e("p",{class:"text-gray-500 text-sm mt-1"},"Items watching",-1))]),E[17]||(E[17]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),e("div",Ob,[e("div",Vb,[e("div",null,[E[18]||(E[18]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Win Rate",-1)),e("p",Fb,A(k(o).stats.win_rate)+"%",1),E[19]||(E[19]=e("p",{class:"text-gray-500 text-sm mt-1"},"Success rate",-1))]),E[20]||(E[20]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",qb,[e("div",Ub,[e("div",Hb,[e("div",Wb,[e("div",Kb,[E[22]||(E[22]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Active Bids")],-1)),z(W,{to:"/bid-dashboard/active-bids",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:J(()=>E[21]||(E[21]=[Q(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[21]})])]),e("div",Yb,[k(o).activeBids.length===0?(d(),p("div",Xb,[E[24]||(E[24]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),E[25]||(E[25]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No active bids",-1)),E[26]||(E[26]=e("p",{class:"text-gray-500 mb-4"},"Start bidding on items to see them here.",-1)),z(k(ae),{onClick:E[0]||(E[0]=Y=>O.$router.push("/")),variant:"primary",size:"sm"},{default:J(()=>E[23]||(E[23]=[Q(" Browse Auctions ")])),_:1,__:[23]})])):(d(),p("div",Qb,[z(k(Zd),{items:h.value,"auto-play":!1,"show-counter":!0,"container-classes":"rounded-lg","navigation-button-classes":"hover:bg-gray-50","dot-classes":"hover:scale-105","key-extractor":Y=>Y.id},{default:J(({item:Y,isActive:ee})=>[e("div",Gb,[z(ib,{bid:Y,variant:"active",onViewDetails:j,onIncreaseBid:w},null,8,["bid"])])]),_:1},8,["items","key-extractor"]),c.value>0?(d(),p("div",Jb,A(c.value)+" incomplete bid record"+A(c.value>1?"s":"")+" hidden ",1)):R("",!0)]))])])]),e("div",null,[e("div",Zb,[e("div",ex,[e("div",tx,[E[28]||(E[28]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Watchlist")],-1)),z(W,{to:"/bid-dashboard/watchlist",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:J(()=>E[27]||(E[27]=[Q(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[27]})])]),e("div",null,[k(r).isEmpty?(d(),p("div",sx,[E[30]||(E[30]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1)),E[31]||(E[31]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No watched items",-1)),E[32]||(E[32]=e("p",{class:"text-gray-500 mb-4"},"Add items to your watchlist to track them.",-1)),z(k(ae),{onClick:E[1]||(E[1]=Y=>O.$router.push("/")),variant:"outline",size:"sm"},{default:J(()=>E[29]||(E[29]=[Q(" Browse Items ")])),_:1,__:[29]})])):(d(),p("div",ox,[(d(!0),p(ue,null,fe(k(r).items.slice(0,5),Y=>(d(),ce(wb,{key:Y.id,item:Y,variant:"compact",onViewDetails:_,onPlaceBid:_,onRemoveFromWatchlist:M},null,8,["item"]))),128))]))])])])]),e("div",rx,[e("div",nx,[e("div",ax,[E[34]||(E[34]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Recent Bid History")],-1)),e("div",lx,[e("div",ix,[z(k(ke),{modelValue:b.value,"onUpdate:modelValue":E[2]||(E[2]=Y=>b.value=Y),placeholder:"Search history...",size:"sm",class:"w-48",onInput:U},null,8,["modelValue"]),E[33]||(E[33]=e("svg",{class:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])])]),e("div",dx,[v.value.length===0&&!f.value?(d(),p("div",ux,[E[36]||(E[36]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1)),E[37]||(E[37]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No bid history",-1)),E[38]||(E[38]=e("p",{class:"text-gray-500 mb-4"},"Your completed bids will appear here.",-1)),z(k(ae),{onClick:E[3]||(E[3]=Y=>O.$router.push("/")),variant:"outline",size:"sm"},{default:J(()=>E[35]||(E[35]=[Q(" Start Bidding ")])),_:1,__:[35]})])):(d(),p("div",cx,[z(k(Ba),{columns:x,data:v.value,loading:f.value,bordered:!1,striped:!0,hover:!0,class:"min-h-[400px]"},null,8,["data","loading"]),l.value.last_page>1&&i.value.length>0?(d(),p("div",mx,[z(k(Uo),{"current-page":l.value.current_page,"total-pages":l.value.last_page,total:l.value.total,"per-page":l.value.per_page,"show-info":!0,"show-page-size":!1,onPageChange:I},null,8,["current-page","total-pages","total","per-page"])])):R("",!0)]))])])]))]),_:1})]),_:1})])}}}),fx=()=>{const t=window.location.pathname;return t.startsWith("/spa")?[{path:"/",name:"spa-homepage",component:rs,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"spa-item-detail",component:is,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"spa-cart",component:ns,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"spa-checkout",component:as,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"spa-payment-success",component:ls,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"spa-register-bid",component:bo,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"spa-bid-dashboard",component:xo,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"spa-not-found",component:rs,meta:{title:"Page Not Found - Vertigo AMS",requiresAuth:!1}}]:t==="/cart"?[{path:"/",name:"cart",component:ns,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}}]:t==="/checkout"?[{path:"/",name:"checkout",component:as,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}}]:t==="/payment-success"?[{path:"/",name:"payment-success",component:ls,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}}]:t.startsWith("/item/")?[{path:"/",name:"item-detail",component:is,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}}]:[{path:"/",name:"homepage",component:rs,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"item-detail",component:is,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"cart",component:ns,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"checkout",component:as,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"payment-success",component:ls,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"register-bid",component:bo,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"bid-dashboard",component:xo,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}}]},px=fx(),gx=()=>{const t=window.location.pathname;return t.startsWith("/spa")?"/spa":t.startsWith("/home-vue")?"/home-vue":t.startsWith("/cart")?"/cart":t.startsWith("/checkout")?"/checkout":t.startsWith("/payment-success")?"/payment-success":t.startsWith("/register-bid")?"/register-bid":(t.startsWith("/item/"),"/")},Cr=qn({history:yn(gx()),routes:px,scrollBehavior(t,s,o){return o||{top:0}}});Cr.beforeEach(async(t,s,o)=>{t.meta.title&&(document.title=t.meta.title);const r=be();if(!r.user)try{await r.initialize()}catch(n){console.error("Failed to initialize auth:",n)}if(t.meta.requiresAuth){const n=hx();if(console.log("Router guard: isAuthenticated =",n,"user =",r.user,"sessionAuth =",r.sessionAuth),!n&&!r.user)if(window.location.pathname.startsWith("/spa")){console.log("Allowing navigation to continue, will check auth in component"),o();return}else{window.location.href="/login?redirect="+encodeURIComponent(t.fullPath);return}}if(t.meta.requiresAdmin&&!vx()){console.warn("Admin access required for this route"),o("/");return}o()});function hx(){const t=be(),s=t.sessionAuth||t.user&&!t.token,o=t.token&&t.user;return s||o||t.isAuthenticated}function vx(){var o;const t=be();if(!t.isAuthenticated)return!1;const s=t.user;return((o=s==null?void 0:s.roles)==null?void 0:o.some(r=>r.name==="admin"))||!1}const yx={template:`
    <div class="min-h-screen bg-gray-50">
      <router-view />
      <NotificationContainer />

      <!-- Global Auth Modal -->
      <AuthModal
        v-model:show="showAuthModal"
        :start-with-register="authModalTab === 'register'"
        title="Welcome to Vertigo AMS"
        subtitle="Your premier auction management system"
        @success="handleAuthSuccess"
        @close="hideAuthModal"
      />
    </div>
  `,setup(){const t=Le(),s=be(),o=et(),r=L(!1),n=L("login"),a=c=>{var i;n.value=((i=c.detail)==null?void 0:i.tab)||"login",r.value=!0},u=()=>{r.value=!1},h=c=>{r.value=!1;const i=(c==null?void 0:c.name)||(c==null?void 0:c.email)||"there";t.success(`Welcome back, ${i}! You have successfully signed in.`);const l=sessionStorage.getItem("auth_redirect");l?(sessionStorage.removeItem("auth_redirect"),o.push(l)):window.location.reload()};return Ie(async()=>{window.addEventListener("show-auth-modal",a);try{await s.initialize()}catch(c){console.error("Failed to initialize auth store:",c)}}),wt(()=>{window.removeEventListener("show-auth-modal",a)}),{showAuthModal:r,authModalTab:n,hideAuthModal:u,handleAuthSuccess:h}}},ye=Tr(yx),bx=Br();ye.use(bx);ye.use(Cr);ye.component("AppLayout",X0);ye.component("Button",ae);ye.component("Input",ke);ye.component("Card",pt);ye.component("Modal",Co);ye.component("Select",qo);ye.component("Loading",tt);ye.component("Container",Ee);ye.component("ItemCard",xr);ye.component("Banner",Wo);ye.component("Badge",it);ye.component("Alert",fs);ye.component("Pagination",Uo);ye.component("NotificationContainer",Ho);ye.component("CartDrawer",wr);ye.component("CartIcon",Fm);ye.component("CartNavIcon",Um);ye.component("WatchlistNavIcon",Kl);ye.component("AuthModal",Lt);const ws=be(),xx=_r(),wx=kr(),wo=Ue(),_s=_t();Le();ws.initialize().then(()=>{ws.isAuthenticated&&_s.initializeWatchlist()});Me(()=>ws.isAuthenticated,async t=>{t?await _s.initializeWatchlist():_s.clearWatchlist()},{immediate:!1});window.branches&&xx.initializeBranches(window.branches);window.adverts&&wx.initializeAdverts(window.adverts);window.cartItems?wo.initializeCart(window.cartItems):wo.fetchCart();ye.mount("#app");const _x=async()=>{const t=document.getElementById("watchlist-nav-icon");if(t){const s=be();t.addEventListener("click",o=>{o.preventDefault(),s.isAuthenticated?window.location.href="/bid-dashboard":window.dispatchEvent(new CustomEvent("show-auth-modal",{detail:{tab:"login"}}))})}};setTimeout(async()=>{await _x()},100);ye.config.errorHandler=(t,s,o)=>{console.error("Vue error:",t,o)};ye.config.warnHandler=(t,s,o)=>{console.warn("Vue warning:",t,o)};
