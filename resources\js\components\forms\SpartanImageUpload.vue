<template>
  <div class="spartan-image-upload">
    <!-- Label -->
    <label
      v-if="label"
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <!-- Spartan Multi Image Picker Container -->
    <div 
      ref="spartanContainer"
      :id="spartanId"
      class="spartan-multi-image-picker-container row"
    >
      <!-- Spartan picker will be initialized here -->
    </div>

    <!-- Existing Images Display (for edit mode) -->
    <div v-if="existingImages && existingImages.length > 0" class="existing-images mt-4">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Existing Images</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div 
          v-for="(image, index) in existingImages" 
          :key="image.id || index"
          class="relative group"
        >
          <img 
            :src="image.url || image.thumb_url" 
            :alt="image.name || `Image ${index + 1}`"
            class="w-full h-32 object-cover rounded-lg border border-gray-200"
          />
          <button
            v-if="!disabled"
            type="button"
            @click="removeExistingImage(index)"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <XMarkIcon class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Help text -->
    <p
      v-if="helpText && !error"
      :class="helpTextClasses"
    >
      {{ helpText }}
    </p>

    <!-- Error message -->
    <p
      v-if="error"
      :class="errorClasses"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

interface ExistingImage {
  id?: string | number;
  url?: string;
  thumb_url?: string;
  name?: string;
}

interface Props {
  label?: string;
  helpText?: string;
  error?: string;
  fieldName?: string;
  maxCount?: number;
  maxFileSize?: number; // in KB
  allowedExt?: string;
  groupClassName?: string;
  rowHeight?: string;
  dropFileLabel?: string;
  disabled?: boolean;
  required?: boolean;
  existingImages?: ExistingImage[];
  spartanOptions?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  fieldName: 'media[]',
  maxFileSize: 5000, // 5MB in KB
  allowedExt: 'png|jpg|jpeg|gif',
  groupClassName: 'col-md-4 col-sm-4 col-xs-6',
  rowHeight: '200px',
  dropFileLabel: 'Drop file here',
  disabled: false,
  required: false,
});

const emit = defineEmits<{
  'add-row': [index: number];
  'remove-row': [index: number];
  'rendered-preview': [index: number];
  'extension-error': [index: number, file: any];
  'size-error': [index: number, file: any];
  'remove-existing': [index: number, image: ExistingImage];
}>();

// Reactive state
const spartanContainer = ref<HTMLDivElement>();
const spartanId = ref(`spartan-picker-${Math.random().toString(36).substr(2, 9)}`);
const spartanInstance = ref<any>(null);

// Computed properties
const labelClasses = computed(() => {
  const baseClasses = 'block text-sm font-medium mb-2';
  const colorClasses = props.error 
    ? 'text-red-700' 
    : props.disabled 
    ? 'text-gray-400' 
    : 'text-gray-700';
  
  return [baseClasses, colorClasses].join(' ');
});

const helpTextClasses = computed(() => {
  return 'mt-2 text-sm text-gray-500';
});

const errorClasses = computed(() => {
  return 'mt-2 text-sm text-red-600';
});

// Methods
const initializeSpartanPicker = () => {
  if (!spartanContainer.value) {
    return;
  }

  // Ensure jQuery and spartanMultiImagePicker are available
  if (typeof window.$ === 'undefined' || typeof window.$.fn.spartanMultiImagePicker === 'undefined') {
    console.warn('jQuery or spartanMultiImagePicker not available');
    return;
  }

  const defaultOptions = {
    fieldName: props.fieldName,
    maxCount: props.maxCount || '',
    maxFileSize: props.maxFileSize,
    allowedExt: props.allowedExt,
    groupClassName: props.groupClassName,
    rowHeight: props.rowHeight,
    dropFileLabel: props.dropFileLabel,
    onAddRow: (index: number) => {
      emit('add-row', index);
    },
    onRenderedPreview: (index: number) => {
      emit('rendered-preview', index);
    },
    onRemoveRow: (index: number) => {
      emit('remove-row', index);
    },
    onExtensionErr: (index: number, file: any) => {
      emit('extension-error', index, file);
      console.warn('File extension error:', file);
    },
    onSizeErr: (index: number, file: any) => {
      emit('size-error', index, file);
      const message = `Max size ${props.maxFileSize}KB, File size too big.`;
      console.warn(message, file);
      
      // Show notification if Notyf is available
      if (typeof window.Notyf !== 'undefined') {
        const notyf = new window.Notyf({dismissible: true});
        notyf.error(message);
      }
    }
  };

  const options = { ...defaultOptions, ...props.spartanOptions };

  try {
    spartanInstance.value = window.$(spartanContainer.value).spartanMultiImagePicker(options);
  } catch (error) {
    console.error('Failed to initialize Spartan Multi Image Picker:', error);
  }
};

const destroySpartanPicker = () => {
  if (spartanInstance.value && spartanContainer.value) {
    try {
      // Clean up the Spartan picker instance
      window.$(spartanContainer.value).empty();
      spartanInstance.value = null;
    } catch (error) {
      console.error('Failed to destroy Spartan Multi Image Picker:', error);
    }
  }
};

const removeExistingImage = (index: number) => {
  if (props.existingImages && props.existingImages[index]) {
    emit('remove-existing', index, props.existingImages[index]);
  }
};

// Lifecycle hooks
onMounted(async () => {
  await nextTick();
  initializeSpartanPicker();
});

onUnmounted(() => {
  destroySpartanPicker();
});

// Watch for prop changes that require re-initialization
watch([() => props.fieldName, () => props.maxCount, () => props.maxFileSize], async () => {
  destroySpartanPicker();
  await nextTick();
  initializeSpartanPicker();
});

// Expose methods
defineExpose({
  initializeSpartanPicker,
  destroySpartanPicker,
  spartanInstance,
});
</script>

<style scoped>
.spartan-image-upload {
  @apply w-full;
}

.existing-images img {
  transition: transform 0.2s ease;
}

.existing-images img:hover {
  transform: scale(1.05);
}
</style>
