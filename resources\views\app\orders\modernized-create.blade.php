@extends('layouts.modernized-admin')

@section('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container--default .select2-selection--single {
    height: 42px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    padding: 0 12px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 40px !important;
    padding-left: 0 !important;
    font-size: 0.875rem !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px !important;
    right: 8px !important;
}
.select2-dropdown {
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    padding: 8px 12px !important;
    font-size: 0.875rem !important;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #3b82f6 !important;
}
</style>
@endsection

@section('title', 'Point of Sale - Vertigo AMS')

@section('page-title', 'Point of Sale')
@section('page-subtitle', 'Create a new sale')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('orders.index') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Sales</span>
        <span class="lg:hidden">Back</span>
    </a>
    <button id="clear-cart-btn" class="flex items-center bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        <span class="hidden lg:inline">Clear Cart</span>
        <span class="lg:hidden">Clear</span>
    </button>
    <a href="/sales-report" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="hidden lg:inline">View Reports</span>
        <span class="lg:hidden">Reports</span>
    </a>
</div>
@endsection

@section('content')
<!-- POS Header with Search and Filters -->
<div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <!-- Search Bar -->
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text"
                       id="item-search"
                       placeholder="Search items by name, code, or description..."
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Category Filter -->
        <div class="flex items-center space-x-4">
            <select id="category-filter" class="px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200">
                <option value="">All Categories</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}">{{ $category->name }} ({{ ucfirst($category->type) }})</option>
                @endforeach
            </select>

            <!-- View Toggle and Help -->
            <div class="flex items-center space-x-3">
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button id="grid-view-btn" class="px-3 py-2 rounded-md text-sm font-medium bg-white text-gray-900 shadow-sm">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                    </button>
                    <button id="list-view-btn" class="px-3 py-2 rounded-md text-sm font-medium text-gray-500 hover:text-gray-900">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>

                <!-- Keyboard Shortcuts Help -->
                <button id="help-btn" class="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors duration-200" title="Keyboard Shortcuts">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main POS Layout -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
    <!-- Items Section (Left Side) -->
    <div class="xl:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                Available Items
                <span id="items-count" class="ml-2 text-sm text-gray-500">(Loading...)</span>
            </h3>
        </div>

        <!-- Items Grid/List Container -->
        <div class="p-6 overflow-y-auto h-full">
            <!-- Grid View -->
            <div id="items-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- Items will be loaded via AJAX -->
            </div>

            <!-- List View (Hidden by default) -->
            <div id="items-list" class="hidden space-y-2">
                <!-- Items will be loaded via AJAX -->
            </div>

            <!-- No Items Found -->
            <div id="no-items-found" class="hidden text-center py-12">
                <div class="h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <p class="text-gray-500 text-lg font-medium mb-2">No items found</p>
                <p class="text-gray-400">Try adjusting your search or filter criteria</p>
            </div>
        </div>
    </div>

    <!-- Shopping Cart Section (Right Side) -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
                        </svg>
                    </div>
                    Shopping Cart
                </div>
                <span id="cart-count" class="text-sm text-gray-500">(0 items)</span>
            </h3>
        </div>

        <!-- Cart Form -->
        <form method="POST" action="{{ route('orders.store') }}" class="flex flex-col h-full">
            @csrf

            <!-- Customer Selection -->
            <div class="px-6 py-4 border-b border-gray-200">
                <label class="block text-sm font-medium text-gray-700 mb-2">Customer</label>
                <select id="customer-select" name="order[user_id]" required class="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200">
                    <option value="2" selected>Walk-in customer</option>
                    @foreach( App\Models\User::get() as $user)
                    <option value="{{ $user->id }}">{{ $user->name ?? ''}}</option>
                    @endforeach
                </select>
            </div>

            <!-- Cart Items -->
            <div class="flex-1 overflow-y-auto">
                <div id="cart-items" class="p-6">
                    <!-- Empty Cart State -->
                    <div id="empty-cart" class="text-center py-12">
                        <div class="h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500 text-lg font-medium mb-2">Cart is empty</p>
                        <p class="text-gray-400">Add items from the left to start a sale</p>
                    </div>

                    <!-- Cart Items Container -->
                    <div id="cart-items-list" class="space-y-3 hidden">
                        <!-- Cart items will be dynamically added here -->
                    </div>
                </div>
            </div>

            <!-- Cart Summary -->
            <div class="border-t border-gray-200 bg-gray-50">
                <div class="p-4">
                    <!-- Totals -->
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Subtotal:</span>
                            <span id="subtotal-display" class="text-sm font-bold text-gray-900">MK 0.00</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Discount:</span>
                            <span id="discount-display" class="text-sm font-bold text-blue-600">MK 0.00</span>
                        </div>
                        <div class="flex justify-between items-center pt-2 border-t border-gray-300">
                            <span class="text-lg font-bold text-gray-900">Total:</span>
                            <span id="total-display" class="text-xl font-bold text-green-600">MK 0.00</span>
                        </div>
                    </div>

                    <!-- Payment Amount -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount Paid</label>
                        <div class="relative">
                            <input type="number"
                                   id="amount-paid"
                                   name="order[amount_paid]"
                                   min="0"
                                   step="0.01"
                                   class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                                   value="0"
                                   placeholder="0.00">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm">MK</span>
                            </div>
                        </div>
                        <div class="mt-2 flex justify-between items-center text-sm">
                            <span class="text-gray-600">Balance Due:</span>
                            <span id="balance-display" class="font-bold text-red-600">MK 0.00</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-2">
                        <button type="button" id="quick-pay-btn" class="w-full bg-green-600 text-white py-2.5 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200 text-sm">
                            Quick Pay (Full Amount)
                        </button>
                        <button type="submit" class="w-full bg-blue-600 text-white py-2.5 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm">
                            Complete Sale
                        </button>
                    </div>
                </div>
            </div>

            <!-- Hidden Fields -->
            <input type="hidden" name="order[sub_total]" id="sub-total-input" value="0">
            <input type="hidden" name="order[discount]" id="discount-input" value="0">
            <input type="hidden" name="order[amount_total]" id="amount-total-input" value="0">
            <input type="hidden" name="order[user_id]" id="user-id-input" value="2">
            <input type="hidden" name="order[amount_paid]" id="amount-paid-input" value="0">
        </form>
    </div>
</div>

<!-- Keyboard Shortcuts Help Modal -->
<div id="help-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Keyboard Shortcuts</h3>
            <button id="close-help-btn" class="text-gray-400 hover:text-gray-600">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="space-y-3">
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Focus search</span>
                <kbd class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Ctrl + F</kbd>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Complete sale</span>
                <kbd class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Ctrl + Enter</kbd>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Clear cart</span>
                <kbd class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Ctrl + D</kbd>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Quick pay</span>
                <kbd class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Ctrl + P</kbd>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Clear search</span>
                <kbd class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">Escape</kbd>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // POS System State
    let cart = [];
    let cartCounter = 0;

    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // DOM Elements
    const itemSearch = document.getElementById('item-search');
    const categoryFilter = document.getElementById('category-filter');
    const gridViewBtn = document.getElementById('grid-view-btn');
    const listViewBtn = document.getElementById('list-view-btn');
    const itemsGrid = document.getElementById('items-grid');
    const itemsList = document.getElementById('items-list');
    const noItemsFound = document.getElementById('no-items-found');
    const itemsCount = document.getElementById('items-count');
    const cartCount = document.getElementById('cart-count');
    const emptyCart = document.getElementById('empty-cart');
    const cartItemsList = document.getElementById('cart-items-list');
    const clearCartBtn = document.getElementById('clear-cart-btn');
    const quickPayBtn = document.getElementById('quick-pay-btn');
    const amountPaidInput = document.getElementById('amount-paid');
    const helpBtn = document.getElementById('help-btn');
    const helpModal = document.getElementById('help-modal');
    const closeHelpBtn = document.getElementById('close-help-btn');

    // Initialize Select2 for customer selection
    $('#customer-select').select2({
        placeholder: 'Search for a customer...',
        allowClear: false,
        width: '100%'
    }).on('change', function() {
        document.getElementById('user-id-input').value = this.value;
    });

    // View Toggle
    gridViewBtn.addEventListener('click', function() {
        itemsGrid.classList.remove('hidden');
        itemsList.classList.add('hidden');
        gridViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        gridViewBtn.classList.remove('text-gray-500');
        listViewBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        listViewBtn.classList.add('text-gray-500');
    });

    listViewBtn.addEventListener('click', function() {
        itemsList.classList.remove('hidden');
        itemsGrid.classList.add('hidden');
        listViewBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
        listViewBtn.classList.remove('text-gray-500');
        gridViewBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
        gridViewBtn.classList.add('text-gray-500');
    });

    // AJAX Search and Filter
    let searchTimeout;

    function searchItems() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            const searchTerm = itemSearch.value;
            const selectedCategory = categoryFilter.value;

            console.log('Searching items:', { searchTerm, selectedCategory });

            // Show loading state
            itemsGrid.innerHTML = '<div class="col-span-full text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-gray-500">Searching items...</p></div>';
            itemsList.innerHTML = '<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-gray-500">Searching items...</p></div>';

            // Make AJAX request
            fetch(`{{ route('orders.search-items') }}?search=${encodeURIComponent(searchTerm)}&category=${encodeURIComponent(selectedCategory)}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Search response:', data);
                    updateItemsDisplay(data.items);
                    itemsCount.textContent = `(${data.count} items)`;
                })
                .catch(error => {
                    console.error('Search error:', error);
                    itemsGrid.innerHTML = '<div class="col-span-full text-center py-8 text-red-600"><p>Error loading items. Please try again.</p><p class="text-sm text-gray-500 mt-1">' + error.message + '</p></div>';
                    itemsList.innerHTML = '<div class="text-center py-8 text-red-600"><p>Error loading items. Please try again.</p><p class="text-sm text-gray-500 mt-1">' + error.message + '</p></div>';
                });
        }, 300); // Debounce search
    }

    function updateItemsDisplay(items) {
        if (items.length === 0) {
            noItemsFound.classList.remove('hidden');
            itemsGrid.classList.add('hidden');
            itemsList.classList.add('hidden');
            return;
        }

        noItemsFound.classList.add('hidden');

        // Update grid view
        itemsGrid.innerHTML = items.map(item => `
            <div class="item-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer"
                 data-item='${JSON.stringify(item)}'>
                <div class="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
                    <img src="${item.image}"
                         alt="${item.name}"
                         class="w-full h-full object-cover"
                         onerror="this.src='/img/product.jpeg'">
                </div>
                <h4 class="font-medium text-gray-900 text-sm mb-1 truncate">${item.name}</h4>
                <p class="text-xs text-gray-500 mb-2">${item.category.name}</p>
                <div class="flex items-center justify-between">
                    <span class="text-lg font-bold text-green-600">MK ${parseFloat(item.selling_price).toFixed(2)}</span>
                    <button class="add-item-btn bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors duration-200">
                        Add
                    </button>
                </div>
            </div>
        `).join('');

        // Update list view
        itemsList.innerHTML = items.map(item => `
            <div class="item-row bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer flex items-center space-x-4"
                 data-item='${JSON.stringify(item)}'>
                <div class="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <img src="${item.image}"
                         alt="${item.name}"
                         class="w-full h-full object-cover"
                         onerror="this.src='/img/product.jpeg'">
                </div>
                <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-gray-900 truncate">${item.name}</h4>
                    <p class="text-sm text-gray-500">${item.category.name}</p>
                    <p class="text-xs text-gray-400 truncate">${item.code}</p>
                </div>
                <div class="text-right">
                    <div class="text-lg font-bold text-green-600">MK ${parseFloat(item.selling_price).toFixed(2)}</div>
                    <button class="add-item-btn bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 transition-colors duration-200 mt-1">
                        Add to Cart
                    </button>
                </div>
            </div>
        `).join('');

        // Show appropriate view
        if (gridViewBtn.classList.contains('bg-white')) {
            itemsGrid.classList.remove('hidden');
            itemsList.classList.add('hidden');
        } else {
            itemsList.classList.remove('hidden');
            itemsGrid.classList.add('hidden');
        }
    }

    itemSearch.addEventListener('input', searchItems);
    categoryFilter.addEventListener('change', searchItems);

    // Initial load
    searchItems();

    // Add Item to Cart
    function addToCart(item) {
        const existingIndex = cart.findIndex(cartItem => cartItem.id === item.id);

        if (existingIndex > -1) {
            cart[existingIndex].quantity += 1;
        } else {
            cart.push({
                ...item,
                quantity: 1,
                cartId: ++cartCounter
            });
        }

        updateCartDisplay();
        updateTotals();
    }

    // Remove Item from Cart
    function removeFromCart(cartId) {
        cart = cart.filter(item => item.cartId !== cartId);
        updateCartDisplay();
        updateTotals();
    }

    // Update Item Quantity
    function updateQuantity(cartId, quantity) {
        const item = cart.find(item => item.cartId === cartId);
        if (item) {
            item.quantity = Math.max(1, quantity);
            updateCartDisplay();
            updateTotals();
        }
    }

    // Update Cart Display
    function updateCartDisplay() {
        cartCount.textContent = `(${cart.length} items)`;

        if (cart.length === 0) {
            emptyCart.classList.remove('hidden');
            cartItemsList.classList.add('hidden');
        } else {
            emptyCart.classList.add('hidden');
            cartItemsList.classList.remove('hidden');

            cartItemsList.innerHTML = cart.map(item => `
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            <img src="${item.image}" alt="${item.name}" class="w-full h-full object-cover" onerror="this.src='/img/product.jpeg'">
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="font-medium text-gray-900 text-sm truncate">${item.name}</h4>
                            <p class="text-xs text-gray-500">${item.category.name}</p>
                            <p class="text-sm font-bold text-green-600">MK ${parseFloat(item.selling_price).toFixed(2)}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="updateQuantity(${item.cartId}, ${item.quantity - 1})" class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors duration-200">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                </svg>
                            </button>
                            <span class="w-8 text-center font-medium">${item.quantity}</span>
                            <button type="button" onclick="updateQuantity(${item.cartId}, ${item.quantity + 1})" class="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors duration-200">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                            <button type="button" onclick="removeFromCart(${item.cartId})" class="w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center hover:bg-red-200 transition-colors duration-200 ml-2">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    }

    // Update Totals
    function updateTotals() {
        const subtotal = cart.reduce((sum, item) => sum + (item.selling_price * item.quantity), 0);
        const discount = 0; // Can be enhanced later
        const total = subtotal - discount;
        const amountPaid = parseFloat(amountPaidInput.value) || 0;
        const balance = total - amountPaid;

        document.getElementById('subtotal-display').textContent = `MK ${subtotal.toFixed(2)}`;
        document.getElementById('discount-display').textContent = `MK ${discount.toFixed(2)}`;
        document.getElementById('total-display').textContent = `MK ${total.toFixed(2)}`;
        document.getElementById('balance-display').textContent = `MK ${balance.toFixed(2)}`;

        // Update hidden form fields
        document.getElementById('sub-total-input').value = subtotal.toFixed(2);
        document.getElementById('discount-input').value = discount.toFixed(2);
        document.getElementById('amount-total-input').value = total.toFixed(2);
        document.getElementById('amount-paid-input').value = amountPaid.toFixed(2);

        // Update balance color
        const balanceDisplay = document.getElementById('balance-display');
        if (balance > 0) {
            balanceDisplay.className = 'font-bold text-red-600';
        } else if (balance < 0) {
            balanceDisplay.className = 'font-bold text-green-600';
        } else {
            balanceDisplay.className = 'font-bold text-gray-600';
        }
    }

    // Event Listeners
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('add-item-btn')) {
            e.preventDefault();
            const itemCard = e.target.closest('.item-card, .item-row');
            const itemData = JSON.parse(itemCard.dataset.item);
            addToCart(itemData);
        }
    });

    clearCartBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to clear the cart?')) {
            cart = [];
            updateCartDisplay();
            updateTotals();
        }
    });

    quickPayBtn.addEventListener('click', function() {
        const total = parseFloat(document.getElementById('amount-total-input').value) || 0;
        amountPaidInput.value = total.toFixed(2);
        updateTotals();
    });

    amountPaidInput.addEventListener('input', updateTotals);

    // Help Modal
    helpBtn.addEventListener('click', function() {
        helpModal.classList.remove('hidden');
    });

    closeHelpBtn.addEventListener('click', function() {
        helpModal.classList.add('hidden');
    });

    helpModal.addEventListener('click', function(e) {
        if (e.target === helpModal) {
            helpModal.classList.add('hidden');
        }
    });

    // Make functions global for onclick handlers
    window.updateQuantity = updateQuantity;
    window.removeFromCart = removeFromCart;

    // Keyboard Shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F: Focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            itemSearch.focus();
        }

        // Ctrl/Cmd + Enter: Complete sale
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (cart.length > 0) {
                document.querySelector('form').submit();
            }
        }

        // Ctrl/Cmd + D: Clear cart
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            clearCartBtn.click();
        }

        // Ctrl/Cmd + P: Quick pay
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            quickPayBtn.click();
        }

        // Escape: Clear search
        if (e.key === 'Escape') {
            itemSearch.value = '';
            categoryFilter.value = '';
            filterItems();
        }
    });

    // Form submission enhancement
    document.querySelector('form').addEventListener('submit', function(e) {
        if (cart.length === 0) {
            e.preventDefault();
            alert('Please add items to the cart before completing the sale.');
            return;
        }

        // Add cart items as hidden inputs
        cart.forEach((item, index) => {
            // Item ID
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = `order[sales][${index}][item_id]`;
            hiddenInput.value = item.id;
            this.appendChild(hiddenInput);

            // Quantity
            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = `order[sales][${index}][quantity]`;
            quantityInput.value = item.quantity;
            this.appendChild(quantityInput);

            // Selling Price
            const priceInput = document.createElement('input');
            priceInput.type = 'hidden';
            priceInput.name = `order[sales][${index}][selling_price]`;
            priceInput.value = item.selling_price;
            this.appendChild(priceInput);

            // Total for this line item
            const totalInput = document.createElement('input');
            totalInput.type = 'hidden';
            totalInput.name = `order[sales][${index}][total]`;
            totalInput.value = (item.selling_price * item.quantity).toFixed(2);
            this.appendChild(totalInput);
        });
    });

    // Show keyboard shortcuts help
    console.log('POS Keyboard Shortcuts:');
    console.log('Ctrl/Cmd + F: Focus search');
    console.log('Ctrl/Cmd + Enter: Complete sale');
    console.log('Ctrl/Cmd + D: Clear cart');
    console.log('Ctrl/Cmd + P: Quick pay');
    console.log('Escape: Clear search and filters');
});
</script>
@endsection
