import{c as i,a as m,_ as p,b as r,d as l,e as u,r as s}from"./Modal.vue_vue_type_script_setup_true_lang-96a11b6a.js";const a=i({setup(){const e=s(!1),o=s(""),t=s(""),c=s(""),n=s(!1);return{showModal:e,email:o,password:t,searchQuery:c,isLoading:n,validateEmail:()=>{n.value=!0,setTimeout(()=>{n.value=!1},2e3)}}}}),f=m();a.use(f);a.component("Button",p);a.component("Input",r);a.component("Card",l);a.component("Modal",u);a.mount("#app");
