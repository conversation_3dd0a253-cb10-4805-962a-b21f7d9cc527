<template>
  <div class="form-file-upload">
    <!-- Label -->
    <label
      v-if="label"
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <!-- Spartan Multi Image Picker Container -->
    <div
      v-if="useSpartanPicker && isImageUpload"
      ref="spartanContainer"
      :id="spartanId"
      class="spartan-multi-image-picker-container"
    >
      <!-- Spartan picker will be initialized here -->
    </div>

    <!-- Standard Drop zone (fallback or non-image uploads) -->
    <div
      v-else
      ref="dropZone"
      :class="dropZoneClasses"
      @click="openFileDialog"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <!-- Hidden file input -->
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="disabled"
        class="hidden"
        @change="handleFileSelect"
      />

      <!-- Upload content -->
      <div v-if="!files.length" class="text-center py-6">
        <CloudArrowUpIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <div class="text-sm text-gray-600">
          <span class="font-medium text-primary-600 hover:text-primary-500 cursor-pointer">
            Click to upload
          </span>
          <span v-if="!disabled"> or drag and drop</span>
        </div>
        <p v-if="acceptText" class="text-xs text-gray-500 mt-1">
          {{ acceptText }}
        </p>
        <p v-if="maxSize" class="text-xs text-gray-500">
          Max file size: {{ formatFileSize(maxSize) }}
        </p>
      </div>

      <!-- File list -->
      <div v-else class="space-y-2">
        <div
          v-for="(file, index) in files"
          :key="file.id"
          :class="fileItemClasses(file)"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3 flex-1 min-w-0">
              <!-- File icon -->
              <div :class="fileIconClasses(file)">
                <DocumentIcon v-if="!isImage(file)" class="h-5 w-5" />
                <PhotoIcon v-else class="h-5 w-5" />
              </div>

              <!-- File info -->
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ file.name }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ formatFileSize(file.size) }}
                  <span v-if="file.status === 'uploading'" class="ml-2">
                    {{ Math.round(file.progress || 0) }}%
                  </span>
                </p>
              </div>

              <!-- Status indicator -->
              <div class="flex-shrink-0">
                <div
                  v-if="file.status === 'uploading'"
                  class="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"
                />
                <CheckCircleIcon
                  v-else-if="file.status === 'success'"
                  class="h-5 w-5 text-green-500"
                />
                <ExclamationCircleIcon
                  v-else-if="file.status === 'error'"
                  class="h-5 w-5 text-red-500"
                />
              </div>
            </div>

            <!-- Remove button -->
            <button
              v-if="!disabled && file.status !== 'uploading'"
              type="button"
              @click.stop="removeFile(index)"
              class="ml-3 text-gray-400 hover:text-red-500 transition-colors duration-200"
            >
              <XMarkIcon class="h-4 w-4" />
            </button>
          </div>

          <!-- Progress bar -->
          <div
            v-if="file.status === 'uploading'"
            class="mt-2 bg-gray-200 rounded-full h-1"
          >
            <div
              class="bg-primary-500 h-1 rounded-full transition-all duration-300"
              :style="{ width: `${file.progress || 0}%` }"
            />
          </div>

          <!-- Error message -->
          <p
            v-if="file.status === 'error' && file.error"
            class="mt-1 text-xs text-red-600"
          >
            {{ file.error }}
          </p>
        </div>

        <!-- Add more files button -->
        <button
          v-if="multiple && !disabled"
          type="button"
          @click="openFileDialog"
          class="w-full mt-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
        >
          Add more files
        </button>
      </div>
    </div>

    <!-- Help text -->
    <p
      v-if="helpText && !error"
      :class="helpTextClasses"
    >
      {{ helpText }}
    </p>

    <!-- Error message -->
    <p
      v-if="error"
      :class="errorClasses"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  PhotoIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/24/outline';

interface FileItem {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress?: number;
  error?: string;
  url?: string;
}

interface Props {
  modelValue?: FileItem[];
  label?: string;
  helpText?: string;
  error?: string;
  accept?: string;
  acceptText?: string;
  multiple?: boolean;
  disabled?: boolean;
  required?: boolean;
  maxSize?: number; // in bytes
  maxFiles?: number;
  uploadUrl?: string;
  uploadHeaders?: Record<string, string>;
  autoUpload?: boolean;
  useSpartanPicker?: boolean; // New prop to enable Spartan picker
  fieldName?: string; // Field name for form submission
  spartanOptions?: Record<string, any>; // Custom Spartan options
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  disabled: false,
  required: false,
  maxSize: 10 * 1024 * 1024, // 10MB
  autoUpload: true,
  useSpartanPicker: false,
  fieldName: 'media[]',
  accept: 'image/*',
});

const emit = defineEmits<{
  'update:modelValue': [files: FileItem[]];
  'upload-start': [file: FileItem];
  'upload-progress': [file: FileItem, progress: number];
  'upload-success': [file: FileItem, response: any];
  'upload-error': [file: FileItem, error: string];
  'file-add': [file: FileItem];
  'file-remove': [file: FileItem];
  'spartan-add': [index: number];
  'spartan-remove': [index: number];
  'spartan-rendered': [index: number];
}>();

// Reactive state
const fileInput = ref<HTMLInputElement>();
const dropZone = ref<HTMLDivElement>();
const spartanContainer = ref<HTMLDivElement>();
const files = ref<FileItem[]>(props.modelValue || []);
const isDragOver = ref(false);
const spartanId = ref(`spartan-picker-${Math.random().toString(36).substr(2, 9)}`);
const spartanInstance = ref<any>(null);

// Computed properties
const labelClasses = computed(() => {
  const baseClasses = 'block text-sm font-medium mb-2';
  const colorClasses = props.error
    ? 'text-red-700'
    : props.disabled
    ? 'text-gray-400'
    : 'text-gray-700';

  return [baseClasses, colorClasses].join(' ');
});

const dropZoneClasses = computed(() => {
  const baseClasses = 'border-2 border-dashed rounded-lg transition-colors duration-200 cursor-pointer';

  if (props.disabled) {
    return [baseClasses, 'border-gray-200 bg-gray-50 cursor-not-allowed'].join(' ');
  }

  if (props.error) {
    return [baseClasses, 'border-red-300 bg-red-50'].join(' ');
  }

  if (isDragOver.value) {
    return [baseClasses, 'border-primary-400 bg-primary-50'].join(' ');
  }

  return [baseClasses, 'border-gray-300 hover:border-gray-400'].join(' ');
});

const helpTextClasses = computed(() => {
  return 'mt-1 text-sm text-gray-500';
});

const errorClasses = computed(() => {
  return 'mt-1 text-sm text-red-600';
});

const isImageUpload = computed(() => {
  return props.accept?.includes('image') || props.accept === 'image/*';
});

// Methods
const fileItemClasses = (file: FileItem) => {
  const baseClasses = 'p-3 border rounded-md';
  
  switch (file.status) {
    case 'error':
      return [baseClasses, 'border-red-200 bg-red-50'].join(' ');
    case 'success':
      return [baseClasses, 'border-green-200 bg-green-50'].join(' ');
    case 'uploading':
      return [baseClasses, 'border-blue-200 bg-blue-50'].join(' ');
    default:
      return [baseClasses, 'border-gray-200 bg-gray-50'].join(' ');
  }
};

const fileIconClasses = (file: FileItem) => {
  const baseClasses = 'flex-shrink-0 p-1 rounded';
  
  switch (file.status) {
    case 'error':
      return [baseClasses, 'text-red-500 bg-red-100'].join(' ');
    case 'success':
      return [baseClasses, 'text-green-500 bg-green-100'].join(' ');
    case 'uploading':
      return [baseClasses, 'text-blue-500 bg-blue-100'].join(' ');
    default:
      return [baseClasses, 'text-gray-500 bg-gray-100'].join(' ');
  }
};

const isImage = (file: FileItem): boolean => {
  return file.type.startsWith('image/');
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const generateFileId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

const validateFile = (file: File): string | null => {
  // Check file size
  if (props.maxSize && file.size > props.maxSize) {
    return `File size exceeds ${formatFileSize(props.maxSize)}`;
  }
  
  // Check file type
  if (props.accept) {
    const acceptedTypes = props.accept.split(',').map(type => type.trim());
    const isAccepted = acceptedTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase());
      }
      return file.type.match(type.replace('*', '.*'));
    });
    
    if (!isAccepted) {
      return 'File type not accepted';
    }
  }
  
  return null;
};

const openFileDialog = () => {
  if (props.disabled) return;
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const selectedFiles = Array.from(target.files || []);
  addFiles(selectedFiles);
  
  // Clear the input so the same file can be selected again
  target.value = '';
};

const handleDragOver = (event: DragEvent) => {
  if (props.disabled) return;
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  isDragOver.value = false;
};

const handleDrop = (event: DragEvent) => {
  if (props.disabled) return;
  
  isDragOver.value = false;
  const droppedFiles = Array.from(event.dataTransfer?.files || []);
  addFiles(droppedFiles);
};

const addFiles = (newFiles: File[]) => {
  const validFiles: FileItem[] = [];
  
  for (const file of newFiles) {
    // Check max files limit
    if (props.maxFiles && files.value.length + validFiles.length >= props.maxFiles) {
      break;
    }
    
    // Validate file
    const error = validateFile(file);
    
    const fileItem: FileItem = {
      id: generateFileId(),
      name: file.name,
      size: file.size,
      type: file.type,
      file,
      status: error ? 'error' : 'pending',
      error,
    };
    
    validFiles.push(fileItem);
    emit('file-add', fileItem);
    
    // Auto upload if enabled and no error
    if (props.autoUpload && !error && props.uploadUrl) {
      uploadFile(fileItem);
    }
  }
  
  if (!props.multiple) {
    files.value = validFiles.slice(0, 1);
  } else {
    files.value.push(...validFiles);
  }
  
  emit('update:modelValue', files.value);
};

const removeFile = (index: number) => {
  const file = files.value[index];
  files.value.splice(index, 1);
  emit('file-remove', file);
  emit('update:modelValue', files.value);
};

const uploadFile = async (fileItem: FileItem) => {
  if (!props.uploadUrl) return;

  fileItem.status = 'uploading';
  fileItem.progress = 0;
  emit('upload-start', fileItem);

  const formData = new FormData();
  formData.append('file', fileItem.file);

  try {
    const xhr = new XMLHttpRequest();

    // Track upload progress
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = (event.loaded / event.total) * 100;
        fileItem.progress = progress;
        emit('upload-progress', fileItem, progress);
      }
    });

    // Handle completion
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        fileItem.status = 'success';
        fileItem.progress = 100;

        try {
          const response = JSON.parse(xhr.responseText);
          fileItem.url = response.url || response.path;
          emit('upload-success', fileItem, response);
        } catch (e) {
          emit('upload-success', fileItem, xhr.responseText);
        }
      } else {
        fileItem.status = 'error';
        fileItem.error = `Upload failed: ${xhr.statusText}`;
        emit('upload-error', fileItem, fileItem.error);
      }
    });

    // Handle errors
    xhr.addEventListener('error', () => {
      fileItem.status = 'error';
      fileItem.error = 'Upload failed: Network error';
      emit('upload-error', fileItem, fileItem.error);
    });

    // Set headers
    if (props.uploadHeaders) {
      Object.entries(props.uploadHeaders).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
    }

    // Start upload
    xhr.open('POST', props.uploadUrl);
    xhr.send(formData);

  } catch (error) {
    fileItem.status = 'error';
    fileItem.error = `Upload failed: ${error}`;
    emit('upload-error', fileItem, fileItem.error);
  }
};

// Spartan Multi Image Picker methods
const initializeSpartanPicker = () => {
  if (!props.useSpartanPicker || !isImageUpload.value || !spartanContainer.value) {
    return;
  }

  // Ensure jQuery and spartanMultiImagePicker are available
  if (typeof window.$ === 'undefined' || typeof window.$.fn.spartanMultiImagePicker === 'undefined') {
    console.warn('jQuery or spartanMultiImagePicker not available');
    return;
  }

  const defaultOptions = {
    fieldName: props.fieldName,
    maxCount: props.maxFiles || '',
    maxFileSize: props.maxSize ? Math.floor(props.maxSize / 1000) : '5000', // Convert to KB
    allowedExt: 'png|jpg|jpeg|gif',
    groupClassName: 'col-md-4 col-sm-4 col-xs-6',
    rowHeight: '200px',
    dropFileLabel: 'Drop file here',
    onAddRow: (index: number) => {
      emit('spartan-add', index);
    },
    onRenderedPreview: (index: number) => {
      emit('spartan-rendered', index);
    },
    onRemoveRow: (index: number) => {
      emit('spartan-remove', index);
    },
    onExtensionErr: (index: number, file: any) => {
      console.warn('File extension error:', file);
    },
    onSizeErr: (index: number, file: any) => {
      const message = `Max size ${Math.floor((props.maxSize || 5000000) / 1000)}KB, File size too big.`;
      console.warn(message, file);
      // You can emit an error event here if needed
    }
  };

  const options = { ...defaultOptions, ...props.spartanOptions };

  try {
    spartanInstance.value = window.$(spartanContainer.value).spartanMultiImagePicker(options);
  } catch (error) {
    console.error('Failed to initialize Spartan Multi Image Picker:', error);
  }
};

const destroySpartanPicker = () => {
  if (spartanInstance.value && spartanContainer.value) {
    try {
      // Clean up the Spartan picker instance
      window.$(spartanContainer.value).empty();
      spartanInstance.value = null;
    } catch (error) {
      console.error('Failed to destroy Spartan Multi Image Picker:', error);
    }
  }
};

// Lifecycle hooks
onMounted(async () => {
  if (props.useSpartanPicker && isImageUpload.value) {
    await nextTick();
    initializeSpartanPicker();
  }
});

onUnmounted(() => {
  destroySpartanPicker();
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    files.value = newValue;
  }
}, { deep: true });

// Watch for Spartan picker prop changes
watch(() => props.useSpartanPicker, async (newValue) => {
  if (newValue && isImageUpload.value) {
    await nextTick();
    initializeSpartanPicker();
  } else {
    destroySpartanPicker();
  }
});

// Expose methods
defineExpose({
  openFileDialog,
  uploadFile,
  removeFile,
  addFiles,
  initializeSpartanPicker,
  destroySpartanPicker,
});
</script>
