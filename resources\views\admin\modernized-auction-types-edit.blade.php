@extends('layouts.modernized-admin')

@section('title', 'Edit Auction Category - Vertigo AMS')

@section('page-title', 'Edit Auction Category')
@section('page-subtitle', 'Update the details of this auction category.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auction-types.show', $auctionType) }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
        <span class="hidden lg:inline">View Category</span>
        <span class="lg:hidden">View</span>
    </a>
    <a href="{{ route('auction-types.index') }}" class="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Back to Categories</span>
        <span class="lg:hidden">Back</span>
    </a>
</div>
@endsection

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Form Card -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Category Details</h3>
            <p class="text-sm text-gray-600 mt-1">Update the information below to modify this auction category</p>
        </div>
        
        <form method="POST" action="{{ route('auction-types.update', $auctionType) }}" enctype="multipart/form-data" class="p-6">
            @csrf
            @method('PUT')
            
            <!-- Category Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name', $auctionType->name) }}" 
                       required
                       maxlength="255"
                       placeholder="Enter category name"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('name') border-red-300 @enderror">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Category Type -->
            <div class="mb-6">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Type <span class="text-red-500">*</span>
                </label>
                <select id="type" 
                        name="type" 
                        required
                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('type') border-red-300 @enderror">
                    <option value="">Select category type...</option>
                    <option value="online" {{ old('type', $auctionType->type) == 'online' ? 'selected' : '' }}>Online</option>
                    <option value="live" {{ old('type', $auctionType->type) == 'live' ? 'selected' : '' }}>Live</option>
                    <option value="cash" {{ old('type', $auctionType->type) == 'cash' ? 'selected' : '' }}>Cash Sale</option>
                </select>
                @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Choose the type of auction this category will be used for</p>
            </div>

            <!-- Description -->
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          maxlength="255"
                          placeholder="Enter a description for this category (optional)"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('description') border-red-300 @enderror">{{ old('description', $auctionType->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Provide additional details about this category</p>
            </div>

            <!-- Select Products -->
            <div class="mb-6">
                <label for="items" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Products
                </label>
                <div class="tom-select-custom">
                    <select id="items"
                            name="items[]"
                            multiple
                            class="js-select form-select"
                            data-hs-tom-select-options='{
                                "placeholder": "Search and select products...",
                                "maxItems": null
                            }'>
                        @php $selectedItems = old('items', $auctionType->items->pluck('id')->toArray()) @endphp
                        @foreach($items as $item)
                        <option value="{{ $item->id }}"
                                {{ in_array($item->id, $selectedItems) ? 'selected' : '' }}
                                data-option-template='<span class="d-flex align-items-center"><img class="avatar avatar-xss avatar-circle me-2" src="{{ $item->image ?? asset('assets/img/placeholder.jpg') }}" alt="{{ $item->name }}" /><span class="text-truncate">{{ $item->name }}</span></span>'>
                            {{ $item->name }}
                        </option>
                        @endforeach
                    </select>
                </div>
                @error('items')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Search and select products to include in this category</p>
            </div>

            <!-- Image Upload Section -->
            <div class="mb-6" id="auction-type-images-app">
                @php
                    $images = $auctionType->getMedia('media');
                    $existingImages = $images->map(function($image) {
                        return [
                            'id' => $image->id,
                            'url' => $image->getUrl(),
                            'thumb_url' => $image->getUrl('thumb'),
                            'name' => $image->name
                        ];
                    })->toArray();
                @endphp

                <image-upload-field
                    label="Category Images"
                    help-text="Upload or manage images for this auction category. Maximum 5 images, 10MB each."
                    field-name="media[]"
                    :max-count="5"
                    :max-file-size="10000"
                    allowed-ext="png|jpg|jpeg|gif"
                    :existing-images="@json($existingImages)"
                    @files-changed="handleFilesChanged"
                    @add-row="handleAddRow"
                    @size-error="handleSizeError"
                    @extension-error="handleExtensionError"
                    @remove-existing="removeExistingImage"
                ></image-upload-field>

                @error('media')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('auction-types.show', $auctionType) }}" 
                   class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 border border-transparent rounded-lg hover:from-primary-600 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Category
                </button>
            </div>
        </form>
    </div>

    <!-- Category Info -->
    <div class="mt-8 bg-gray-50 rounded-xl p-6 border border-gray-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Category Information</h3>
                <div class="mt-2 text-sm text-gray-600">
                    <p><strong>Created:</strong> {{ $auctionType->created_at->format('M j, Y \a\t g:i A') }}</p>
                    <p><strong>Last Updated:</strong> {{ $auctionType->updated_at->format('M j, Y \a\t g:i A') }}</p>
                    <p><strong>Created By:</strong> {{ optional($auctionType->createdBy)->name ?? 'Unknown' }}</p>
                    <p><strong>Current Items:</strong> {{ $auctionType->items()->whereNull('closed_by')->count() }} active items</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Vue app for image upload
    const { createApp } = Vue;

    createApp({
        components: {
            ImageUploadField: window.ImageUploadField
        },
        setup() {
            const handleFilesChanged = (count) => {
                console.log('Auction type images count:', count);
            };

            const handleAddRow = (index) => {
                console.log('Added image upload row:', index);
            };

            const handleSizeError = (index, file) => {
                console.log('File size error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('File size too large. Maximum 10MB allowed.');
                }
            };

            const handleExtensionError = (index, file) => {
                console.log('File extension error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('Invalid file type. Only PNG, JPG, JPEG, and GIF are allowed.');
                }
            };

            const removeExistingImage = async (index, image) => {
                try {
                    const response = await fetch(`/delete-media/${image.id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Content-Type': 'application/json',
                        },
                    });

                    if (response.ok) {
                        if (typeof Notyf !== 'undefined') {
                            const notyf = new Notyf({dismissible: true});
                            notyf.success('Image removed successfully');
                        }
                    } else {
                        throw new Error('Failed to delete image');
                    }
                } catch (error) {
                    console.error('Failed to remove image:', error);
                    if (typeof Notyf !== 'undefined') {
                        const notyf = new Notyf({dismissible: true});
                        notyf.error('Failed to remove image. Please try again.');
                    }
                }
            };

            return {
                handleFilesChanged,
                handleAddRow,
                handleSizeError,
                handleExtensionError,
                removeExistingImage
            };
        }
    }).mount('#auction-type-images-app');

    console.log('Modernized Edit Auction Category page loaded');
});
</script>
@endpush
