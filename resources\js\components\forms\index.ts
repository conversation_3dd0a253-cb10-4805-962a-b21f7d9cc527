// Form component exports for Vertigo AMS
// This file provides centralized exports for all form components

// Form Components
export { default as Form } from './Form.vue';
export { default as FormField } from './FormField.vue';
export { default as FormGroup } from './FormGroup.vue';
export { default as FormInput } from './FormInput.vue';
export { default as FormTextarea } from './FormTextarea.vue';
export { default as FormFileUpload } from './FormFileUpload.vue';
export { default as SpartanImageUpload } from './SpartanImageUpload.vue';
export { default as ImageUploadField } from './ImageUploadField.vue';

// Component type exports
export type { 
  FormInputProps, 
  FormTextareaProps, 
  FormFileUploadProps, 
  FileItem 
} from '@/types';

// Validation utilities
export { 
  validationRules, 
  validators, 
  validateField, 
  validateForm, 
  commonRules, 
  createValidator, 
  debounceValidator 
} from '@/utils/validation';

export type { ValidationRule, ValidationResult } from '@/utils/validation';
