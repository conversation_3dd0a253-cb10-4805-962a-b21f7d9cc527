# Image Upload Components

This document provides comprehensive documentation for the image upload components in the Vertigo AMS modernized admin UI.

## Overview

We have created three reusable image upload components that integrate with the existing spartanMultiImagePicker library:

1. **FormFileUpload** - Enhanced version with optional Spartan picker support
2. **SpartanImageUpload** - Dedicated Spartan Multi Image Picker component
3. **ImageUploadField** - Simple wrapper for easy usage

## Components

### 1. SpartanImageUpload

The core component that wraps the spartanMultiImagePicker jQuery plugin.

#### Props

```typescript
interface Props {
  label?: string;                    // Field label
  helpText?: string;                 // Help text below the field
  error?: string;                    // Error message
  fieldName?: string;                // Form field name (default: 'media[]')
  maxCount?: number;                 // Maximum number of files
  maxFileSize?: number;              // Max file size in KB (default: 5000)
  allowedExt?: string;               // Allowed extensions (default: 'png|jpg|jpeg|gif')
  groupClassName?: string;           // CSS classes for grid layout
  rowHeight?: string;                // Height of upload boxes
  dropFileLabel?: string;            // Drop zone label
  disabled?: boolean;                // Disable the component
  required?: boolean;                // Mark as required field
  existingImages?: ExistingImage[];  // Array of existing images for edit mode
  spartanOptions?: Record<string, any>; // Custom Spartan options
}
```

#### Events

```typescript
// Emitted events
'add-row': [index: number];                           // When a new upload slot is added
'remove-row': [index: number];                        // When an upload slot is removed
'rendered-preview': [index: number];                  // When image preview is rendered
'extension-error': [index: number, file: any];       // File extension error
'size-error': [index: number, file: any];           // File size error
'remove-existing': [index: number, image: ExistingImage]; // Existing image removed
```

#### Usage Example

```vue
<template>
  <SpartanImageUpload
    label="Product Images"
    help-text="Upload up to 5 images. Max 5MB each."
    field-name="product_images[]"
    :max-count="5"
    :max-file-size="5000"
    :existing-images="existingImages"
    @add-row="handleAddRow"
    @remove-existing="handleRemoveExisting"
  />
</template>

<script setup>
import { SpartanImageUpload } from '@/components/forms';

const existingImages = ref([
  { id: 1, url: '/storage/images/product1.jpg' },
  { id: 2, url: '/storage/images/product2.jpg' }
]);

const handleAddRow = (index) => {
  console.log('New upload slot added:', index);
};

const handleRemoveExisting = (index, image) => {
  // Remove from backend
  axios.delete(`/api/images/${image.id}`);
  // Remove from local array
  existingImages.value.splice(index, 1);
};
</script>
```

### 2. ImageUploadField

A simplified wrapper around SpartanImageUpload for easier usage.

#### Usage Example

```vue
<template>
  <ImageUploadField
    label="Auction Images"
    help-text="Upload auction images"
    field-name="auction_media[]"
    :max-count="10"
    :existing-images="auctionImages"
    @files-changed="handleFilesChanged"
    @remove-existing="removeExistingImage"
  />
</template>

<script setup>
import { ImageUploadField } from '@/components/forms';

const auctionImages = ref([]);

const handleFilesChanged = (fileCount) => {
  console.log('File count changed:', fileCount);
};

const removeExistingImage = async (index, image) => {
  try {
    await axios.delete(`/api/media/${image.id}`);
    auctionImages.value.splice(index, 1);
  } catch (error) {
    console.error('Failed to delete image:', error);
  }
};
</script>
```

### 3. Enhanced FormFileUpload

The existing FormFileUpload component now supports Spartan picker mode.

#### Usage Example

```vue
<template>
  <FormFileUpload
    v-model="files"
    label="Item Images"
    :use-spartan-picker="true"
    field-name="item_media[]"
    :max-files="8"
    :max-size="5000000"
    accept="image/*"
    @spartan-add="handleSpartanAdd"
  />
</template>

<script setup>
import { FormFileUpload } from '@/components/forms';

const files = ref([]);

const handleSpartanAdd = (index) => {
  console.log('Spartan picker added row:', index);
};
</script>
```

## Integration Examples

### Laravel Blade Integration

For use in Laravel Blade templates with Vue 3:

```blade
<!-- In your Blade template -->
<div id="image-upload-app">
  <image-upload-field
    label="Product Images"
    field-name="product_images[]"
    :max-count="5"
    :existing-images="@json($existingImages ?? [])"
    @remove-existing="removeExistingImage"
  ></image-upload-field>
</div>

@push('scripts')
<script>
const { createApp } = Vue;

createApp({
  components: {
    ImageUploadField: window.ImageUploadField
  },
  methods: {
    removeExistingImage(index, image) {
      // Handle existing image removal
      axios.delete(`/admin/media/${image.id}`)
        .then(() => {
          console.log('Image deleted successfully');
        })
        .catch(error => {
          console.error('Failed to delete image:', error);
        });
    }
  }
}).mount('#image-upload-app');
</script>
@endpush
```

### Form Submission

When using these components in forms, the spartanMultiImagePicker automatically creates file inputs with the specified field name. The form data will include the uploaded files.

```javascript
// Form submission example
const form = document.getElementById('your-form');
const formData = new FormData(form);

// The uploaded files will be available as:
// formData.get('media[]') or formData.getAll('media[]')

fetch('/api/upload', {
  method: 'POST',
  body: formData
});
```

## Styling

The components use Tailwind CSS classes and are designed to match the modernized admin UI theme. You can customize the appearance by:

1. Overriding CSS classes
2. Passing custom `spartanOptions`
3. Modifying the component styles

## Dependencies

- jQuery (required for spartanMultiImagePicker)
- spartanMultiImagePicker plugin
- Vue 3
- Tailwind CSS
- Heroicons (for icons)

## Best Practices

1. **Always specify field names** to ensure proper form submission
2. **Set appropriate file size limits** to prevent server issues
3. **Handle existing images** properly in edit modes
4. **Provide clear help text** for user guidance
5. **Implement proper error handling** for file operations
6. **Use consistent styling** across your application

## Troubleshooting

### Common Issues

1. **jQuery not available**: Ensure jQuery is loaded before the component
2. **Spartan picker not initializing**: Check console for errors and ensure the plugin is loaded
3. **File uploads not working**: Verify field names and form submission handling
4. **Styling issues**: Ensure Tailwind CSS is properly configured

### Debug Mode

You can enable debug logging by checking the browser console for component-specific messages.
