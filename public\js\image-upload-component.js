/**
 * Simple Image Upload Component - Vanilla JavaScript
 * A reusable component for handling image uploads with drag & drop, validation, and previews
 */

class ImageUploadComponent {
    constructor(options = {}) {
        this.options = {
            containerId: options.containerId || 'image-upload-container',
            inputName: options.inputName || 'media[]',
            maxFiles: options.maxFiles || 10,
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
            allowedTypes: options.allowedTypes || ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
            allowedExtensions: options.allowedExtensions || ['jpg', 'jpeg', 'png', 'gif'],
            label: options.label || 'Upload Images',
            helpText: options.helpText || 'PNG, JPG, GIF up to 10MB each',
            showCount: options.showCount !== false,
            existingImages: options.existingImages || [],
            onFilesChanged: options.onFilesChanged || null,
            onError: options.onError || null,
            onRemoveExisting: options.onRemoveExisting || null
        };
        
        this.selectedFiles = [];
        this.existingImages = [...this.options.existingImages];
        this.init();
    }
    
    init() {
        this.createHTML();
        this.bindEvents();
        this.updateCount();
    }
    
    createHTML() {
        const container = document.getElementById(this.options.containerId);
        if (!container) {
            console.error(`Container with ID "${this.options.containerId}" not found`);
            return;
        }
        
        container.innerHTML = `
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <label class="block text-sm font-medium text-gray-700">
                        ${this.options.label}
                    </label>
                    ${this.options.showCount ? `<span id="${this.options.containerId}-count" class="text-sm text-gray-500">0/${this.options.maxFiles} files</span>` : ''}
                </div>
                
                <!-- Existing Images -->
                <div id="${this.options.containerId}-existing" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 ${this.existingImages.length === 0 ? 'hidden' : ''}">
                    ${this.existingImages.map((image, index) => this.createExistingImageHTML(image, index)).join('')}
                </div>
                
                <!-- Upload Area -->
                <div id="${this.options.containerId}-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200 cursor-pointer">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="mt-4">
                        <span class="mt-2 block text-sm font-medium text-gray-900">
                            Click to upload or drag and drop
                        </span>
                        <span class="mt-1 block text-sm text-gray-500">
                            ${this.options.helpText}
                        </span>
                    </div>
                    <input id="${this.options.containerId}-input" name="${this.options.inputName}" type="file" multiple accept="${this.options.allowedTypes.join(',')}" class="sr-only">
                </div>
                
                <!-- File Previews -->
                <div id="${this.options.containerId}-previews" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 hidden"></div>
            </div>
        `;
    }
    
    createExistingImageHTML(image, index) {
        return `
            <div class="relative group" data-existing-index="${index}">
                <img src="${image.thumb_url || image.url}" alt="${image.name || 'Existing image'}" class="w-full h-24 object-cover rounded-lg border border-gray-200">
                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                    <button type="button" class="text-white hover:text-red-300 transition-colors duration-200 remove-existing-btn" data-index="${index}">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        const uploadArea = document.getElementById(`${this.options.containerId}-upload-area`);
        const fileInput = document.getElementById(`${this.options.containerId}-input`);
        
        if (!uploadArea || !fileInput) return;
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // File input change
        fileInput.addEventListener('change', (e) => this.handleFileSelection(e.target.files));
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Remove existing images
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-existing-btn')) {
                const index = parseInt(e.target.closest('.remove-existing-btn').dataset.index);
                this.removeExistingImage(index);
            }
        });
    }
    
    handleDragOver(e) {
        e.preventDefault();
        const uploadArea = document.getElementById(`${this.options.containerId}-upload-area`);
        uploadArea.classList.add('border-primary-400', 'bg-primary-50');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        const uploadArea = document.getElementById(`${this.options.containerId}-upload-area`);
        uploadArea.classList.remove('border-primary-400', 'bg-primary-50');
    }
    
    handleDrop(e) {
        e.preventDefault();
        const uploadArea = document.getElementById(`${this.options.containerId}-upload-area`);
        uploadArea.classList.remove('border-primary-400', 'bg-primary-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelection(files);
        }
    }
    
    handleFileSelection(files) {
        const validFiles = [];
        const totalFiles = this.selectedFiles.length + this.existingImages.length;
        
        Array.from(files).forEach((file) => {
            // Check total file count
            if (totalFiles + validFiles.length >= this.options.maxFiles) {
                this.showError(`Maximum ${this.options.maxFiles} files allowed.`);
                return;
            }
            
            // Check file size
            if (file.size > this.options.maxFileSize) {
                this.showError(`File "${file.name}" is too large. Maximum ${this.formatFileSize(this.options.maxFileSize)} allowed.`);
                return;
            }
            
            // Check file type
            if (!this.options.allowedTypes.includes(file.type)) {
                const extensions = this.options.allowedExtensions.join(', ').toUpperCase();
                this.showError(`File "${file.name}" is not a valid image type. Only ${extensions} are allowed.`);
                return;
            }
            
            validFiles.push(file);
        });
        
        if (validFiles.length > 0) {
            this.selectedFiles.push(...validFiles);
            this.updatePreviews();
            this.updateCount();
            
            if (this.options.onFilesChanged) {
                this.options.onFilesChanged(this.selectedFiles.length);
            }
        }
    }
    
    updatePreviews() {
        const previewContainer = document.getElementById(`${this.options.containerId}-previews`);
        if (!previewContainer) return;
        
        previewContainer.innerHTML = '';
        
        if (this.selectedFiles.length > 0) {
            previewContainer.classList.remove('hidden');
            
            this.selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const div = document.createElement('div');
                    div.className = 'relative group';
                    div.innerHTML = `
                        <img src="${e.target.result}" alt="${file.name}" class="w-full h-24 object-cover rounded-lg border border-gray-200">
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                            <button type="button" class="text-white hover:text-red-300 transition-colors duration-200" onclick="window.imageUpload.removeFile(${index})">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    `;
                    previewContainer.appendChild(div);
                };
                reader.readAsDataURL(file);
            });
        } else {
            previewContainer.classList.add('hidden');
        }
    }
    
    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.updatePreviews();
        this.updateCount();
        
        if (this.options.onFilesChanged) {
            this.options.onFilesChanged(this.selectedFiles.length);
        }
    }
    
    removeExistingImage(index) {
        const image = this.existingImages[index];
        
        if (this.options.onRemoveExisting) {
            this.options.onRemoveExisting(index, image);
        }
        
        this.existingImages.splice(index, 1);
        
        // Remove from DOM
        const existingContainer = document.getElementById(`${this.options.containerId}-existing`);
        const imageElement = existingContainer.querySelector(`[data-existing-index="${index}"]`);
        if (imageElement) {
            imageElement.remove();
        }
        
        // Hide container if no existing images
        if (this.existingImages.length === 0) {
            existingContainer.classList.add('hidden');
        }
        
        this.updateCount();
    }
    
    updateCount() {
        if (!this.options.showCount) return;
        
        const countElement = document.getElementById(`${this.options.containerId}-count`);
        if (countElement) {
            const total = this.selectedFiles.length + this.existingImages.length;
            countElement.textContent = `${total}/${this.options.maxFiles} files`;
        }
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showError(message) {
        if (this.options.onError) {
            this.options.onError(message);
        } else if (typeof Notyf !== 'undefined') {
            const notyf = new Notyf({dismissible: true});
            notyf.error(message);
        } else {
            alert(message);
        }
    }
    
    getFiles() {
        return this.selectedFiles;
    }
    
    getExistingImages() {
        return this.existingImages;
    }
    
    getTotalCount() {
        return this.selectedFiles.length + this.existingImages.length;
    }
}
