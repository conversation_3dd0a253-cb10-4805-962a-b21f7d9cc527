<template>
  <div class="image-upload-field">
    <SpartanImageUpload
      :label="label"
      :help-text="helpText"
      :error="error"
      :field-name="fieldName"
      :max-count="maxCount"
      :max-file-size="maxFileSize"
      :allowed-ext="allowedExt"
      :disabled="disabled"
      :required="required"
      :existing-images="existingImages"
      :spartan-options="spartanOptions"
      @add-row="handleAddRow"
      @remove-row="handleRemoveRow"
      @rendered-preview="handleRenderedPreview"
      @extension-error="handleExtensionError"
      @size-error="handleSizeError"
      @remove-existing="handleRemoveExisting"
    />
  </div>
</template>

<script setup lang="ts">
import SpartanImageUpload from './SpartanImageUpload.vue';

interface ExistingImage {
  id?: string | number;
  url?: string;
  thumb_url?: string;
  name?: string;
}

interface Props {
  label?: string;
  helpText?: string;
  error?: string;
  fieldName?: string;
  maxCount?: number;
  maxFileSize?: number; // in KB
  allowedExt?: string;
  disabled?: boolean;
  required?: boolean;
  existingImages?: ExistingImage[];
  spartanOptions?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  fieldName: 'media[]',
  maxFileSize: 5000, // 5MB in KB
  allowedExt: 'png|jpg|jpeg|gif',
  disabled: false,
  required: false,
});

const emit = defineEmits<{
  'add-row': [index: number];
  'remove-row': [index: number];
  'rendered-preview': [index: number];
  'extension-error': [index: number, file: any];
  'size-error': [index: number, file: any];
  'remove-existing': [index: number, image: ExistingImage];
  'files-changed': [fileCount: number];
}>();

// Event handlers
const handleAddRow = (index: number) => {
  emit('add-row', index);
  emit('files-changed', index + 1);
};

const handleRemoveRow = (index: number) => {
  emit('remove-row', index);
  emit('files-changed', Math.max(0, index - 1));
};

const handleRenderedPreview = (index: number) => {
  emit('rendered-preview', index);
};

const handleExtensionError = (index: number, file: any) => {
  emit('extension-error', index, file);
};

const handleSizeError = (index: number, file: any) => {
  emit('size-error', index, file);
};

const handleRemoveExisting = (index: number, image: ExistingImage) => {
  emit('remove-existing', index, image);
};
</script>

<style scoped>
.image-upload-field {
  @apply w-full;
}
</style>
