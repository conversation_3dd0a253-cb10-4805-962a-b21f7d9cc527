@extends('layouts.modernized-admin')

@section('title', $auctionType->name . ' - Auction Listing - Vertigo AMS')

@section('page-title', $auctionType->name)
@section('page-subtitle', 'View and manage this auction listing and its items.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auction-listing.index') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Listings</span>
        <span class="lg:hidden">Back</span>
    </a>
    @can('update', $auctionType)
    <a href="{{ route('auction-listing.edit', $auctionType) }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        <span class="hidden lg:inline">Edit Listing</span>
        <span class="lg:hidden">Edit</span>
    </a>
    @endcan
</div>
@endsection

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Auction Information Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Auction Information</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Auction Name</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $auctionType->name ?? 'Unnamed Auction' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Type</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ ucfirst($auctionType->type ?? 'live') }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Start Date</label>
                        <p class="text-sm text-gray-900">{{ \Carbon\Carbon::parse($auctionType->date_from)->format('M d, Y g:i A') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">End Date</label>
                        <p class="text-sm text-gray-900">{{ \Carbon\Carbon::parse($auctionType->date_to)->format('M d, Y g:i A') }}</p>
                    </div>
                </div>
                
                @if($auctionType->description)
                <div>
                    <label class="block text-sm font-medium text-gray-600 mb-1">Description</label>
                    <p class="text-sm text-gray-700 leading-relaxed">{{ $auctionType->description }}</p>
                </div>
                @endif
            </div>
        </div>

        <!-- Auction Images Card -->
        @if($auctionType->getMedia('media')->count() > 0)
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Auction Images</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($auctionType->getMedia('media') as $file)
                    <div class="group relative">
                        <div class="aspect-square rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                            <img class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-200" 
                                 src="{{ $file->getUrl('image') }}" 
                                 alt="Auction Image"
                                 onclick="openImageModal('{{ $file->getUrl('image') }}')"
                                 style="cursor: pointer;">
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Items in this Auction -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Items in this Auction</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {{ $auctionType->items()->whereNull('closed_by')->count() }} active items
                    </span>
                </div>
            </div>
            
            @if($auctionType->items->count() > 0)
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    @foreach($auctionType->items as $item)
                    <div class="group bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-primary-300">
                        <a href="{{ route('items.show', $item) }}" class="block">
                            <div class="aspect-w-1 aspect-h-1">
                                @if($item->image)
                                    <img src="{{ $item->image }}" 
                                         alt="{{ $item->name }}" 
                                         class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200">
                                @else
                                    <div class="w-full h-32 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="p-4">
                                <h4 class="text-sm font-medium text-gray-900 truncate group-hover:text-primary-600 transition-colors duration-200">
                                    {{ $item->name ?? 'Unnamed Item' }}
                                </h4>
                                <p class="text-xs text-gray-500 mt-1">
                                    {{ $item->reference_number ?? 'No reference' }}
                                </p>
                                @if($item->closed_by)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">
                                    Closed
                                </span>
                                @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                    Active
                                </span>
                                @endif
                            </div>
                        </a>
                    </div>
                    @endforeach
                </div>
            </div>
            @else
            <div class="p-12 text-center">
                <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No items in this auction</h3>
                <p class="text-gray-500 mb-4">Add items to this auction to get started.</p>
                @can('create', App\Models\Item::class)
                <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" 
                   class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Item
                </a>
                @endcan
            </div>
            @endif
        </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Status Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Status</h3>
            </div>
            <div class="p-6">
                @php
                    $now = now();
                    $startDate = \Carbon\Carbon::parse($auctionType->date_from);
                    $endDate = \Carbon\Carbon::parse($auctionType->date_to);
                    
                    if ($now < $startDate) {
                        $status = 'upcoming';
                        $statusText = 'Upcoming';
                        $statusClass = 'bg-yellow-100 text-yellow-800';
                        $statusIcon = 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z';
                    } elseif ($now >= $startDate && $now <= $endDate) {
                        $status = 'active';
                        $statusText = 'Active';
                        $statusClass = 'bg-green-100 text-green-800';
                        $statusIcon = 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
                    } else {
                        $status = 'completed';
                        $statusText = 'Completed';
                        $statusClass = 'bg-gray-100 text-gray-800';
                        $statusIcon = 'M5 13l4 4L19 7';
                    }
                @endphp
                
                <div class="text-center">
                    <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium {{ $statusClass }} mb-4">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $statusIcon }}"></path>
                        </svg>
                        {{ $statusText }}
                    </div>
                    
                    @if($status === 'upcoming')
                    <p class="text-sm text-gray-600">
                        Starts {{ $startDate->diffForHumans() }}
                    </p>
                    @elseif($status === 'active')
                    <p class="text-sm text-gray-600">
                        Ends {{ $endDate->diffForHumans() }}
                    </p>
                    @else
                    <p class="text-sm text-gray-600">
                        Ended {{ $endDate->diffForHumans() }}
                    </p>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Stats Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Active Items</span>
                    <span class="text-lg font-semibold text-gray-900">{{ $auctionType->items()->whereNull('closed_by')->count() }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Total Items</span>
                    <span class="text-lg font-semibold text-gray-900">{{ $auctionType->items()->count() }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Total Bids</span>
                    <span class="text-lg font-semibold text-gray-900">{{ $auctionType->items()->withCount('auctions')->get()->sum('auctions_count') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Last Updated</span>
                    <span class="text-sm text-gray-900">{{ $auctionType->updated_at->format('M d, Y') }}</span>
                </div>
            </div>
        </div>
        
        <!-- Actions Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                @can('update', $auctionType)
                <a href="{{ route('auction-listing.edit', $auctionType) }}" class="block w-full text-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
                    Edit Auction
                </a>
                @endcan
                
                @can('create', App\Models\Item::class)
                <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" class="block w-full text-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium">
                    Add Item
                </a>
                @endcan
                
                <a href="{{ route('auctions.index', ['auction_type_id' => $auctionType->id]) }}" class="block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium">
                    View Bids
                </a>
                
                @can('delete', $auctionType)
                <form action="{{ route('auction-listing.destroy', $auctionType) }}" method="POST" 
                      onsubmit="return confirm('Are you sure you want to delete this auction listing? This will also remove all associated items from the auction.')">
                    @csrf @method('DELETE')
                    <button type="submit" class="block w-full text-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium">
                        Delete Auction
                    </button>
                </form>
                @endcan
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <img id="modalImage" src="" alt="Full size image" class="max-w-full max-h-full object-contain rounded-lg">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors duration-200">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Image modal functions
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside the image
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});

console.log('Auction listing show page loaded');
</script>
@endpush
