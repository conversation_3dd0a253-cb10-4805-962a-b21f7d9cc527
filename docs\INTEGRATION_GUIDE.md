# Image Upload Components Integration Guide

This guide shows how to integrate the new reusable image upload components into existing modernized admin pages.

## Quick Start

### 1. Replace Existing Upload Sections

Instead of manually implementing spartanMultiImagePicker, use our reusable components:

**Before (Old Way):**
```blade
<div id="multi_image_picker" class="row"></div>

@push('scripts')
<script>
$("#multi_image_picker").spartanMultiImagePicker({
    fieldName: 'media[]',
    allowedExt: 'png|jpg|jpeg|gif',
    maxFileSize: '5000'
});
</script>
@endpush
```

**After (New Way):**
```blade
<div id="image-upload-app">
    <image-upload-field
        label="Upload Images"
        field-name="media[]"
        :max-file-size="5000"
        help-text="Upload images. Max 5MB each."
    ></image-upload-field>
</div>

@push('scripts')
<script>
const { createApp } = Vue;
createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    }
}).mount('#image-upload-app');
</script>
@endpush
```

### 2. Integration Examples for Existing Pages

#### Auction Listing Create Page

Update `resources/views/admin/modernized-auction-listing-create.blade.php`:

```blade
<!-- Replace the existing file upload section -->
<div class="mb-6" id="auction-images-app">
    <image-upload-field
        label="Auction Images"
        field-name="media[]"
        :max-count="10"
        :max-file-size="5000"
        help-text="Upload auction images. Maximum 10 images, 5MB each."
        @files-changed="handleFilesChanged"
    ></image-upload-field>
</div>

@push('scripts')
<script>
// Add to existing script section
const auctionImagesApp = createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    },
    setup() {
        const handleFilesChanged = (count) => {
            console.log('Auction images count:', count);
        };

        return { handleFilesChanged };
    }
}).mount('#auction-images-app');
</script>
@endpush
```

#### Items Form (Edit Mode with Existing Images)

Update `resources/views/app/items/modernized-form-inputs.blade.php`:

```blade
<!-- Replace the existing upload button section -->
<div class="mb-6" id="item-images-app">
    <image-upload-field
        label="Item Images"
        field-name="media[]"
        :max-count="8"
        :max-file-size="5000"
        :existing-images="existingImages"
        help-text="Upload item images. Maximum 8 images, 5MB each."
        @remove-existing="removeExistingImage"
    ></image-upload-field>
</div>

@push('scripts')
<script>
const itemImagesApp = createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    },
    setup() {
        const existingImages = ref(@json($item->media ?? []));

        const removeExistingImage = async (index, image) => {
            try {
                await axios.delete(`/delete-media/${image.id}`);
                existingImages.value.splice(index, 1);
                
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.success('Image removed successfully');
                }
            } catch (error) {
                console.error('Failed to remove image:', error);
            }
        };

        return {
            existingImages,
            removeExistingImage
        };
    }
}).mount('#item-images-app');
</script>
@endpush
```

#### Auction Types Create/Edit

Update `resources/views/admin/modernized-auction-types-create.blade.php`:

```blade
<!-- Replace the existing file upload section -->
<div class="mb-6" id="auction-type-images-app">
    <image-upload-field
        label="Auction Type Images"
        field-name="media[]"
        :max-count="3"
        :max-file-size="5000"
        help-text="Upload images for this auction type. Maximum 3 images."
    ></image-upload-field>
</div>

@push('scripts')
<script>
// Add to existing Vue app or create new one
const auctionTypeImagesApp = createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    }
}).mount('#auction-type-images-app');
</script>
@endpush
```

### 3. Component Registration

Make sure the components are globally available. Add to your main layout or app file:

```blade
<!-- In resources/views/layouts/modernized-admin.blade.php -->
@push('scripts')
<script>
// Make components globally available
window.SpartanImageUpload = {
    template: `<spartan-image-upload v-bind="$attrs" v-on="$listeners"></spartan-image-upload>`
};

window.ImageUploadField = {
    template: `<image-upload-field v-bind="$attrs" v-on="$listeners"></image-upload-field>`
};
</script>
@endpush
```

### 4. Backend Integration

Update your controllers to handle the uploaded files:

```php
// In your controller
public function store(Request $request)
{
    $request->validate([
        'media.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
    ]);

    $item = Item::create($request->except('media'));

    if ($request->hasFile('media')) {
        foreach ($request->file('media') as $file) {
            $item->addMediaFromRequest('media')
                 ->each(function ($fileAdder) {
                     $fileAdder->toMediaCollection('images');
                 });
        }
    }

    return redirect()->route('items.index')
                   ->with('success', 'Item created successfully');
}
```

### 5. Styling Customization

Add custom styles if needed:

```css
/* Custom styles for image upload components */
.spartan-multi-image-picker-container .file_upload {
    border: 2px dashed #e5e7eb;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.spartan-multi-image-picker-container .file_upload:hover {
    border-color: #0068ff;
    background-color: #f0f9ff;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
    .spartan-multi-image-picker-container .col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}
```

## Migration Checklist

- [ ] Replace manual spartanMultiImagePicker initialization
- [ ] Update field names to match form requirements
- [ ] Add Vue app mounting for new components
- [ ] Update backend validation rules
- [ ] Test file upload functionality
- [ ] Verify existing image display in edit mode
- [ ] Test image removal functionality
- [ ] Check responsive behavior on mobile devices
- [ ] Verify error handling and notifications

## Benefits of Using These Components

1. **Consistency** - Standardized upload experience across all admin pages
2. **Maintainability** - Centralized component logic, easier to update
3. **Reusability** - Drop-in replacement for any image upload needs
4. **Error Handling** - Built-in validation and error messaging
5. **Responsive** - Mobile-friendly design out of the box
6. **Accessibility** - Proper labels and ARIA attributes
7. **Customizable** - Flexible props for different use cases

## Troubleshooting

### Common Issues

1. **Component not rendering**: Check Vue app mounting and component registration
2. **Files not uploading**: Verify field names match backend expectations
3. **Styling issues**: Ensure Tailwind CSS and custom styles are loaded
4. **jQuery errors**: Confirm jQuery and spartanMultiImagePicker are loaded

### Debug Tips

- Check browser console for JavaScript errors
- Verify network requests in browser dev tools
- Test with different file types and sizes
- Ensure proper CSRF tokens for form submissions
