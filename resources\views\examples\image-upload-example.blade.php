@extends('layouts.modernized-admin')

@section('title', 'Image Upload Components Example')

@section('content')
<div class="container-fluid px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Image Upload Components</h1>
        <p class="mt-2 text-gray-600">Examples of reusable image upload components using Spartan Multi Image Picker</p>
    </div>

    <!-- Example 1: Basic Image Upload -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Basic Image Upload</h2>
            <p class="text-sm text-gray-600">Simple image upload with default settings</p>
        </div>
        <div class="p-6">
            <div id="basic-upload-app">
                <spartan-image-upload
                    label="Product Images"
                    help-text="Upload product images. Max 5MB each."
                    field-name="basic_images[]"
                    :max-count="5"
                    :max-file-size="5000"
                    @add-row="handleAddRow"
                    @size-error="handleSizeError"
                ></spartan-image-upload>
            </div>
        </div>
    </div>

    <!-- Example 2: Upload with Existing Images -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Upload with Existing Images</h2>
            <p class="text-sm text-gray-600">Image upload component with existing images (edit mode)</p>
        </div>
        <div class="p-6">
            <div id="existing-upload-app">
                <image-upload-field
                    label="Auction Images"
                    help-text="Upload or manage auction images"
                    field-name="auction_images[]"
                    :max-count="8"
                    :existing-images="existingImages"
                    @remove-existing="removeExistingImage"
                    @files-changed="handleFilesChanged"
                ></image-upload-field>
            </div>
        </div>
    </div>

    <!-- Example 3: Custom Configuration -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Custom Configuration</h2>
            <p class="text-sm text-gray-600">Image upload with custom settings and styling</p>
        </div>
        <div class="p-6">
            <div id="custom-upload-app">
                <spartan-image-upload
                    label="Gallery Images"
                    help-text="Upload gallery images with custom settings"
                    field-name="gallery_images[]"
                    :max-count="12"
                    :max-file-size="10000"
                    allowed-ext="png|jpg|jpeg|gif|webp"
                    group-class-name="col-md-3 col-sm-4 col-xs-6"
                    row-height="250px"
                    drop-file-label="Drop your images here"
                    :spartan-options="customOptions"
                    @add-row="handleCustomAdd"
                ></spartan-image-upload>
            </div>
        </div>
    </div>

    <!-- Example 4: Form Integration -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Form Integration Example</h2>
            <p class="text-sm text-gray-600">Complete form with image upload component</p>
        </div>
        <div class="p-6">
            <form id="example-form" action="/admin/example-upload" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Text Fields -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <input type="text" id="title" name="title" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category" name="category" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics</option>
                            <option value="furniture">Furniture</option>
                            <option value="art">Art</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>

                <!-- Image Upload -->
                <div class="mt-6" id="form-upload-app">
                    <image-upload-field
                        label="Item Images"
                        help-text="Upload images for this item. Maximum 6 images, 5MB each."
                        field-name="item_images[]"
                        :max-count="6"
                        :max-file-size="5000"
                        @files-changed="updateFileCount"
                    ></image-upload-field>
                </div>

                <!-- Submit Button -->
                <div class="mt-8 flex justify-end">
                    <button type="submit" 
                            class="px-6 py-2 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200">
                        Save Item
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
const { createApp, ref } = Vue;

// Basic Upload App
createApp({
    components: {
        SpartanImageUpload: window.SpartanImageUpload
    },
    setup() {
        const handleAddRow = (index) => {
            console.log('Basic upload - Added row:', index);
        };

        const handleSizeError = (index, file) => {
            console.log('Basic upload - Size error:', file);
        };

        return {
            handleAddRow,
            handleSizeError
        };
    }
}).mount('#basic-upload-app');

// Existing Images Upload App
createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    },
    setup() {
        const existingImages = ref([
            { id: 1, url: '/storage/demo/image1.jpg', name: 'Sample Image 1' },
            { id: 2, url: '/storage/demo/image2.jpg', name: 'Sample Image 2' },
            { id: 3, url: '/storage/demo/image3.jpg', name: 'Sample Image 3' }
        ]);

        const removeExistingImage = async (index, image) => {
            try {
                // Simulate API call
                console.log('Removing image:', image);
                existingImages.value.splice(index, 1);
                
                // Show success notification
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.success('Image removed successfully');
                }
            } catch (error) {
                console.error('Failed to remove image:', error);
            }
        };

        const handleFilesChanged = (fileCount) => {
            console.log('Files changed, new count:', fileCount);
        };

        return {
            existingImages,
            removeExistingImage,
            handleFilesChanged
        };
    }
}).mount('#existing-upload-app');

// Custom Upload App
createApp({
    components: {
        SpartanImageUpload: window.SpartanImageUpload
    },
    setup() {
        const customOptions = ref({
            placeholderImage: {
                image: '/images/custom-upload-icon.svg',
                width: '80px'
            }
        });

        const handleCustomAdd = (index) => {
            console.log('Custom upload - Added row:', index);
        };

        return {
            customOptions,
            handleCustomAdd
        };
    }
}).mount('#custom-upload-app');

// Form Integration App
createApp({
    components: {
        ImageUploadField: window.ImageUploadField
    },
    setup() {
        const fileCount = ref(0);

        const updateFileCount = (count) => {
            fileCount.value = count;
            console.log('Form upload - File count:', count);
        };

        return {
            fileCount,
            updateFileCount
        };
    }
}).mount('#form-upload-app');

// Form submission handler
document.getElementById('example-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Log form data for demonstration
    console.log('Form submission data:');
    for (let [key, value] of formData.entries()) {
        console.log(key, value);
    }
    
    // Show success message
    if (typeof Notyf !== 'undefined') {
        const notyf = new Notyf({dismissible: true});
        notyf.success('Form submitted successfully (demo mode)');
    }
});
</script>
@endpush

@push('styles')
<style>
/* Custom styles for the examples */
.spartan-multi-image-picker-container .spartan_item_wrapper {
    margin-bottom: 1rem;
}

.spartan-multi-image-picker-container .file_upload {
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.spartan-multi-image-picker-container .file_upload:hover {
    border-color: #6b7280;
    background-color: #f9fafb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .spartan-multi-image-picker-container .col-md-4,
    .spartan-multi-image-picker-container .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (max-width: 576px) {
    .spartan-multi-image-picker-container .col-md-4,
    .spartan-multi-image-picker-container .col-md-3,
    .spartan-multi-image-picker-container .col-sm-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
</style>
@endpush
