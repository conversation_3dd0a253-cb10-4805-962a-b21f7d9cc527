<?php $editing = isset($item) ?>
<?php $images = ( $editing ) ?  $item->getMedia('media') : [] ?>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Left Column - Basic Information -->
    <div class="space-y-6">
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Basic Information
            </h4>
            
            <!-- Item Name -->
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Item Name *</label>
                <input type="text" name="name" id="name" 
                       value="<?php echo e(old('name', ($editing ? $item->name : ''))); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                       placeholder="Enter item name" required>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Reference Number -->
            <div class="mb-4">
                <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">Reference Number *</label>
                <input type="text" name="reference_number" id="reference_number" 
                       value="<?php echo e(old('reference_number', ($editing ? $item->reference_number : ''))); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                       placeholder="Reference number" required>
                <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Status -->
            <div class="mb-4">
                <label for="status_id" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                <select name="status_id" id="status_id" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200" required>
                    <?php $selected = old('status_id', ($editing ? $item->status_id : '')) ?>
                    <option value="" disabled <?php echo e(empty($selected) ? 'selected' : ''); ?>>Please select status</option>
                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value); ?>" <?php echo e($selected == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['status_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Supplier -->
            <?php if(!isset($supplier)): ?>
            <div class="mb-4">
                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Supplier *</label>
                <select name="user_id" id="user_id" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200" required>
                    <?php $selected = old('user_id', ($editing ? $item->user_id : '')) ?>
                    <option value="" disabled <?php echo e(empty($selected) ? 'selected' : ''); ?>>Please select supplier</option>
                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value); ?>" <?php echo e($selected == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <?php else: ?>
            <input type="hidden" name="user_id" value="<?php echo e($supplier->id); ?>">
            <?php endif; ?>
        </div>

        <!-- Auction Details -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Auction Details
            </h4>

            <!-- Auction Type -->
            <div class="mb-4">
                <label for="auction_type_id" class="block text-sm font-medium text-gray-700 mb-2">Auction Category *</label>
                <select name="auction_type_id" id="auction_type_id" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200" required>
                    <?php $selected = old('auction_type_id', ($editing ? $item->auction_type_id : '')) ?>
                    <option value="" disabled <?php echo e(empty($selected) ? 'selected' : ''); ?>>Please select category</option>
                    <?php $__currentLoopData = $auctionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auctionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($auctionType->id); ?>" <?php echo e($selected == $auctionType->id ? 'selected' : ''); ?>>
                        <?php echo e($auctionType->name); ?> (<?php echo e($auctionType->type); ?>)
                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['auction_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Target Price -->
            <div class="mb-4">
                <label for="target_amount" class="block text-sm font-medium text-gray-700 mb-2">Target Price (MK) *</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 sm:text-sm">MK</span>
                    </div>
                    <input type="number" name="target_amount" id="target_amount" 
                           value="<?php echo e(old('target_amount', ($editing ? $item->target_amount : ''))); ?>"
                           class="w-full pl-12 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                           placeholder="0.00" step="0.01" min="0" required>
                </div>
                <?php $__errorArgs = ['target_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>

    <!-- Right Column - Dates and Description -->
    <div class="space-y-6">
        <!-- Auction Dates -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="h-4 w-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z"></path>
                </svg>
                Auction Schedule
            </h4>

            <!-- Date From -->
            <div class="mb-4">
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">Start Date & Time *</label>
                <input type="datetime-local" name="date_from" id="date_from" 
                       value="<?php echo e(old('date_from', $editing ? ($item->date_from ? $item->date_from->format('Y-m-d\TH:i') : '') : now()->format('Y-m-d\TH:i'))); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200" required>
                <?php $__errorArgs = ['date_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Date To -->
            <div class="mb-4">
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">End Date & Time *</label>
                <input type="datetime-local" name="date_to" id="date_to" 
                       value="<?php echo e(old('date_to', $editing ? ($item->date_to ? $item->date_to->format('Y-m-d\TH:i') : '') : now()->addDays(30)->format('Y-m-d\TH:i'))); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200" required>
                <?php $__errorArgs = ['date_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Description -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center">
                <svg class="h-4 w-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                </svg>
                Description
            </h4>

            <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Item Description</label>
                <textarea name="description" id="description" rows="6" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                          placeholder="Describe the item in detail, including condition, features, and any relevant information..."><?php echo e(old('description', ($editing ? $item->description : ''))); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Image Upload -->
        <div class="bg-gray-50 rounded-lg p-4" id="item-images-app">
            <?php
                $existingImages = $editing && $images->count() > 0 ? $images->map(function($image) {
                    return [
                        'id' => $image->id,
                        'url' => $image->getUrl(),
                        'thumb_url' => $image->getUrl('thumb'),
                        'name' => $image->name
                    ];
                })->toArray() : [];
            ?>

            <image-upload-field
                label="Item Images"
                help-text="Upload item images. Maximum 8 images, 10MB each."
                field-name="media[]"
                :max-count="8"
                :max-file-size="10000"
                allowed-ext="png|jpg|jpeg|gif"
                :existing-images="<?php echo json_encode($existingImages, 15, 512) ?>"
                @files-changed="handleFilesChanged"
                @add-row="handleAddRow"
                @size-error="handleSizeError"
                @extension-error="handleExtensionError"
                @remove-existing="removeExistingImage"
            ></image-upload-field>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Vue app for image upload
    const { createApp } = Vue;

    createApp({
        components: {
            ImageUploadField: window.ImageUploadField
        },
        setup() {
            const handleFilesChanged = (count) => {
                console.log('Item images count:', count);
            };

            const handleAddRow = (index) => {
                console.log('Added image upload row:', index);
            };

            const handleSizeError = (index, file) => {
                console.log('File size error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('File size too large. Maximum 10MB allowed.');
                }
            };

            const handleExtensionError = (index, file) => {
                console.log('File extension error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('Invalid file type. Only PNG, JPG, JPEG, and GIF are allowed.');
                }
            };

            const removeExistingImage = async (index, image) => {
                try {
                    const response = await fetch(`/delete-media/${image.id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Content-Type': 'application/json',
                        },
                    });

                    if (response.ok) {
                        if (typeof Notyf !== 'undefined') {
                            const notyf = new Notyf({dismissible: true});
                            notyf.success('Image removed successfully');
                        }
                    } else {
                        throw new Error('Failed to delete image');
                    }
                } catch (error) {
                    console.error('Failed to remove image:', error);
                    if (typeof Notyf !== 'undefined') {
                        const notyf = new Notyf({dismissible: true});
                        notyf.error('Failed to remove image. Please try again.');
                    }
                }
            };

            return {
                handleFilesChanged,
                handleAddRow,
                handleSizeError,
                handleExtensionError,
                removeExistingImage
            };
        }
    }).mount('#item-images-app');

    // Auto-calculate end date when start date changes
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');

    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', function() {
            if (this.value && !dateToInput.value) {
                const startDate = new Date(this.value);
                const endDate = new Date(startDate.getTime() + (30 * 24 * 60 * 60 * 1000)); // Add 30 days
                dateToInput.value = endDate.toISOString().slice(0, 16);
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/modernized-form-inputs.blade.php ENDPATH**/ ?>